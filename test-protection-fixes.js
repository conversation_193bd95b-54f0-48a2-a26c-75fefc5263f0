const axios = require('axios');

// Configuration
const BACKEND_URL = 'http://localhost:3000';
const TEST_USER_ADDRESS = '0x164D435789d02dbE8317f48017D61663C1CE369B';

async function testProtectionCasesFix() {
  console.log('🧪 Testing Protection Cases Fix...\n');

  try {
    console.log('📋 Testing /api/protection/my-cases endpoint...');
    const response = await axios.get(`${BACKEND_URL}/api/protection/my-cases`, {
      headers: {
        'X-User-Address': TEST_USER_ADDRESS
      }
    });

    console.log('✅ Response Status:', response.status);
    console.log('✅ Response Data:', JSON.stringify(response.data, null, 2));

    if (response.data.success) {
      const cases = response.data.data;
      console.log(`\n🎉 Success! Found ${cases.length} protection cases for user`);
      console.log('✅ No more "Invalid case ID" error!');
    } else {
      console.log('❌ Request failed:', response.data.message);
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

async function testPatentSearchFix() {
  console.log('\n🔍 Testing Patent Search Fix...\n');

  try {
    // Test 1: Search with single character (should work now)
    console.log('📋 Testing search with single character "2"...');
    const response1 = await axios.get(`${BACKEND_URL}/api/patents/search`, {
      params: {
        name: '2'
      }
    });

    console.log('✅ Single character search - Status:', response1.status);
    console.log('✅ Single character search - Data:', JSON.stringify(response1.data, null, 2));

    // Test 2: Search with number "2"
    console.log('\n📋 Testing search with number "2"...');
    const response2 = await axios.get(`${BACKEND_URL}/api/patents/search`, {
      params: {
        number: '2'
      }
    });

    console.log('✅ Number search - Status:', response2.status);
    console.log('✅ Number search - Data:', JSON.stringify(response2.data, null, 2));

    console.log('\n🎉 Success! No more "Validation failed" error!');

  } catch (error) {
    console.error('❌ Search Error:', error.response?.data || error.message);
  }
}

async function runTests() {
  console.log('🚀 Running Protection View Fix Tests...\n');
  
  await testProtectionCasesFix();
  await testPatentSearchFix();
  
  console.log('\n✅ All tests completed!');
}

runTests().catch(console.error); 