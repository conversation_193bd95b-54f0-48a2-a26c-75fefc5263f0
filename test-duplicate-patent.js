const axios = require('axios');
const FormData = require('form-data');

async function testDuplicatePatentNumber() {
  try {
    console.log('🔍 Testing duplicate patent number handling...');
    
    // Create test form data with a known existing patent number
    const formData = new FormData();
    
    // Use a patent number that we know exists (from our previous successful upload)
    formData.append('patentName', '测试专利名称 - 重复测试');
    formData.append('patentNumber', 'CN748669644266.5'); // This should already exist
    formData.append('patentCategory', 'invention');
    formData.append('transferPrice', '1.5');
    formData.append('patentAbstract', '这是一个测试专利的摘要。这个摘要包含了足够的字符数来满足最少50个字符的要求。专利摘要应该详细描述专利的技术内容和创新点，以便审核人员能够理解专利的核心价值。');
    formData.append('applicationDate', '2023-01-15');
    formData.append('expirationDate', '2043-01-15');
    formData.append('ownerName', '测试用户');
    formData.append('ownerIdNumber', '110101199001011234');
    formData.append('isAgentSale', 'false');
    formData.append('uploaderAddress', '******************************************');
    
    // Create dummy files for testing
    const testFileContent = Buffer.from('This is a test file content for patent document');
    formData.append('patentDocument', testFileContent, {
      filename: 'test-patent.pdf',
      contentType: 'application/pdf'
    });
    
    formData.append('ownershipDocument', testFileContent, {
      filename: 'test-ownership.pdf',
      contentType: 'application/pdf'
    });
    
    console.log('📋 Testing with existing patent number: CN748669644266.5');
    
    // Test the upload endpoint
    const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
      headers: {
        ...formData.getHeaders(),
        'X-User-Address': '******************************************'
      },
      timeout: 30000
    });
    
    console.log('❌ Expected error but got success:', response.data);
    
  } catch (error) {
    console.log('✅ Got expected error:');
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error code:', error.response.data?.error?.code);
      console.log('Error message:', error.response.data?.error?.message);
      
      // Check if we got the expected validation error
      if (error.response.data?.error?.code === 'VALIDATION_ERROR' && 
          error.response.data?.error?.message?.includes('专利号已存在')) {
        console.log('🎉 Perfect! Got the expected duplicate patent number error');
      } else {
        console.log('⚠️ Got an error but not the expected one');
        console.log('Full response:', JSON.stringify(error.response.data, null, 2));
      }
    } else {
      console.error('No response received:', error.message);
    }
  }
}

async function main() {
  console.log('🚀 Starting duplicate patent number test...\n');
  
  // Wait for backend to be ready
  console.log('⏳ Waiting for backend to be ready...');
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  try {
    // Test basic connectivity
    const healthCheck = await axios.get('http://localhost:3000/api/user/role/******************************************');
    console.log('✅ Backend is responding\n');
  } catch (error) {
    console.error('❌ Backend is not responding:', error.message);
    console.log('Please make sure the backend server is running on port 3000');
    return;
  }
  
  await testDuplicatePatentNumber();
  
  console.log('\n✅ Test completed');
}

main().catch(console.error); 