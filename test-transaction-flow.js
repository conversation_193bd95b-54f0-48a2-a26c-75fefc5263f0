#!/usr/bin/env node

/**
 * Test script to verify the patent purchase flow fix
 * This script tests the complete transaction workflow:
 * 1. User initiates purchase → status: pending
 * 2. Reviewer approves → status: approved (NOT completed)
 * 3. Reviewer completes → status: completed + ownership transfer
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// Test configuration
const TEST_CONFIG = {
  // Using actual addresses from Ganache setup
  buyerAddress: '******************************************', // Account 1 (buyer)
  sellerAddress: '******************************************', // Patent owner (seller)
  reviewerAddress: '******************************************', // Account 2 (reviewer)
  patentId: '6', // Using available patent ID 6
  price: '2' // Using the actual price from the patent
};

// Helper function to make API calls
async function apiCall(method, endpoint, data = null, userAddress = null) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (userAddress) {
      config.headers['X-User-Address'] = userAddress;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ API call failed: ${method} ${endpoint}`);
    console.error('Error:', error.response?.data || error.message);
    throw error;
  }
}

// Test functions
async function testInitiateTransaction() {
  console.log('\n🚀 Step 1: Initiating transaction...');

  const transactionData = {
    patentId: TEST_CONFIG.patentId,
    buyerAddress: TEST_CONFIG.buyerAddress,
    sellerAddress: TEST_CONFIG.sellerAddress,
    price: TEST_CONFIG.price
  };

  const result = await apiCall('POST', '/transactions/initiate', transactionData, TEST_CONFIG.buyerAddress);
  console.log('✅ Transaction initiated:', result);

  return result.data?.transactionId || result.transactionId;
}

async function testGetPendingTransactions() {
  console.log('\n🔍 Step 2: Checking pending transactions...');

  const result = await apiCall('GET', '/transactions/pending', null, TEST_CONFIG.reviewerAddress);
  console.log('✅ Pending transactions:', result);

  return result.data?.data || result.data || result;
}

async function testApproveTransaction(transactionId) {
  console.log('\n✅ Step 3: Approving transaction...');

  const approvalData = {
    reviewerAddress: TEST_CONFIG.reviewerAddress,
    comments: 'Transaction approved for testing'
  };

  const result = await apiCall('PUT', `/transactions/${transactionId}/approve`, approvalData, TEST_CONFIG.reviewerAddress);
  console.log('✅ Transaction approved:', result);

  return result;
}

async function testGetUserTransactions(userAddress) {
  console.log('\n📋 Step 4: Checking user transactions...');

  const result = await apiCall('GET', `/transactions/user/${userAddress}`, null, userAddress);
  console.log('✅ User transactions:', result);

  return result.data?.transactions || result.transactions || [];
}

async function testCompleteTransaction(transactionId) {
  console.log('\n🏁 Step 5: Completing transaction...');

  const result = await apiCall('PUT', `/transactions/${transactionId}/complete`, {}, TEST_CONFIG.reviewerAddress);
  console.log('✅ Transaction completed:', result);

  return result;
}

// Main test function
async function runTransactionFlowTest() {
  console.log('🧪 Starting Patent Purchase Flow Test');
  console.log('=====================================');

  try {
    // Step 1: Initiate transaction
    const transactionId = await testInitiateTransaction();
    if (!transactionId) {
      throw new Error('Failed to get transaction ID');
    }

    // Step 2: Check pending transactions
    const pendingTransactions = await testGetPendingTransactions();
    const ourTransaction = pendingTransactions.find(t => t.id === transactionId.toString());
    if (!ourTransaction) {
      throw new Error('Transaction not found in pending list');
    }
    console.log('✅ Transaction found in pending list with status:', ourTransaction.status);

    // Step 3: Approve transaction
    await testApproveTransaction(transactionId);

    // Step 4: Check user transactions to verify status is "approved" (not completed)
    const userTransactions = await testGetUserTransactions(TEST_CONFIG.buyerAddress);
    const approvedTransaction = userTransactions.find(t => t.id === transactionId.toString());
    if (!approvedTransaction) {
      throw new Error('Transaction not found in user transactions');
    }
    console.log('✅ Transaction status after approval:', approvedTransaction.status);

    if (approvedTransaction.status !== 'approved') {
      throw new Error(`Expected status 'approved', got '${approvedTransaction.status}'`);
    }

    // Step 5: Complete transaction
    await testCompleteTransaction(transactionId);

    // Step 6: Verify final status is "completed"
    const finalUserTransactions = await testGetUserTransactions(TEST_CONFIG.buyerAddress);
    const completedTransaction = finalUserTransactions.find(t => t.id === transactionId.toString());
    if (!completedTransaction) {
      throw new Error('Transaction not found in final user transactions');
    }
    console.log('✅ Final transaction status:', completedTransaction.status);

    if (completedTransaction.status !== 'completed') {
      throw new Error(`Expected final status 'completed', got '${completedTransaction.status}'`);
    }

    console.log('\n🎉 All tests passed! Transaction flow is working correctly.');
    console.log('=====================================');
    console.log('✅ Purchase initiated → pending');
    console.log('✅ Reviewer approved → approved (not completed)');
    console.log('✅ Reviewer completed → completed + ownership transfer');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('=====================================');
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  runTransactionFlowTest();
}

module.exports = {
  runTransactionFlowTest,
  testInitiateTransaction,
  testGetPendingTransactions,
  testApproveTransaction,
  testGetUserTransactions,
  testCompleteTransaction
};
