#!/usr/bin/env node

/**
 * Test script to verify the frontend data parsing fix
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';
const REVIEWER_ADDRESS = '0xa19515E50EA5c913e9ddB5476E4a59E097606727';

// Simulate the frontend data parsing logic
function simulateFrontendParsing(response) {
  console.log('🧪 Simulating Frontend Data Parsing');
  console.log('===================================');
  
  // Old logic (broken)
  const oldResult = response.data.data;
  console.log('❌ Old logic (response.data.data):', {
    type: typeof oldResult,
    isArray: Array.isArray(oldResult),
    length: Array.isArray(oldResult) ? oldResult.length : 'N/A'
  });
  
  // New logic (fixed)
  const newResult = response.data.data.data || response.data.data || [];
  console.log('✅ New logic (response.data.data.data || response.data.data || []):', {
    type: typeof newResult,
    isArray: Array.isArray(newResult),
    length: Array.isArray(newResult) ? newResult.length : 'N/A'
  });
  
  return newResult;
}

async function testFrontendFix() {
  console.log('🧪 Testing Frontend Data Parsing Fix');
  console.log('====================================');
  
  try {
    // Make the same API call the frontend makes
    const response = await axios.get(`${API_BASE_URL}/transactions/pending`, {
      headers: {
        'X-User-Address': REVIEWER_ADDRESS,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('📊 API Response Status:', response.status);
    console.log('📊 API Response Structure:', {
      success: response.data.success,
      hasData: !!response.data.data,
      dataType: typeof response.data.data,
      dataKeys: response.data.data ? Object.keys(response.data.data) : 'none'
    });
    
    // Simulate frontend parsing
    const transactions = simulateFrontendParsing(response);
    
    console.log('\n🎯 Final Result:');
    if (Array.isArray(transactions) && transactions.length > 0) {
      console.log(`✅ SUCCESS: Frontend should now display ${transactions.length} transactions`);
      console.log('✅ First transaction:', {
        id: transactions[0].id,
        patentName: transactions[0].patentName,
        buyerName: transactions[0].buyerName,
        sellerName: transactions[0].sellerName,
        status: transactions[0].status
      });
    } else {
      console.log('❌ FAILED: Frontend would still show no transactions');
    }
    
    console.log('\n📋 Instructions:');
    console.log('1. Refresh the frontend application (http://localhost:5173)');
    console.log('2. Make sure you\'re connected with reviewer account:', REVIEWER_ADDRESS);
    console.log('3. Navigate to Review → Trading Review');
    console.log('4. You should now see the pending transactions');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
if (require.main === module) {
  testFrontendFix();
}

module.exports = { testFrontendFix, simulateFrontendParsing };
