const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

/**
 * Test script to replicate the exact frontend patent upload request
 * This helps identify the specific validation error occurring in the frontend
 */

async function testFrontendUpload() {
  console.log('🔍 Testing frontend-style patent upload...');

  try {
    // Create FormData exactly like the frontend does
    const formData = new FormData();

    // Add patent information with empty/invalid values to trigger validation
    formData.append('patentName', ''); // Empty name to trigger validation
    formData.append('patentNumber', 'CN202410001234.5');
    formData.append('patentCategory', 'invention');
    formData.append('transferPrice', '1.5');
    formData.append('patentAbstract', '这是一个测试专利的摘要。这个摘要包含了足够的字符数来满足最少50个字符的要求。');
    formData.append('applicationDate', '2023-01-15');
    formData.append('expirationDate', '2043-01-15');
    formData.append('ownerName', '测试用户');
    formData.append('ownerIdNumber', '110101199001011234');
    formData.append('isAgentSale', 'false'); // String value like frontend should send
    formData.append('uploaderAddress', '******************************************');

    // Add dummy files
    const testFileContent = Buffer.from('Test file content');
    formData.append('patentDocument', testFileContent, {
      filename: 'test-patent.pdf',
      contentType: 'application/pdf'
    });
    formData.append('ownershipDocument', testFileContent, {
      filename: 'test-ownership.pdf',
      contentType: 'application/pdf'
    });

    console.log('📋 Form data prepared (with empty patent name to trigger validation)');

    // Make request exactly like frontend API client
    const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
      headers: {
        ...formData.getHeaders(),
        'X-User-Address': '******************************************'
      },
      timeout: 30000
    });

    console.log('❌ Expected validation error but got success:', response.data);

  } catch (error) {
    if (error.response) {
      console.log('✅ Got expected validation error:');
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);

      // Test with valid data to see if it works
      await testWithValidData();
    } else {
      console.error('❌ Network or other error:', error.message);
    }
  }
}

async function testWithValidData() {
  console.log('\n🔍 Testing with valid data...');

  try {
    const formData = new FormData();

    // Add valid patent information
    formData.append('patentName', '测试专利名称 - 这是一个用于测试的专利名称');
    formData.append('patentNumber', `CN${Date.now().toString().slice(-12)}.5`);
    formData.append('patentCategory', 'invention');
    formData.append('transferPrice', '1.5');
    formData.append('patentAbstract', '这是一个测试专利的摘要。这个摘要包含了足够的字符数来满足最少50个字符的要求。专利摘要应该详细描述专利的技术内容和创新点。');
    formData.append('applicationDate', '2023-01-15');
    formData.append('expirationDate', '2043-01-15');
    formData.append('ownerName', '测试用户');
    formData.append('ownerIdNumber', '110101199001011234');
    formData.append('isAgentSale', 'false'); // String value like frontend should send
    formData.append('uploaderAddress', '******************************************');

    // Add dummy files
    const testFileContent = Buffer.from('Test file content');
    formData.append('patentDocument', testFileContent, {
      filename: 'test-patent.pdf',
      contentType: 'application/pdf'
    });
    formData.append('ownershipDocument', testFileContent, {
      filename: 'test-ownership.pdf',
      contentType: 'application/pdf'
    });

    console.log('📋 Valid form data prepared');

    const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
      headers: {
        ...formData.getHeaders(),
        'X-User-Address': '******************************************'
      },
      timeout: 30000
    });

    console.log('✅ Upload successful with valid data:', response.data);

  } catch (error) {
    if (error.response) {
      console.log('❌ Unexpected error with valid data:');
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    } else {
      console.error('❌ Network or other error:', error.message);
    }
  }
}

async function testSpecificUserData() {
  console.log('\n🔍 Testing with specific user data that might be causing the issue...');

  try {
    const formData = new FormData();

    // Test with potentially problematic data from the user's actual form
    formData.append('patentName', ''); // Empty - this should trigger validation
    formData.append('patentNumber', '');
    formData.append('patentCategory', '');
    formData.append('transferPrice', '');
    formData.append('patentAbstract', '');
    formData.append('applicationDate', '');
    formData.append('expirationDate', '');
    formData.append('ownerName', '');
    formData.append('ownerIdNumber', '');
    formData.append('isAgentSale', 'false');
    formData.append('uploaderAddress', '******************************************'); // User's actual address

    // Add empty files to see if that's the issue
    formData.append('patentDocument', Buffer.from(''), {
      filename: '',
      contentType: 'application/octet-stream'
    });
    formData.append('ownershipDocument', Buffer.from(''), {
      filename: '',
      contentType: 'application/octet-stream'
    });

    console.log('📋 Empty form data prepared (should trigger multiple validation errors)');

    const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
      headers: {
        ...formData.getHeaders(),
        'X-User-Address': '******************************************'
      },
      timeout: 30000
    });

    console.log('❌ Expected validation error but got success:', response.data);

  } catch (error) {
    if (error.response) {
      console.log('✅ Got expected validation error with empty data:');
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    } else {
      console.error('❌ Network or other error:', error.message);
    }
  }
}

// Run the tests
async function runTests() {
  console.log('🚀 Starting frontend upload replication tests...\n');

  await testFrontendUpload();
  await testSpecificUserData();

  console.log('\n✅ Tests completed');
}

runTests().catch(console.error);
