#!/usr/bin/env node

/**
 * Debug script to check the exact API response structure
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';
const REVIEWER_ADDRESS = '0xa19515E50EA5c913e9ddB5476E4a59E097606727';

async function debugAPIResponse() {
  console.log('🔍 Debugging API Response Structure');
  console.log('===================================');
  
  try {
    console.log('Making request to:', `${API_BASE_URL}/transactions/pending`);
    console.log('With headers:', {
      'X-User-Address': REVIEWER_ADDRESS,
      'Content-Type': 'application/json'
    });
    
    const response = await axios.get(`${API_BASE_URL}/transactions/pending`, {
      headers: {
        'X-User-Address': REVIEWER_ADDRESS,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('\n📊 Full Response:');
    console.log('Status:', response.status);
    console.log('Headers:', response.headers);
    console.log('\n📋 Response Data Structure:');
    console.log('typeof response.data:', typeof response.data);
    console.log('response.data keys:', Object.keys(response.data));
    
    console.log('\n🔍 Detailed Response Data:');
    console.log(JSON.stringify(response.data, null, 2));
    
    // Test different access paths
    console.log('\n🧪 Testing Access Paths:');
    console.log('response.data:', !!response.data);
    console.log('response.data.data:', !!response.data.data);
    console.log('response.data.data.data:', !!response.data.data?.data);
    
    // Check what the frontend should use
    const transactions1 = response.data.data;
    const transactions2 = response.data.data?.data;
    
    console.log('\n📈 Transaction Arrays:');
    console.log('response.data.data type:', typeof transactions1);
    console.log('response.data.data.data type:', typeof transactions2);
    
    if (Array.isArray(transactions1)) {
      console.log('✅ response.data.data is array with', transactions1.length, 'items');
      if (transactions1.length > 0) {
        console.log('First transaction keys:', Object.keys(transactions1[0]));
      }
    } else {
      console.log('❌ response.data.data is not an array:', transactions1);
    }
    
    if (Array.isArray(transactions2)) {
      console.log('✅ response.data.data.data is array with', transactions2.length, 'items');
      if (transactions2.length > 0) {
        console.log('First transaction keys:', Object.keys(transactions2[0]));
      }
    } else {
      console.log('❌ response.data.data.data is not an array:', transactions2);
    }
    
    console.log('\n🎯 Conclusion:');
    if (Array.isArray(transactions1)) {
      console.log('✅ Frontend should use: response.data.data');
    } else if (Array.isArray(transactions2)) {
      console.log('✅ Frontend should use: response.data.data.data');
    } else {
      console.log('❌ Neither path returns an array - there\'s a structure issue');
    }
    
  } catch (error) {
    console.error('❌ API call failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the debug
if (require.main === module) {
  debugAPIResponse();
}

module.exports = { debugAPIResponse };
