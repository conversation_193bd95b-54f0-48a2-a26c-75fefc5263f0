const axios = require('axios');

async function checkUserStatus() {
  try {
    const userAddress = '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252';
    
    console.log('🔍 Checking user status for:', userAddress);
    
    // Check user registration status
    console.log('\n📋 Checking user registration...');
    const registrationResponse = await axios.get(`http://localhost:3000/api/user/profile/${userAddress}`);
    console.log('Registration response:', registrationResponse.data);
    
    // Check user role
    console.log('\n👤 Checking user role...');
    const roleResponse = await axios.get(`http://localhost:3000/api/user/role/${userAddress}`);
    console.log('Role response:', roleResponse.data);
    
    // Check user permissions
    console.log('\n🔐 Checking user permissions...');
    try {
      const permissionsResponse = await axios.get(`http://localhost:3000/api/user/permissions/${userAddress}`);
      console.log('Permissions response:', permissionsResponse.data);
    } catch (error) {
      console.log('Permissions check failed:', error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('❌ Error checking user status:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

async function registerUserIfNeeded() {
  try {
    const userAddress = '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252';
    
    console.log('\n🔧 Attempting to register user...');
    
    const registrationData = {
      address: userAddress,
      name: '测试用户 4',
      phone: '13800138004',
      idNumber: '110101199001011234'
    };
    
    const response = await axios.post('http://localhost:3000/api/user/register', registrationData);
    console.log('✅ Registration successful:', response.data);
    
  } catch (error) {
    console.error('❌ Registration failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

async function main() {
  console.log('🚀 Starting user status check...\n');
  
  // Wait for backend to be ready
  console.log('⏳ Waiting for backend to be ready...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await checkUserStatus();
  
  console.log('\n' + '='.repeat(50));
  console.log('Attempting registration if needed...');
  
  await registerUserIfNeeded();
  
  console.log('\n' + '='.repeat(50));
  console.log('Checking status again after registration...');
  
  await checkUserStatus();
  
  console.log('\n✅ User status check completed');
}

main().catch(console.error); 