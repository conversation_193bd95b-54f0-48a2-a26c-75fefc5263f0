const axios = require('axios');

// Configuration
const BACKEND_URL = 'http://localhost:3000';
const REVIEWER_ADDRESS = '0x164D435789d02dbE8317f48017D61663C1CE369B';

async function testCompleteWorkflow() {
  console.log('🔬 Testing Complete Review Workflow...\n');

  try {
    // 1. Test Review Statistics
    console.log('📊 Step 1: Testing Review Statistics...');
    const statsResponse = await axios.get(`${BACKEND_URL}/api/review/statistics`, {
      headers: { 'X-User-Address': REVIEWER_ADDRESS }
    });

    if (statsResponse.data.success) {
      const stats = statsResponse.data.data;
      console.log(`✅ Statistics: ${stats.patents.pending} pending, ${stats.patents.approved} approved, ${stats.patents.rejected} rejected`);
    }

    // 2. Test Pending Uploads
    console.log('\n📋 Step 2: Testing Pending Uploads...');
    const pendingResponse = await axios.get(`${BACKEND_URL}/api/review/uploads/pending`, {
      headers: { 'X-User-Address': REVIEWER_ADDRESS }
    });

    if (pendingResponse.data.success) {
      const uploads = pendingResponse.data.data.data;
      console.log(`✅ Found ${uploads.length} pending uploads`);
      
      uploads.forEach((upload, index) => {
        console.log(`   ${index + 1}. ${upload.patentName} (ID: ${upload.id})`);
        console.log(`      - Number: ${upload.patentNumber}`);
        console.log(`      - Category: ${upload.category}`);
        console.log(`      - Price: ${upload.price} ETH`);
        console.log(`      - Owner: ${upload.ownerName}`);
        console.log(`      - Uploader: ${upload.uploaderName} (${upload.uploaderAddressShort})`);
        console.log(`      - Submit Date: ${upload.submitDate}`);
      });

      if (uploads.length > 0) {
        // 3. Test Patent Details for first upload
        const firstUpload = uploads[0];
        console.log(`\n🔍 Step 3: Testing Patent Details for ID ${firstUpload.id}...`);
        
        const detailsResponse = await axios.get(`${BACKEND_URL}/api/patents/${firstUpload.id}`, {
          headers: { 'X-User-Address': REVIEWER_ADDRESS }
        });

        if (detailsResponse.data.success) {
          const patent = detailsResponse.data.data.data || detailsResponse.data.data;
          console.log(`✅ Patent details loaded successfully`);
          console.log(`   Name: ${patent.name}`);
          console.log(`   Status: ${patent.status}`);
          console.log(`   Abstract: ${patent.abstract.substring(0, 100)}...`);
        }
      }
    }

    console.log('\n🎉 Complete workflow test successful!');
    console.log('\n📋 Frontend Verification Checklist:');
    console.log('   ✅ Patent data should display correctly (name, number, category, price, etc.)');
    console.log('   ✅ Statistics should show real counts instead of mock data');
    console.log('   ✅ "View Details" button should navigate to /patent/:id');
    console.log('   ✅ "Approve" and "Reject" modals should open without Bootstrap errors');
    console.log('   ✅ Statistics should update after approve/reject actions');

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testCompleteWorkflow(); 