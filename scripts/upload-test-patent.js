const { Web3 } = require('web3');
const fs = require('fs');
const path = require('path');

// Initialize Web3
const web3 = new Web3('http://127.0.0.1:7545');

// Load contract addresses
const contractAddresses = JSON.parse(fs.readFileSync(path.join(__dirname, '../backend/contract-addresses.json'), 'utf8'));

// Load PatentRegistry contract
const patentRegistryABI = JSON.parse(fs.readFileSync(path.join(__dirname, '../backend/build/contracts/PatentRegistry.json'), 'utf8')).abi;
const patentRegistry = new web3.eth.Contract(patentRegistryABI, contractAddresses.PatentRegistry);

async function uploadTestPatent() {
  try {
    console.log('📄 Uploading test patent for review interface testing...\n');

    // Get accounts
    const accounts = await web3.eth.getAccounts();
    const userAccount = accounts[1]; // Regular user

    console.log(`👤 Uploading from: ${userAccount}\n`);

    // Patent data
    const patentData = {
      name: 'AI-Powered Blockchain Patent Management System',
      number: `CN${Date.now()}`,
      category: '计算机软件',
      price: web3.utils.toWei('15', 'ether'),
      abstractText: 'A revolutionary patent management system that combines artificial intelligence with blockchain technology to provide secure, transparent, and efficient patent registration, review, and trading capabilities.',
      applicationDate: Math.floor(Date.now() / 1000) - 86400, // Yesterday
      expirationDate: Math.floor(Date.now() / 1000) + (20 * 365 * 24 * 60 * 60), // 20 years from now
      ownerName: 'Dr. Zhang Wei',
      ownerIdNumber: '110101199001011234',
      isAgentSale: false,
      documentHash: 'QmAIBlockchainPatentDocumentHash123456789',
      ownershipDocumentHash: 'QmAIBlockchainOwnershipDocumentHash123456789'
    };

    console.log('📋 Patent Information:');
    console.log(`   Name: ${patentData.name}`);
    console.log(`   Number: ${patentData.number}`);
    console.log(`   Category: ${patentData.category}`);
    console.log(`   Price: ${web3.utils.fromWei(patentData.price, 'ether')} ETH`);
    console.log(`   Owner: ${patentData.ownerName}`);
    console.log(`   Owner ID: ${patentData.ownerIdNumber}`);
    console.log(`   Agent Sale: ${patentData.isAgentSale ? 'Yes' : 'No'}\n`);

    const uploadResult = await patentRegistry.methods.uploadPatent(
      patentData.name,
      patentData.number,
      patentData.category,
      patentData.price,
      patentData.abstractText,
      patentData.applicationDate,
      patentData.expirationDate,
      patentData.ownerName,
      patentData.ownerIdNumber,
      patentData.isAgentSale,
      patentData.documentHash,
      patentData.ownershipDocumentHash
    ).send({ from: userAccount, gas: 3000000 });

    const patentId = uploadResult.events.PatentUploaded.returnValues.patentId;
    console.log(`✅ Patent uploaded successfully!`);
    console.log(`   Patent ID: ${patentId}`);
    console.log(`   Transaction Hash: ${uploadResult.transactionHash}`);
    console.log(`   Gas Used: ${uploadResult.gasUsed}\n`);

    // Verify it's in pending list
    const pendingPatents = await patentRegistry.methods.getPendingPatents().call();
    console.log(`📋 Current pending patents: ${pendingPatents.length}`);
    
    if (pendingPatents.includes(patentId)) {
      console.log(`✅ Patent ${patentId} is now in the pending review queue!`);
    } else {
      console.log(`❌ Patent ${patentId} not found in pending queue!`);
    }

    console.log('\n🎉 Test patent upload completed!');
    console.log('💡 You can now test the review interface in the frontend.');

  } catch (error) {
    console.error('❌ Upload failed:', error);
  }
}

// Run the upload
uploadTestPatent().catch(console.error);
