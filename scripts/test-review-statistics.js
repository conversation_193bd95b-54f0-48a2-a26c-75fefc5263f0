const axios = require('axios');

// Configuration
const BACKEND_URL = 'http://localhost:3000';
const REVIEWER_ADDRESS = '0x164D435789d02dbE8317f48017D61663C1CE369B'; // Using same address as reviewer

async function testReviewStatistics() {
  console.log('📊 Testing Review Statistics Endpoint...\n');

  try {
    // Test the new review statistics endpoint
    console.log('📋 Getting review statistics...');
    const statsResponse = await axios.get(`${BACKEND_URL}/api/review/statistics`, {
      headers: {
        'X-User-Address': REVIEWER_ADDRESS
      }
    });

    console.log('✅ Review statistics response:', JSON.stringify(statsResponse.data, null, 2));

    if (statsResponse.data.success) {
      const stats = statsResponse.data.data;
      console.log('\n📊 Statistics Summary:');
      console.log(`   Patents - Pending: ${stats.patents.pending}, Approved: ${stats.patents.approved}, Rejected: ${stats.patents.rejected}`);
      console.log(`   Transactions - Pending: ${stats.transactions.pending}, Approved: ${stats.transactions.approved}, Rejected: ${stats.transactions.rejected}`);
      console.log(`   Protection - Pending: ${stats.protection.pending}, Approved: ${stats.protection.approved}, Rejected: ${stats.protection.rejected}`);
      console.log(`   Overall - Pending: ${stats.summary.totalPending}, Approved: ${stats.summary.totalApproved}, Rejected: ${stats.summary.totalRejected}`);
    }

    console.log('\n🎉 Review statistics test completed successfully!');

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

// Test pending uploads endpoint as well
async function testPendingUploads() {
  console.log('\n📋 Testing Pending Uploads Endpoint...\n');

  try {
    const pendingResponse = await axios.get(`${BACKEND_URL}/api/review/uploads/pending`, {
      headers: {
        'X-User-Address': REVIEWER_ADDRESS
      }
    });

    console.log('✅ Pending uploads response:', JSON.stringify(pendingResponse.data, null, 2));

    if (pendingResponse.data.success) {
      const uploads = pendingResponse.data.data.data; // Handle nested data structure
      console.log(`\n📊 Found ${uploads.length} pending uploads:`);
      uploads.forEach((upload, index) => {
        console.log(`   ${index + 1}. ${upload.patentName} (ID: ${upload.id}) - ${upload.category}`);
      });
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

async function runTests() {
  await testReviewStatistics();
  await testPendingUploads();
}

runTests(); 