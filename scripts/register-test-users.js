const { Web3 } = require('web3');
const fs = require('fs');
const path = require('path');

// Initialize Web3
const web3 = new Web3('http://127.0.0.1:7545');

// Load contract addresses
const contractAddresses = JSON.parse(fs.readFileSync(path.join(__dirname, '../backend/contract-addresses.json'), 'utf8'));

// Load UserManagement contract
const userManagementABI = JSON.parse(fs.readFileSync(path.join(__dirname, '../backend/build/contracts/UserManagement.json'), 'utf8')).abi;
const userManagement = new web3.eth.Contract(userManagementABI, contractAddresses.UserManagement);

async function registerTestUsers() {
  try {
    console.log('👥 Registering test users...\n');

    // Get accounts
    const accounts = await web3.eth.getAccounts();
    const userAccount = accounts[1]; // Regular user
    const reviewerAccount = accounts[2]; // Reviewer
    const adminAccount = accounts[0]; // Admin

    console.log('Accounts:');
    console.log(`   User: ${userAccount}`);
    console.log(`   Reviewer: ${reviewerAccount}`);
    console.log(`   Admin: ${adminAccount}\n`);

    // Register regular user
    console.log('📝 Registering regular user...');
    try {
      const isUserRegistered = await userManagement.methods.registeredUsers(userAccount).call();
      if (!isUserRegistered) {
        await userManagement.methods.registerUser(
          userAccount,
          'Test User',
          '***********',
          'ID123456789'
        ).send({ from: userAccount, gas: 1000000 });
        console.log('✅ Regular user registered successfully');
      } else {
        console.log('✅ Regular user already registered');
      }
    } catch (error) {
      console.error('❌ Failed to register regular user:', error.message);
    }

    // Register reviewer
    console.log('📝 Registering reviewer...');
    try {
      const isReviewerRegistered = await userManagement.methods.registeredUsers(reviewerAccount).call();
      if (!isReviewerRegistered) {
        await userManagement.methods.registerUser(
          reviewerAccount,
          'Test Reviewer',
          '***********',
          'ID987654321'
        ).send({ from: reviewerAccount, gas: 1000000 });
        console.log('✅ Reviewer registered successfully');
      } else {
        console.log('✅ Reviewer already registered');
      }
    } catch (error) {
      console.error('❌ Failed to register reviewer:', error.message);
    }

    // Set reviewer role
    console.log('🔧 Setting reviewer role...');
    try {
      const currentRole = await userManagement.methods.getUserRole(reviewerAccount).call();
      console.log(`   Current role: ${currentRole}`);

      if (parseInt(currentRole) !== 1) { // 1 = REVIEWER
        await userManagement.methods.changeUserRole(reviewerAccount, 1).send({ from: adminAccount, gas: 1000000 });
        console.log('✅ Reviewer role set successfully');
      } else {
        console.log('✅ Reviewer role already set');
      }
    } catch (error) {
      console.error('❌ Failed to set reviewer role:', error.message);
    }

    // Register admin (admin is already registered in constructor, but let's check)
    console.log('📝 Checking admin registration...');
    try {
      const isAdminRegistered = await userManagement.methods.registeredUsers(adminAccount).call();
      if (isAdminRegistered) {
        console.log('✅ Admin already registered');
      } else {
        await userManagement.methods.registerUser(
          adminAccount,
          'Test Admin',
          '***********',
          'ID000000000'
        ).send({ from: adminAccount, gas: 1000000 });
        console.log('✅ Admin registered successfully');
      }
    } catch (error) {
      console.error('❌ Failed to register admin:', error.message);
    }

    // Set admin role (should already be set in constructor)
    console.log('🔧 Checking admin role...');
    try {
      const currentRole = await userManagement.methods.getUserRole(adminAccount).call();
      console.log(`   Current role: ${currentRole}`);

      if (parseInt(currentRole) !== 2) { // 2 = ADMIN
        await userManagement.methods.changeUserRole(adminAccount, 2).send({ from: adminAccount, gas: 1000000 });
        console.log('✅ Admin role set successfully');
      } else {
        console.log('✅ Admin role already set');
      }
    } catch (error) {
      console.error('❌ Failed to set admin role:', error.message);
    }

    console.log('\n🎉 User registration completed!\n');

    // Verify roles
    console.log('🔍 Verifying user roles:');
    try {
      const userRole = await userManagement.methods.getUserRole(userAccount).call();
      const reviewerRole = await userManagement.methods.getUserRole(reviewerAccount).call();
      const adminRole = await userManagement.methods.getUserRole(adminAccount).call();

      console.log(`   User role: ${userRole} (0=USER, 1=REVIEWER, 2=ADMIN)`);
      console.log(`   Reviewer role: ${reviewerRole} (0=USER, 1=REVIEWER, 2=ADMIN)`);
      console.log(`   Admin role: ${adminRole} (0=USER, 1=REVIEWER, 2=ADMIN)`);
    } catch (error) {
      console.error('❌ Failed to verify roles:', error.message);
    }

  } catch (error) {
    console.error('❌ Registration failed:', error);
  }
}

// Run the registration
registerTestUsers().catch(console.error);
