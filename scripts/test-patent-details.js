const axios = require('axios');

// Configuration
const BACKEND_URL = 'http://localhost:3000';
const USER_ADDRESS = '0x164D435789d02dbE8317f48017D61663C1CE369B'; // Sample user address

async function testPatentDetails() {
  console.log('🔍 Testing Patent Details Endpoint...\n');

  try {
    // First get the list of pending uploads to find a valid patent ID
    console.log('📋 Getting pending uploads to find a patent ID...');
    const pendingResponse = await axios.get(`${BACKEND_URL}/api/review/uploads/pending`, {
      headers: {
        'X-User-Address': USER_ADDRESS
      }
    });

    if (pendingResponse.data.success && pendingResponse.data.data.data.length > 0) {
      const firstPatent = pendingResponse.data.data.data[0];
      const patentId = firstPatent.id;
      
      console.log(`✅ Found patent ID: ${patentId} - ${firstPatent.patentName}`);
      
      // Test the patent details endpoint
      console.log(`\n🔍 Testing patent details for ID: ${patentId}...`);
      const detailsResponse = await axios.get(`${BACKEND_URL}/api/patents/${patentId}`, {
        headers: {
          'X-User-Address': USER_ADDRESS
        }
      });

      console.log('✅ Patent details response:', JSON.stringify(detailsResponse.data, null, 2));

      if (detailsResponse.data.success) {
        const patent = detailsResponse.data.data.data || detailsResponse.data.data;
        console.log('\n📄 Patent Details Summary:');
        console.log(`   ID: ${patent.id}`);
        console.log(`   Name: ${patent.name}`);
        console.log(`   Number: ${patent.number}`);
        console.log(`   Category: ${patent.category}`);
        console.log(`   Price: ${patent.price} ETH`);
        console.log(`   Status: ${patent.status}`);
        console.log(`   Owner: ${patent.ownerName}`);
        console.log(`   Uploader: ${patent.uploaderName}`);
      }

      console.log('\n🎉 Patent details test completed successfully!');

    } else {
      console.log('❌ No pending patents found to test');
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testPatentDetails(); 