const { Web3 } = require('web3');
const fs = require('fs');
const path = require('path');

// Ganache 配置
const GANACHE_URL = 'http://127.0.0.1:7545';

// 创建 Web3 实例
const web3 = new Web3(GANACHE_URL);

// 预定义的用户信息
const userProfiles = [
  {
    name: '系统管理员',
    phone: '13800000001',
    idNumber: '110101199001010001',
    role: 2 // admin
  },
  {
    name: '审核员张三',
    phone: '13800000002', 
    idNumber: '110101199001010002',
    role: 1 // reviewer
  },
  {
    name: '审核员李四',
    phone: '13800000003',
    idNumber: '110101199001010003', 
    role: 1 // reviewer
  },
  {
    name: '用户王五',
    phone: '13800000004',
    idNumber: '110101199001010004',
    role: 0 // user
  },
  {
    name: '用户赵六',
    phone: '13800000005',
    idNumber: '110101199001010005',
    role: 0 // user
  },
  {
    name: '用户孙七',
    phone: '13800000006',
    idNumber: '110101199001010006',
    role: 0 // user
  },
  {
    name: '用户周八',
    phone: '13800000007',
    idNumber: '110101199001010007',
    role: 0 // user
  },
  {
    name: '用户吴九',
    phone: '13800000008',
    idNumber: '110101199001010008',
    role: 0 // user
  },
  {
    name: '用户郑十',
    phone: '13800000009',
    idNumber: '110101199001010009',
    role: 0 // user
  },
  {
    name: '测试用户',
    phone: '13800000010',
    idNumber: '110101199001010010',
    role: 0 // user
  }
];

async function loadContractAddresses() {
  try {
    const addressesPath = path.join(__dirname, '../backend/contract-addresses.json');
    if (fs.existsSync(addressesPath)) {
      const addressesData = fs.readFileSync(addressesPath, 'utf8');
      return JSON.parse(addressesData);
    } else {
      throw new Error('Contract addresses file not found. Please deploy contracts first.');
    }
  } catch (error) {
    console.error('❌ Failed to load contract addresses:', error);
    throw error;
  }
}

async function loadContract(contractName, contractAddresses) {
  try {
    // Load ABI
    const abiPath = path.join(__dirname, `../backend/build/contracts/${contractName}.json`);
    if (!fs.existsSync(abiPath)) {
      throw new Error(`ABI file not found for ${contractName}. Please compile contracts first.`);
    }

    const contractData = JSON.parse(fs.readFileSync(abiPath, 'utf8'));
    const abi = contractData.abi;
    
    // Get contract address
    const address = contractAddresses[contractName];
    if (!address) {
      throw new Error(`Address not found for ${contractName}. Please deploy contracts first.`);
    }

    // Create contract instance
    return new web3.eth.Contract(abi, address);
  } catch (error) {
    console.error(`❌ Failed to load ${contractName} contract:`, error);
    throw error;
  }
}

async function registerGanacheUsers() {
  console.log('🚀 Ganache 用户批量注册工具');
  console.log('============================\n');
  
  try {
    // 1. 测试连接
    console.log('1. 连接到 Ganache...');
    const isConnected = await web3.eth.net.isListening();
    if (!isConnected) {
      throw new Error('无法连接到 Ganache 节点');
    }
    console.log('✅ 成功连接到 Ganache 节点\n');

    // 2. 获取账户
    console.log('2. 获取 Ganache 账户...');
    const accounts = await web3.eth.getAccounts();
    console.log(`✅ 找到 ${accounts.length} 个账户\n`);

    // 3. 加载合约
    console.log('3. 加载智能合约...');
    const contractAddresses = await loadContractAddresses();
    const userManagementContract = await loadContract('UserManagement', contractAddresses);
    console.log('✅ UserManagement 合约加载成功\n');

    // 4. 检查已注册用户
    console.log('4. 检查已注册用户...');
    const registeredUsers = [];
    for (let i = 0; i < accounts.length; i++) {
      try {
        const isRegistered = await userManagementContract.methods.registeredUsers(accounts[i]).call();
        if (isRegistered) {
          registeredUsers.push(accounts[i]);
          console.log(`   账户 ${i}: ${accounts[i]} - 已注册`);
        } else {
          console.log(`   账户 ${i}: ${accounts[i]} - 未注册`);
        }
      } catch (error) {
        console.log(`   账户 ${i}: ${accounts[i]} - 检查失败: ${error.message}`);
      }
    }
    console.log(`✅ 已注册用户: ${registeredUsers.length} 个\n`);

    // 5. 注册未注册的用户
    console.log('5. 开始批量注册用户...');
    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;

    for (let i = 0; i < Math.min(accounts.length, userProfiles.length); i++) {
      const account = accounts[i];
      const profile = userProfiles[i];
      
      try {
        // 检查是否已注册
        const isRegistered = await userManagementContract.methods.registeredUsers(account).call();
        
        if (isRegistered) {
          console.log(`   跳过账户 ${i}: ${account} - 已注册`);
          skipCount++;
          continue;
        }

        console.log(`   注册账户 ${i}: ${account} - ${profile.name}`);
        
        // 注册用户
        const gasEstimate = await userManagementContract.methods
          .registerUser(account, profile.name, profile.phone, profile.idNumber)
          .estimateGas({ from: account });
        
        const gasPrice = await web3.eth.getGasPrice();
        
        const receipt = await userManagementContract.methods
          .registerUser(account, profile.name, profile.phone, profile.idNumber)
          .send({
            from: account,
            gas: gasEstimate,
            gasPrice: gasPrice
          });

        console.log(`   ✅ 注册成功 - 交易哈希: ${receipt.transactionHash}`);

        // 如果不是普通用户，设置角色
        if (profile.role !== 0) {
          console.log(`   设置角色为: ${profile.role === 2 ? 'admin' : 'reviewer'}`);
          
          const roleGasEstimate = await userManagementContract.methods
            .changeUserRole(account, profile.role)
            .estimateGas({ from: accounts[0] }); // 使用第一个账户（应该是owner）
          
          const roleReceipt = await userManagementContract.methods
            .changeUserRole(account, profile.role)
            .send({
              from: accounts[0],
              gas: roleGasEstimate,
              gasPrice: gasPrice
            });

          console.log(`   ✅ 角色设置成功 - 交易哈希: ${roleReceipt.transactionHash}`);
        }

        successCount++;
        
        // 添加延迟避免过快的交易
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        console.error(`   ❌ 注册失败: ${error.message}`);
        errorCount++;
      }
    }

    console.log('\n📊 注册结果统计:');
    console.log(`   ✅ 成功注册: ${successCount} 个用户`);
    console.log(`   ⏭️  跳过注册: ${skipCount} 个用户`);
    console.log(`   ❌ 注册失败: ${errorCount} 个用户`);

    // 6. 验证注册结果
    console.log('\n6. 验证注册结果...');
    for (let i = 0; i < Math.min(accounts.length, userProfiles.length); i++) {
      const account = accounts[i];
      try {
        const isRegistered = await userManagementContract.methods.registeredUsers(account).call();
        const isActive = await userManagementContract.methods.isUserActive(account).call();
        const role = await userManagementContract.methods.getUserRole(account).call();
        
        const roleNames = ['user', 'reviewer', 'admin'];
        console.log(`   账户 ${i}: ${account}`);
        console.log(`     注册状态: ${isRegistered ? '✅ 已注册' : '❌ 未注册'}`);
        console.log(`     激活状态: ${isActive ? '✅ 已激活' : '❌ 未激活'}`);
        console.log(`     用户角色: ${roleNames[role] || 'unknown'}`);
      } catch (error) {
        console.log(`   账户 ${i}: ${account} - 验证失败: ${error.message}`);
      }
    }

    console.log('\n🎉 用户注册完成！');
    console.log('\n💡 现在您可以使用 MetaMask 连接这些账户进行测试：');
    for (let i = 0; i < Math.min(accounts.length, userProfiles.length); i++) {
      const profile = userProfiles[i];
      const roleNames = ['普通用户', '审核员', '管理员'];
      console.log(`   账户 ${i}: ${profile.name} (${roleNames[profile.role]})`);
    }

  } catch (error) {
    console.error('\n❌ 批量注册失败:');
    console.error(`   错误类型: ${error.name}`);
    console.error(`   错误信息: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 建议检查:');
      console.error('   1. Ganache 是否正在运行');
      console.error('   2. 智能合约是否已部署');
      console.error('   3. contract-addresses.json 文件是否存在');
    }
    
    process.exit(1);
  }
}

// 优雅关闭处理
process.on('SIGINT', () => {
  console.log('\n\n👋 注册被中断，正在退出...');
  process.exit(0);
});

// 未捕获异常处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 调用函数进行注册
registerGanacheUsers(); 