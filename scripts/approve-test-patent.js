const axios = require('axios');

// Configuration
const BACKEND_URL = 'http://localhost:3000';
const REVIEWER_ADDRESS = '0x164D435789d02dbE8317f48017D61663C1CE369B'; // Using same address as reviewer

async function approveTestPatent() {
  console.log('🧪 Approving a test patent for action testing...\n');

  try {
    // Get pending uploads for review
    console.log('📋 Getting pending uploads...');
    const pendingResponse = await axios.get(`${BACKEND_URL}/api/review/uploads/pending`, {
      headers: {
        'X-User-Address': REVIEWER_ADDRESS
      }
    });

    console.log('✅ Pending uploads response:', JSON.stringify(pendingResponse.data, null, 2));

    if (!pendingResponse.data.success) {
      console.log('❌ Failed to get pending uploads');
      return;
    }

    // Handle nested data structure
    const pendingUploads = pendingResponse.data.data.data || pendingResponse.data.data;

    if (!pendingUploads || pendingUploads.length === 0) {
      console.log('❌ No pending uploads found');
      return;
    }
    const firstUpload = pendingUploads[0];

    console.log(`\n🎯 Approving patent: ${firstUpload.name} (ID: ${firstUpload.id})`);

    // Approve the first pending upload
    const approveResponse = await axios.put(`${BACKEND_URL}/api/review/uploads/${firstUpload.id}/approve`, {
      comments: 'Approved for testing patent actions'
    }, {
      headers: {
        'X-User-Address': REVIEWER_ADDRESS
      }
    });

    console.log('✅ Approve response:', approveResponse.data);

    if (approveResponse.data.success) {
      console.log('🎉 Patent approved successfully!');
      console.log('💡 Now you can test withdraw/freeze actions on this patent');
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

// Run the approval
approveTestPatent().catch(console.error);
