const axios = require('axios');

// Configuration
const BACKEND_URL = 'http://localhost:3000';
const TEST_USER_ADDRESS = '0x164D435789d02dbE8317f48017D61663C1CE369B';

async function testUserPatents() {
  console.log('🔍 Testing User Patents Endpoint...\n');

  try {
    console.log('📋 Getting user patents...');
    const response = await axios.get(`${BACKEND_URL}/api/patents/user/${TEST_USER_ADDRESS}`, {
      headers: {
        'X-User-Address': TEST_USER_ADDRESS
      }
    });

    console.log('✅ Response Status:', response.status);
    console.log('✅ Response Data Structure:');
    console.log(JSON.stringify(response.data, null, 2));

    if (response.data.success) {
      const patents = response.data.data;
      console.log(`\n🎉 Found ${patents.length} patents for user`);
      
      if (patents.length > 0) {
        console.log('\n📄 First Patent Details:');
        const firstPatent = patents[0];
        console.log(`   ID: ${firstPatent.id}`);
        console.log(`   Name: ${firstPatent.name || 'UNKNOWN'}`);
        console.log(`   Number: ${firstPatent.number || 'UNKNOWN'}`);
        console.log(`   Status: ${firstPatent.status || 'UNKNOWN'}`);
        console.log(`   Category: ${firstPatent.category || 'UNKNOWN'}`);
        console.log(`   Price: ${firstPatent.price || 'UNKNOWN'}`);
        console.log(`   Upload Date: ${firstPatent.uploadDate || 'UNKNOWN'}`);
        console.log(`   Abstract: ${firstPatent.abstract || 'UNKNOWN'}`);
      }
    } else {
      console.log('❌ Request failed:', response.data.message);
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testUserPatents().catch(console.error);
