const { Web3 } = require('web3');

// Ganache 配置
const GANACHE_URL = 'http://127.0.0.1:7545';
const EXPECTED_NETWORK_ID = '5777';

// 创建 Web3 实例
const web3 = new Web3(GANACHE_URL);

async function testGanacheConnection() {
    console.log('🚀 Ganache 连通性测试工具');
    console.log('========================\n');
    console.log('开始测试 Ganache 连接...\n');
    
    try {
        // 1. 测试基本连接
        console.log('1. 测试基本连接...');
        const isConnected = await web3.eth.net.isListening();
        if (isConnected) {
            console.log('✅ 成功连接到 Ganache 节点!');
        } else {
            throw new Error('无法连接到 Ganache 节点');
        }
        console.log('');

        // 2. 验证网络ID
        console.log('2. 验证网络ID...');
        const networkId = await web3.eth.net.getId();
        console.log(`   当前网络ID: ${networkId}`);
        console.log(`   期望网络ID: ${EXPECTED_NETWORK_ID}`);
        if (networkId.toString() === EXPECTED_NETWORK_ID) {
            console.log('✅ 网络ID匹配!');
        } else {
            console.log('⚠️  网络ID不匹配!');
        }
        console.log('');

        // 3. 获取链信息
        console.log('3. 获取区块链信息...');
        const chainId = await web3.eth.getChainId();
        const blockNumber = await web3.eth.getBlockNumber();
        const gasPrice = await web3.eth.getGasPrice();
        
        console.log(`✅ 区块链信息:`);
        console.log(`   链ID: ${chainId}`);
        console.log(`   当前区块号: ${blockNumber}`);
        console.log(`   Gas价格: ${web3.utils.fromWei(gasPrice, 'gwei')} Gwei`);
        console.log('');

        // 4. 获取账户信息
        console.log('4. 获取账户信息...');
        const accounts = await web3.eth.getAccounts();
        console.log(`✅ 找到 ${accounts.length} 个账户:`);
        
        for (let i = 0; i < Math.min(accounts.length, 5); i++) {
            const balance = await web3.eth.getBalance(accounts[i]);
            const balanceInEth = web3.utils.fromWei(balance, 'ether');
            console.log(`   账户 ${i}: ${accounts[i]}`);
            console.log(`   余额: ${balanceInEth} ETH`);
        }
        
        if (accounts.length > 5) {
            console.log(`   ... 还有 ${accounts.length - 5} 个账户`);
        }
        console.log('');

        // 5. 测试最新区块信息
        console.log('5. 获取最新区块信息...');
        const latestBlock = await web3.eth.getBlock('latest');
        console.log(`✅ 最新区块信息:`);
        console.log(`   区块号: ${latestBlock.number}`);
        console.log(`   区块哈希: ${latestBlock.hash}`);
        console.log(`   时间戳: ${new Date(Number(latestBlock.timestamp) * 1000).toLocaleString()}`);
        console.log(`   交易数量: ${latestBlock.transactions.length}`);
        console.log(`   Gas使用量: ${latestBlock.gasUsed}`);
        console.log(`   Gas限制: ${latestBlock.gasLimit}`);
        console.log('');

        // 6. 测试简单交易（如果有足够的账户）
        if (accounts.length >= 2) {
            console.log('6. 测试简单交易...');
            try {
                const fromAccount = accounts[0];
                const toAccount = accounts[1];
                const transferAmount = web3.utils.toWei('0.1', 'ether');
                
                // 获取交易前余额
                const fromBalanceBefore = await web3.eth.getBalance(fromAccount);
                const toBalanceBefore = await web3.eth.getBalance(toAccount);
                
                console.log(`   发送方余额 (交易前): ${web3.utils.fromWei(fromBalanceBefore, 'ether')} ETH`);
                console.log(`   接收方余额 (交易前): ${web3.utils.fromWei(toBalanceBefore, 'ether')} ETH`);
                
                // 发送交易
                const transaction = {
                    from: fromAccount,
                    to: toAccount,
                    value: transferAmount,
                    gas: 21000
                };
                
                const receipt = await web3.eth.sendTransaction(transaction);
                console.log(`✅ 交易成功!`);
                console.log(`   交易哈希: ${receipt.transactionHash}`);
                console.log(`   区块号: ${receipt.blockNumber}`);
                console.log(`   Gas使用量: ${receipt.gasUsed}`);
                
                // 获取交易后余额
                const fromBalanceAfter = await web3.eth.getBalance(fromAccount);
                const toBalanceAfter = await web3.eth.getBalance(toAccount);
                
                console.log(`   发送方余额 (交易后): ${web3.utils.fromWei(fromBalanceAfter, 'ether')} ETH`);
                console.log(`   接收方余额 (交易后): ${web3.utils.fromWei(toBalanceAfter, 'ether')} ETH`);
                
            } catch (txError) {
                console.log(`⚠️  交易测试失败: ${txError.message}`);
            }
        } else {
            console.log('6. 跳过交易测试 (账户数量不足)');
        }
        console.log('');

        // 7. 测试节点信息
        console.log('7. 获取节点信息...');
        try {
            const nodeInfo = await web3.eth.getNodeInfo();
            console.log(`✅ 节点信息: ${nodeInfo}`);
        } catch (nodeError) {
            console.log(`⚠️  无法获取节点信息: ${nodeError.message}`);
        }
        console.log('');

        // 8. 测试挖矿状态
        console.log('8. 检查挖矿状态...');
        try {
            const isMining = await web3.eth.isMining();
            const hashrate = await web3.eth.getHashrate();
            console.log(`✅ 挖矿状态:`);
            console.log(`   正在挖矿: ${isMining ? '是' : '否'}`);
            console.log(`   算力: ${hashrate} H/s`);
        } catch (miningError) {
            console.log(`⚠️  无法获取挖矿信息: ${miningError.message}`);
        }
        console.log('');

        console.log('🎉 所有测试完成! Ganache 节点运行正常。');
        console.log('\n📊 测试摘要:');
        console.log(`   - 连接地址: ${GANACHE_URL}`);
        console.log(`   - 网络ID: ${networkId}`);
        console.log(`   - 链ID: ${chainId}`);
        console.log(`   - 当前区块: ${blockNumber}`);
        console.log(`   - 可用账户: ${accounts.length}`);
        console.log(`   - Gas价格: ${web3.utils.fromWei(gasPrice, 'gwei')} Gwei`);

    } catch (error) {
        console.error('❌ Ganache 连接测试失败:');
        console.error(`   错误类型: ${error.name}`);
        console.error(`   错误信息: ${error.message}`);
        
        if (error.code === 'ECONNREFUSED' || error.message.includes('CONNECTION ERROR')) {
            console.error('\n💡 建议检查:');
            console.error('   1. Ganache 是否正在运行');
            console.error('   2. Ganache 是否监听在 127.0.0.1:7545');
            console.error('   3. 网络ID 是否设置为 5777');
            console.error('   4. 防火墙设置是否阻止了连接');
            console.error('   5. 尝试重启 Ganache');
        } else if (error.message.includes('Invalid JSON RPC')) {
            console.error('\n💡 可能的问题:');
            console.error('   1. Ganache 版本不兼容');
            console.error('   2. RPC 接口配置问题');
            console.error('   3. Web3 版本兼容性问题');
        }
        
        process.exit(1);
    }
}

// 优雅关闭处理
process.on('SIGINT', () => {
    console.log('\n\n👋 测试被中断，正在退出...');
    process.exit(0);
});

// 未捕获异常处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    process.exit(1);
});

// 调用函数进行测试
testGanacheConnection(); 