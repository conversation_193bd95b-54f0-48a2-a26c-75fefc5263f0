const { Web3 } = require('web3');

async function checkPatentStatus() {
  console.log('🔍 Checking patent status directly from blockchain...\n');

  try {
    const web3 = new Web3('http://127.0.0.1:7545');

    // Load contract ABI
    const contractArtifact = require('../backend/build/contracts/PatentRegistry.json');
    const contractAddress = '******************************************';
    
    const contract = new web3.eth.Contract(contractArtifact.abi, contractAddress);

    // Get total patents
    console.log('📊 Getting total patents from blockchain...');
    const totalPatents = await contract.methods.getTotalPatents().call();
    console.log(`✅ Total patents in blockchain: ${totalPatents}`);

    // Check status of each patent
    for (let i = 1; i <= totalPatents; i++) {
      console.log(`\n📄 Checking patent ID: ${i}`);
      const patent = await contract.methods.getPatent(i).call();
      
      console.log(`   Name: ${patent.name}`);
      console.log(`   Number: ${patent.number}`);
      console.log(`   Status: ${patent.status} (${getStatusText(patent.status)})`);
      console.log(`   Uploader: ${patent.uploaderAddress}`);
      console.log(`   Upload Date: ${new Date(parseInt(patent.uploadDate) * 1000).toLocaleString()}`);
      
      // If status is pending (0), let's try to approve it manually
      if (patent.status === '0') {
        console.log(`   🔄 This patent is pending - attempting to approve...`);
        
        try {
          // Get accounts
          const accounts = await web3.eth.getAccounts();
          const reviewerAccount = accounts[0]; // Use first account as reviewer
          
          console.log(`   👤 Using reviewer account: ${reviewerAccount}`);
          
          // Approve the patent
          const result = await contract.methods.approvePatent(i, 'Approved for testing').send({
            from: reviewerAccount,
            gas: 500000
          });
          
          console.log(`   ✅ Patent approved! Transaction: ${result.transactionHash}`);
          
          // Check status again
          const updatedPatent = await contract.methods.getPatent(i).call();
          console.log(`   📊 New status: ${updatedPatent.status} (${getStatusText(updatedPatent.status)})`);
          
        } catch (approveError) {
          console.log(`   ❌ Failed to approve: ${approveError.message}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

function getStatusText(status) {
  const statusMap = {
    '0': 'PENDING',
    '1': 'APPROVED', 
    '2': 'REJECTED',
    '3': 'NORMAL',
    '4': 'WITHDRAWN'
  };
  return statusMap[status] || 'UNKNOWN';
}

// Run the check
checkPatentStatus().catch(console.error);
