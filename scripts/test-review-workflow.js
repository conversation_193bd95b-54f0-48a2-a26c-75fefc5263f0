const { Web3 } = require('web3');
const fs = require('fs');
const path = require('path');

// Initialize Web3
const web3 = new Web3('http://127.0.0.1:7545');

// Load contract ABIs and addresses
const contractsDir = path.join(__dirname, '../backend/contracts');
const contractAddressesPath = path.join(__dirname, '../backend/contract-addresses.json');

let contractAddresses;
try {
  contractAddresses = JSON.parse(fs.readFileSync(contractAddressesPath, 'utf8'));
} catch (error) {
  console.error('❌ Failed to load contract addresses:', error);
  process.exit(1);
}

// Load PatentRegistry contract
const patentRegistryABI = JSON.parse(fs.readFileSync(path.join(__dirname, '../backend/build/contracts/PatentRegistry.json'), 'utf8')).abi;
const patentRegistry = new web3.eth.Contract(patentRegistryABI, contractAddresses.PatentRegistry);

// Load UserManagement contract
const userManagementABI = JSON.parse(fs.readFileSync(path.join(__dirname, '../backend/build/contracts/UserManagement.json'), 'utf8')).abi;
const userManagement = new web3.eth.Contract(userManagementABI, contractAddresses.UserManagement);

async function testReviewWorkflow() {
  try {
    console.log('🧪 Testing Patent Review Workflow...\n');

    // Get accounts
    const accounts = await web3.eth.getAccounts();
    const userAccount = accounts[1]; // Regular user
    const reviewerAccount = accounts[2]; // Reviewer
    const adminAccount = accounts[0]; // Admin

    console.log('👥 Test Accounts:');
    console.log(`   User: ${userAccount}`);
    console.log(`   Reviewer: ${reviewerAccount}`);
    console.log(`   Admin: ${adminAccount}\n`);

    // Step 1: Upload a test patent
    console.log('📄 Step 1: Uploading test patent...');

    const patentData = {
      name: 'Test Patent for Review Workflow',
      number: `CN${Date.now()}`,
      category: '测试分类',
      price: web3.utils.toWei('10', 'ether'),
      abstractText: 'This is a test patent for testing the review workflow functionality.',
      applicationDate: Math.floor(Date.now() / 1000) - 86400, // Yesterday
      expirationDate: Math.floor(Date.now() / 1000) + (20 * 365 * 24 * 60 * 60), // 20 years from now
      ownerName: 'Test Owner',
      ownerIdNumber: '123456789012345678',
      isAgentSale: false,
      documentHash: 'QmTestPatentDocumentHash123456789',
      ownershipDocumentHash: 'QmTestOwnershipDocumentHash123456789'
    };

    const uploadResult = await patentRegistry.methods.uploadPatent(
      patentData.name,
      patentData.number,
      patentData.category,
      patentData.price,
      patentData.abstractText,
      patentData.applicationDate,
      patentData.expirationDate,
      patentData.ownerName,
      patentData.ownerIdNumber,
      patentData.isAgentSale,
      patentData.documentHash,
      patentData.ownershipDocumentHash
    ).send({ from: userAccount, gas: 3000000 });

    const patentId = uploadResult.events.PatentUploaded.returnValues.patentId;
    console.log(`✅ Patent uploaded successfully! Patent ID: ${patentId}`);
    console.log(`   Transaction Hash: ${uploadResult.transactionHash}\n`);

    // Step 2: Check if patent appears in pending list
    console.log('🔍 Step 2: Checking pending patents...');

    try {
      const pendingPatents = await patentRegistry.methods.getPendingPatents().call({ from: reviewerAccount });
      console.log(`📋 Found ${pendingPatents.length} pending patents:`);

      pendingPatents.forEach((id, index) => {
        console.log(`   ${index + 1}. Patent ID: ${id}`);
      });

      if (pendingPatents.includes(patentId)) {
        console.log(`✅ Test patent ${patentId} found in pending list!\n`);
      } else {
        console.log(`❌ Test patent ${patentId} NOT found in pending list!\n`);
        return;
      }
    } catch (error) {
      console.error('❌ Error getting pending patents:', error.message);
      console.log('   This might be a role permission issue.\n');
    }

    // Step 3: Get patent details
    console.log('📖 Step 3: Getting patent details...');

    const patent = await patentRegistry.methods.getPatent(patentId).call();
    console.log('📄 Patent Details:');
    console.log(`   Name: ${patent.name}`);
    console.log(`   Number: ${patent.number}`);
    console.log(`   Status: ${patent.status} (0=PENDING, 1=APPROVED, 2=REJECTED)`);
    console.log(`   Uploader: ${patent.uploaderAddress}`);
    console.log(`   Owner: ${patent.ownerName}`);
    console.log(`   Price: ${web3.utils.fromWei(patent.price, 'ether')} ETH`);
    console.log(`   Upload Date: ${new Date(parseInt(patent.uploadDate) * 1000).toLocaleString()}\n`);

    // Step 4: Test approval workflow
    console.log('✅ Step 4: Testing approval workflow...');

    try {
      const approveResult = await patentRegistry.methods.approvePatent(
        patentId,
        'Patent approved for testing review workflow'
      ).send({ from: reviewerAccount, gas: 1000000 });

      console.log(`✅ Patent ${patentId} approved successfully!`);
      console.log(`   Transaction Hash: ${approveResult.transactionHash}`);
      console.log(`   Approved by: ${reviewerAccount}\n`);

      // Check updated status
      const updatedPatent = await patentRegistry.methods.getPatent(patentId).call();
      console.log(`📊 Updated Status: ${updatedPatent.status} (should be 1 for APPROVED)`);
      console.log(`   Reviewed by: ${updatedPatent.reviewedBy}`);
      console.log(`   Review comments: ${updatedPatent.reviewComments}`);
      console.log(`   Review date: ${new Date(parseInt(updatedPatent.reviewDate) * 1000).toLocaleString()}\n`);

    } catch (error) {
      console.error('❌ Error approving patent:', error.message);
      console.log('   This might be a role permission issue.\n');
    }

    // Step 5: Check if patent is no longer in pending list
    console.log('🔍 Step 5: Verifying patent removed from pending list...');

    try {
      const updatedPendingPatents = await patentRegistry.methods.getPendingPatents().call({ from: reviewerAccount });
      console.log(`📋 Pending patents after approval: ${updatedPendingPatents.length}`);

      if (!updatedPendingPatents.includes(patentId)) {
        console.log(`✅ Patent ${patentId} successfully removed from pending list!\n`);
      } else {
        console.log(`❌ Patent ${patentId} still in pending list after approval!\n`);
      }
    } catch (error) {
      console.error('❌ Error checking updated pending patents:', error.message);
    }

    console.log('🎉 Review workflow test completed!\n');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testReviewWorkflow().catch(console.error);
