const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// Configuration
const BACKEND_URL = 'http://localhost:3000';
const TEST_USER_ADDRESS = '0x164D435789d02dbE8317f48017D61663C1CE369B';

// Create test files
function createTestFiles() {
  const testDir = path.join(__dirname, 'test-files');
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir);
  }

  const patentContent = 'This is a test patent document content for testing the upload workflow.';
  const ownershipContent = 'This is a test ownership document content for testing the upload workflow.';

  fs.writeFileSync(path.join(testDir, 'test-patent.txt'), patentContent);
  fs.writeFileSync(path.join(testDir, 'test-ownership.txt'), ownershipContent);

  return {
    patentFile: path.join(testDir, 'test-patent.txt'),
    ownershipFile: path.join(testDir, 'test-ownership.txt')
  };
}

// Test patent upload
async function testPatentUpload() {
  console.log('🧪 Testing Patent Upload Workflow...\n');

  try {
    // Create test files
    const { patentFile, ownershipFile } = createTestFiles();

    // Prepare form data
    const formData = new FormData();
    formData.append('patentName', 'Test Patent for Workflow');
    formData.append('patentNumber', `TEST-${Date.now()}`);
    formData.append('patentCategory', 'Software');
    formData.append('transferPrice', '1.5');
    formData.append('patentAbstract', 'This is a test patent for testing the upload and retrieval workflow.');
    formData.append('applicationDate', '2024-01-01');
    formData.append('expirationDate', '2034-01-01');
    formData.append('ownerName', 'Test Owner');
    formData.append('ownerIdNumber', 'TEST123456');
    formData.append('isAgentSale', 'false');
    formData.append('uploaderAddress', TEST_USER_ADDRESS);
    formData.append('patentDocument', fs.createReadStream(patentFile));
    formData.append('ownershipDocument', fs.createReadStream(ownershipFile));

    console.log('📤 Uploading patent...');
    const uploadResponse = await axios.post(`${BACKEND_URL}/api/patents/upload`, formData, {
      headers: {
        ...formData.getHeaders(),
        'X-User-Address': TEST_USER_ADDRESS
      },
      timeout: 30000
    });

    console.log('✅ Upload Response:', uploadResponse.data);
    
    if (uploadResponse.data.success) {
      console.log('🎉 Patent uploaded successfully!');
      console.log('📋 Patent ID:', uploadResponse.data.data?.patentId || 'Not provided');
      return uploadResponse.data.data?.patentId;
    } else {
      console.log('❌ Upload failed:', uploadResponse.data.message);
      return null;
    }

  } catch (error) {
    console.error('❌ Upload error:', error.response?.data || error.message);
    return null;
  }
}

// Test patent retrieval
async function testPatentRetrieval(patentId) {
  console.log('\n🔍 Testing Patent Retrieval Workflow...\n');

  try {
    // Test 1: Get user patents
    console.log('📋 Getting user patents...');
    const userPatientsResponse = await axios.get(`${BACKEND_URL}/api/patents/user/${TEST_USER_ADDRESS}`, {
      headers: {
        'X-User-Address': TEST_USER_ADDRESS
      }
    });

    console.log('✅ User Patents Response:', JSON.stringify(userPatientsResponse.data, null, 2));

    if (userPatientsResponse.data.success && userPatientsResponse.data.data.length > 0) {
      console.log(`🎉 Found ${userPatientsResponse.data.data.length} patents for user`);
      
      // Check if patents have proper data
      const patents = userPatientsResponse.data.data;
      patents.forEach((patent, index) => {
        console.log(`\n📄 Patent ${index + 1}:`);
        console.log(`   ID: ${patent.id}`);
        console.log(`   Name: ${patent.name || 'UNKNOWN'}`);
        console.log(`   Number: ${patent.number || 'UNKNOWN'}`);
        console.log(`   Status: ${patent.status || 'UNKNOWN'}`);
        console.log(`   Category: ${patent.category || 'UNKNOWN'}`);
        console.log(`   Price: ${patent.price || 'UNKNOWN'}`);
        console.log(`   Upload Date: ${patent.uploadDate || 'UNKNOWN'}`);
      });
    } else {
      console.log('❌ No patents found for user or request failed');
    }

    // Test 2: Get specific patent details if we have a patent ID
    if (patentId) {
      console.log(`\n🔍 Getting details for patent ID: ${patentId}`);
      const patentDetailsResponse = await axios.get(`${BACKEND_URL}/api/patents/${patentId}`, {
        headers: {
          'X-User-Address': TEST_USER_ADDRESS
        }
      });

      console.log('✅ Patent Details Response:', JSON.stringify(patentDetailsResponse.data, null, 2));
    }

    // Test 3: Search patents
    console.log('\n🔍 Testing patent search...');
    const searchResponse = await axios.get(`${BACKEND_URL}/api/patents/search`, {
      params: {
        page: 1,
        limit: 10
      }
    });

    console.log('✅ Search Response:', JSON.stringify(searchResponse.data, null, 2));

  } catch (error) {
    console.error('❌ Retrieval error:', error.response?.data || error.message);
  }
}

// Test blockchain direct access
async function testBlockchainDirectAccess() {
  console.log('\n⛓️ Testing Direct Blockchain Access...\n');

  try {
    const { Web3 } = require('web3');
    const web3 = new Web3('http://127.0.0.1:7545');

    // Load contract ABI
    const contractArtifact = require('../backend/build/contracts/PatentRegistry.json');
    const contractAddress = '******************************************';
    
    const contract = new web3.eth.Contract(contractArtifact.abi, contractAddress);

    // Get total patents
    console.log('📊 Getting total patents from blockchain...');
    const totalPatents = await contract.methods.getTotalPatents().call();
    console.log(`✅ Total patents in blockchain: ${totalPatents}`);

    // Get user patents
    console.log('👤 Getting user patents from blockchain...');
    const userPatentIds = await contract.methods.getUserPatents(TEST_USER_ADDRESS).call();
    console.log(`✅ User patent IDs: [${userPatentIds.join(', ')}]`);

    // Get details for each patent
    for (const patentId of userPatentIds) {
      console.log(`\n📄 Getting details for patent ID: ${patentId}`);
      const patent = await contract.methods.getPatent(patentId).call();
      
      console.log(`   ID: ${patent.id}`);
      console.log(`   Name: ${patent.name}`);
      console.log(`   Number: ${patent.number}`);
      console.log(`   Category: ${patent.category}`);
      console.log(`   Price: ${web3.utils.fromWei(patent.price, 'ether')} ETH`);
      console.log(`   Status: ${patent.status}`);
      console.log(`   Uploader: ${patent.uploaderAddress}`);
      console.log(`   Document Hash: ${patent.documentHash}`);
      console.log(`   Ownership Hash: ${patent.ownershipDocumentHash}`);
      console.log(`   Upload Date: ${new Date(parseInt(patent.uploadDate) * 1000).toLocaleString()}`);
    }

  } catch (error) {
    console.error('❌ Blockchain access error:', error.message);
  }
}

// Main test function
async function runWorkflowTest() {
  console.log('🚀 Patent Upload and Retrieval Workflow Test');
  console.log('==============================================\n');

  // Test upload
  const patentId = await testPatentUpload();

  // Wait a moment for blockchain to process
  console.log('\n⏳ Waiting for blockchain to process...');
  await new Promise(resolve => setTimeout(resolve, 3000));

  // Test retrieval
  await testPatentRetrieval(patentId);

  // Test direct blockchain access
  await testBlockchainDirectAccess();

  console.log('\n🏁 Workflow test completed!');
}

// Run the test
runWorkflowTest().catch(console.error);
