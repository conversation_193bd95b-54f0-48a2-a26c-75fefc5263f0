const { Web3 } = require('web3');
const fs = require('fs');
const path = require('path');

async function testBlockchainCall() {
  try {
    console.log('🔍 Testing blockchain contract call...');
    
    // Initialize Web3
    const ganacheUrl = 'http://127.0.0.1:7545';
    const web3 = new Web3(ganacheUrl);
    
    // Test connection
    await web3.eth.net.isListening();
    console.log('✅ Web3 connected to Ganache');
    
    // Load contract addresses
    const addressesPath = path.join(__dirname, 'backend/contract-addresses.json');
    const contractAddresses = JSON.parse(fs.readFileSync(addressesPath, 'utf8'));
    console.log('📄 Contract addresses loaded:', contractAddresses);
    
    // Load PatentRegistry contract
    const patentRegistryPath = path.join(__dirname, 'backend/build/contracts/PatentRegistry.json');
    const patentRegistryData = JSON.parse(fs.readFileSync(patentRegistryPath, 'utf8'));
    const patentRegistryAbi = patentRegistryData.abi;
    
    const patentRegistryAddress = contractAddresses.PatentRegistry;
    const patentRegistry = new web3.eth.Contract(patentRegistryAbi, patentRegistryAddress);
    console.log('✅ PatentRegistry contract loaded at', patentRegistryAddress);
    
    // Load UserManagement contract
    const userManagementPath = path.join(__dirname, 'backend/build/contracts/UserManagement.json');
    const userManagementData = JSON.parse(fs.readFileSync(userManagementPath, 'utf8'));
    const userManagementAbi = userManagementData.abi;
    
    const userManagementAddress = contractAddresses.UserManagement;
    const userManagement = new web3.eth.Contract(userManagementAbi, userManagementAddress);
    console.log('✅ UserManagement contract loaded at', userManagementAddress);
    
    // Get accounts
    const accounts = await web3.eth.getAccounts();
    console.log('📋 Available accounts:', accounts.slice(0, 3));
    
    const userAddress = '******************************************';
    console.log('🔍 Testing user address:', userAddress);
    
    // Check if user exists in blockchain
    console.log('\n📋 Checking user existence in blockchain...');
    try {
      const userExists = await userManagement.methods.userExists(userAddress).call();
      console.log('User exists in blockchain:', userExists);
    } catch (error) {
      console.error('Error checking user existence:', error.message);
    }
    
    // Check if user is active in blockchain
    console.log('\n🔍 Checking user active status in blockchain...');
    try {
      const isUserActive = await userManagement.methods.isUserActive(userAddress).call();
      console.log('User is active in blockchain:', isUserActive);
    } catch (error) {
      console.error('Error checking user active status:', error.message);
    }
    
    // Get user profile from blockchain
    console.log('\n👤 Getting user profile from blockchain...');
    try {
      const userProfile = await userManagement.methods.getUser(userAddress).call();
      console.log('User profile from blockchain:', userProfile);
    } catch (error) {
      console.error('Error getting user profile:', error.message);
    }
    
    // Test patent upload parameters
    console.log('\n🧪 Testing patent upload parameters...');
    
    const patentParams = [
      '测试专利名称 - 这是一个用于测试的专利名称',  // name
      'CN202410001234.5',                              // number
      'invention',                                     // category
      web3.utils.toWei('1.5', 'ether'),              // price in Wei
      '这是一个测试专利的摘要。这个摘要包含了足够的字符数来满足最少50个字符的要求。专利摘要应该详细描述专利的技术内容和创新点，以便审核人员能够理解专利的核心价值。', // abstractText
      Math.floor(new Date('2023-01-15').getTime() / 1000), // applicationDate
      Math.floor(new Date('2043-01-15').getTime() / 1000), // expirationDate
      '测试用户',                                      // ownerName
      '110101199001011234',                           // ownerIdNumber
      false,                                          // isAgentSale
      'QmTestPatentDocumentHash123',                  // documentHash
      'QmTestOwnershipDocumentHash456'                // ownershipDocumentHash
    ];
    
    console.log('Patent parameters:', {
      name: patentParams[0],
      number: patentParams[1],
      category: patentParams[2],
      price: patentParams[3],
      applicationDate: new Date(patentParams[5] * 1000).toISOString(),
      expirationDate: new Date(patentParams[6] * 1000).toISOString(),
      ownerName: patentParams[7],
      ownerIdNumber: patentParams[8],
      isAgentSale: patentParams[9],
      documentHash: patentParams[10],
      ownershipDocumentHash: patentParams[11]
    });
    
    // Estimate gas for the transaction
    console.log('\n⛽ Estimating gas...');
    try {
      const gasEstimate = await patentRegistry.methods.uploadPatent(...patentParams).estimateGas({ 
        from: userAddress 
      });
      console.log('Gas estimate:', gasEstimate);
    } catch (error) {
      console.error('❌ Gas estimation failed:', error.message);
      
      // Try to get more detailed error information
      if (error.message.includes('revert')) {
        console.error('Contract revert reason:', error.message);
      }
      
      // Check if it's a user active issue
      if (error.message.includes('User is not active')) {
        console.error('🚨 User is not active in the blockchain contract!');
        
        // Try to register the user in blockchain
        console.log('\n🔧 Attempting to register user in blockchain...');
        try {
          const registerTx = await userManagement.methods.registerUser(
            userAddress,
            '测试用户 4',
            '***********',
            'TEST1748631828918004'
          ).send({ 
            from: accounts[0], // Use first account as admin
            gas: 500000 
          });
          console.log('✅ User registered in blockchain:', registerTx.transactionHash);
          
          // Try gas estimation again
          console.log('\n⛽ Retrying gas estimation after registration...');
          const gasEstimate2 = await patentRegistry.methods.uploadPatent(...patentParams).estimateGas({ 
            from: userAddress 
          });
          console.log('Gas estimate after registration:', gasEstimate2);
          
        } catch (regError) {
          console.error('❌ Failed to register user in blockchain:', regError.message);
        }
      }
    }
    
    // Check account balance
    console.log('\n💰 Checking account balance...');
    const balance = await web3.eth.getBalance(userAddress);
    console.log(`Account balance: ${web3.utils.fromWei(balance, 'ether')} ETH`);
    
    if (balance === '0') {
      console.log('🚨 Account has no ETH! Transferring some ETH...');
      try {
        const transferTx = await web3.eth.sendTransaction({
          from: accounts[0],
          to: userAddress,
          value: web3.utils.toWei('1', 'ether'),
          gas: 21000
        });
        console.log('✅ ETH transferred:', transferTx.transactionHash);
        
        const newBalance = await web3.eth.getBalance(userAddress);
        console.log(`New account balance: ${web3.utils.fromWei(newBalance, 'ether')} ETH`);
      } catch (transferError) {
        console.error('❌ Failed to transfer ETH:', transferError.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Error in blockchain test:', error);
  }
}

async function main() {
  console.log('🚀 Starting blockchain contract test...\n');
  
  await testBlockchainCall();
  
  console.log('\n✅ Blockchain test completed');
}

main().catch(console.error); 