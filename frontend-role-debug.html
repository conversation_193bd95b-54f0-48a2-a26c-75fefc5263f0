<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Role Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Frontend Role Debug Tool</h1>
        <p>This tool helps debug role detection issues in the frontend.</p>
        
        <div id="status" class="status info">
            Click "Check Current Status" to begin debugging...
        </div>
        
        <button onclick="checkCurrentStatus()">Check Current Status</button>
        <button onclick="testAPICall()">Test API Call</button>
        <button onclick="switchToReviewer()">Switch to Reviewer Account</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api';
        
        // Known reviewer addresses from our test
        const REVIEWER_ADDRESSES = [
            '0xa19515E50EA5c913e9ddB5476E4a59E097606727',
            '0x4ECd8c0fD3B1BebFd355C766664e2f5CE69CD6Ce',
            '0x234D1d5522F9E502a0239740E40c72d7c4FbBe9f'
        ];
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }
        
        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <div class="status ${type}">
                    <pre>${typeof content === 'object' ? JSON.stringify(content, null, 2) : content}</pre>
                </div>
            `;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function checkCurrentStatus() {
            updateStatus('🔍 Checking current status...', 'info');
            
            try {
                // Check if MetaMask is available
                if (typeof window.ethereum === 'undefined') {
                    updateStatus('❌ MetaMask not detected', 'error');
                    addResult('MetaMask Status', 'MetaMask is not installed or not available', 'error');
                    return;
                }
                
                // Get current account
                const accounts = await window.ethereum.request({ method: 'eth_accounts' });
                if (accounts.length === 0) {
                    updateStatus('⚠️ No accounts connected', 'warning');
                    addResult('Account Status', 'No MetaMask accounts connected', 'warning');
                    return;
                }
                
                const currentAccount = accounts[0];
                addResult('Current Account', currentAccount, 'success');
                
                // Check role via API
                const roleResponse = await fetch(`${API_BASE_URL}/user/role/${currentAccount}`, {
                    headers: {
                        'X-User-Address': currentAccount,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (roleResponse.ok) {
                    const roleData = await roleResponse.json();
                    const role = roleData.data?.role || 'unknown';
                    addResult('Current Role', role, role === 'reviewer' || role === 'admin' ? 'success' : 'warning');
                    
                    if (role === 'reviewer' || role === 'admin') {
                        updateStatus('✅ You have reviewer/admin access!', 'success');
                    } else {
                        updateStatus('⚠️ You need reviewer or admin role to access ReviewTradingView', 'warning');
                    }
                } else {
                    const errorData = await roleResponse.json();
                    addResult('Role Check Error', errorData, 'error');
                    updateStatus('❌ Failed to check role', 'error');
                }
                
            } catch (error) {
                updateStatus('❌ Error checking status', 'error');
                addResult('Error', error.message, 'error');
            }
        }
        
        async function testAPICall() {
            updateStatus('🧪 Testing API call...', 'info');
            
            try {
                const accounts = await window.ethereum.request({ method: 'eth_accounts' });
                if (accounts.length === 0) {
                    updateStatus('⚠️ No accounts connected', 'warning');
                    return;
                }
                
                const currentAccount = accounts[0];
                
                // Test pending transactions API
                const response = await fetch(`${API_BASE_URL}/transactions/pending`, {
                    headers: {
                        'X-User-Address': currentAccount,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const transactions = data.data?.data || data.data || [];
                    addResult('API Test Result', `✅ Success! Found ${transactions.length} pending transactions`, 'success');
                    if (transactions.length > 0) {
                        addResult('Sample Transaction', transactions[0], 'info');
                    }
                    updateStatus('✅ API call successful', 'success');
                } else {
                    const errorData = await response.json();
                    addResult('API Test Error', errorData, 'error');
                    updateStatus('❌ API call failed', 'error');
                }
                
            } catch (error) {
                updateStatus('❌ Error testing API', 'error');
                addResult('API Test Error', error.message, 'error');
            }
        }
        
        async function switchToReviewer() {
            updateStatus('🔄 Switching to reviewer account...', 'info');
            
            try {
                // Request account change to first reviewer address
                const reviewerAddress = REVIEWER_ADDRESSES[0];
                
                await window.ethereum.request({
                    method: 'wallet_requestPermissions',
                    params: [{ eth_accounts: {} }]
                });
                
                updateStatus('✅ Please select a reviewer account in MetaMask', 'success');
                addResult('Reviewer Addresses', REVIEWER_ADDRESSES.join('\n'), 'info');
                
            } catch (error) {
                updateStatus('❌ Error switching account', 'error');
                addResult('Switch Error', error.message, 'error');
            }
        }
        
        // Auto-check status on page load
        window.addEventListener('load', () => {
            setTimeout(checkCurrentStatus, 1000);
        });
    </script>
</body>
</html>
