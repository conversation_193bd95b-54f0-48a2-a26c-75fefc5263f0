const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

async function testUserVerification() {
  try {
    console.log('🧪 Testing User Verification for Patent Upload...\n');

    // Test addresses from our registration check
    const testAddresses = [
      '0x164D435789d02dbE8317f48017D61663C1CE369B', // Admin
      '0xa19515E50EA5c913e9ddB5476E4a59E097606727', // User1
      '0x4ECd8c0fD3B1BebFd355C766664e2f5CE69CD6Ce', // User2
      '0x234D1d5522F9E502a0239740E40c72d7c4FbBe9f', // Reviewer
      '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252'  // User4
    ];

    for (const address of testAddresses) {
      console.log(`\n🔍 Testing address: ${address}`);
      
      try {
        // Test user profile endpoint
        const profileResponse = await axios.get(`${API_BASE_URL}/user/profile/${address}`, {
          headers: {
            'X-User-Address': address,
            'Content-Type': 'application/json'
          }
        });

        if (profileResponse.data.success) {
          const profile = profileResponse.data.data;
          console.log(`✅ Profile: ${profile.name} (${profile.role})`);
        } else {
          console.log(`❌ Profile failed: ${profileResponse.data.error?.message}`);
        }

        // Test user role endpoint
        const roleResponse = await axios.get(`${API_BASE_URL}/user/role/${address}`, {
          headers: {
            'X-User-Address': address,
            'Content-Type': 'application/json'
          }
        });

        if (roleResponse.data.success) {
          const roleData = roleResponse.data.data;
          console.log(`✅ Role: ${roleData.role}`);
        } else {
          console.log(`❌ Role failed: ${roleResponse.data.error?.message}`);
        }

        // Test user switch endpoint (simulates what happens during MetaMask connection)
        const switchResponse = await axios.post(`${API_BASE_URL}/user/switch/${address}`, {}, {
          headers: {
            'X-User-Address': address,
            'Content-Type': 'application/json'
          }
        });

        if (switchResponse.data.success) {
          const userData = switchResponse.data.data;
          console.log(`✅ Switch: ${userData.profile.name} (${userData.role})`);
        } else {
          console.log(`❌ Switch failed: ${switchResponse.data.error?.message}`);
        }

      } catch (error) {
        console.log(`❌ Error testing ${address}:`, error.response?.data?.error?.message || error.message);
      }
    }

    console.log('\n🎯 Testing patent upload simulation...');
    
    // Test with a regular user account
    const testUser = testAddresses[1]; // User1
    console.log(`\n📤 Simulating patent upload for: ${testUser}`);
    
    try {
      // Create a minimal FormData simulation (without actual files)
      const testData = {
        patentName: 'Test Patent',
        patentNumber: 'TEST123',
        patentCategory: 'Software',
        transferPrice: '1.0',
        patentAbstract: 'Test abstract',
        applicationDate: '2024-01-01',
        expirationDate: '2034-01-01',
        ownerName: 'Test Owner',
        ownerIdNumber: 'TEST123',
        isAgentSale: false,
        uploaderAddress: testUser
      };

      // This will fail because we don't have files, but it should pass user verification
      const uploadResponse = await axios.post(`${API_BASE_URL}/patents/upload`, testData, {
        headers: {
          'X-User-Address': testUser,
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ Upload test passed user verification');
      
    } catch (error) {
      const errorData = error.response?.data?.error;
      if (errorData) {
        if (errorData.code === 'USER_NOT_REGISTERED' || errorData.code === 'USER_PROFILE_ERROR') {
          console.log(`❌ User verification failed: ${errorData.message}`);
          console.log('   Details:', errorData.details);
        } else if (errorData.message?.includes('required')) {
          console.log('✅ Upload test passed user verification (failed on file validation as expected)');
        } else {
          console.log(`⚠️ Other error: ${errorData.message}`);
        }
      } else {
        console.log(`⚠️ Network error: ${error.message}`);
      }
    }

    console.log('\n🎉 User verification test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testUserVerification(); 