const { Web3 } = require('web3');
const fs = require('fs');
const path = require('path');

// Initialize Web3
const web3 = new Web3('http://127.0.0.1:7545');

async function checkRegistrationStatus() {
  try {
    console.log('🔍 Checking registration status of Ganache accounts...\n');

    // Load contract addresses
    const contractAddressesPath = path.join(__dirname, 'contract-addresses.json');
    if (!fs.existsSync(contractAddressesPath)) {
      console.error('❌ Contract addresses file not found. Please deploy contracts first.');
      return;
    }
    
    const contractAddresses = JSON.parse(fs.readFileSync(contractAddressesPath, 'utf8'));
    
    // Load UserManagement contract
    const userManagementPath = path.join(__dirname, 'build/contracts/UserManagement.json');
    if (!fs.existsSync(userManagementPath)) {
      console.error('❌ UserManagement contract ABI not found. Please compile contracts first.');
      return;
    }
    
    const userManagementABI = JSON.parse(fs.readFileSync(userManagementPath, 'utf8')).abi;
    const userManagement = new web3.eth.Contract(userManagementABI, contractAddresses.UserManagement);

    // Get accounts
    const accounts = await web3.eth.getAccounts();
    console.log(`📊 Found ${accounts.length} accounts in Ganache\n`);

    let registeredCount = 0;
    let unregisteredAccounts = [];

    // Check each account
    for (let i = 0; i < Math.min(accounts.length, 10); i++) {
      const account = accounts[i];
      
      try {
        // Check if registered
        const isRegistered = await userManagement.methods.registeredUsers(account).call();
        
        if (isRegistered) {
          // Get profile
          const profile = await userManagement.methods.getUserProfile(account).call();
          const role = await userManagement.methods.getUserRole(account).call();
          
          const roleNames = ['User', 'Reviewer', 'Admin'];
          const roleName = roleNames[role] || 'Unknown';
          
          console.log(`✅ Account ${i}: ${account}`);
          console.log(`   Name: ${profile.name}`);
          console.log(`   Role: ${roleName}`);
          console.log(`   Active: ${profile.isActive}`);
          console.log('');
          
          registeredCount++;
        } else {
          console.log(`❌ Account ${i}: ${account} - NOT REGISTERED`);
          unregisteredAccounts.push({ index: i, address: account });
        }
      } catch (error) {
        console.log(`🔴 Account ${i}: ${account} - ERROR: ${error.message}`);
        unregisteredAccounts.push({ index: i, address: account });
      }
    }

    console.log('\n📈 Summary:');
    console.log(`   Total accounts: ${accounts.length}`);
    console.log(`   Registered: ${registeredCount}`);
    console.log(`   Unregistered: ${unregisteredAccounts.length}`);

    if (unregisteredAccounts.length > 0) {
      console.log('\n⚠️ Unregistered accounts found. This may cause "User does not exist" errors.');
      console.log('To fix this, run: node scripts/register-ganache-users.js');
      
      console.log('\n📋 Unregistered accounts:');
      unregisteredAccounts.forEach(acc => {
        console.log(`   - Account ${acc.index}: ${acc.address}`);
      });
    } else {
      console.log('\n✅ All accounts are properly registered!');
    }

  } catch (error) {
    console.error('❌ Error checking registration status:', error);
  }
}

checkRegistrationStatus(); 