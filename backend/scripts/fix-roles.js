#!/usr/bin/env node

require('dotenv').config();
const blockchainService = require('../src/services/blockchainService');

async function fixRoles() {
  console.log('🔧 Fixing Account Roles...\n');
  
  try {
    await blockchainService.initialize();
    const accounts = await blockchainService.getAccounts();
    
    // Fix Account 3 - should be User (role 0), not Reviewer
    console.log('🔧 Fixing Account 3 role...');
    await blockchainService.callContractMethod(
      'UserManagement', 
      'changeUserRole', 
      [accounts[3], 0], // 0 = User role
      { send: true, from: accounts[0] } // Admin (Account 0) makes the change
    );
    console.log('✅ Account 3 role changed to User');
    
    // Verify the fix
    console.log('\n📊 Verifying roles after fix:');
    for (let i = 0; i < Math.min(accounts.length, 6); i++) {
      try {
        const profile = await blockchainService.callContractMethod('UserManagement', 'getUserProfile', [accounts[i]]);
        const roleNames = ['User', 'Reviewer', 'Admin'];
        console.log(`Account ${i}: ${accounts[i]} - ${roleNames[profile.role]} (${profile.name})`);
      } catch (error) {
        console.log(`Account ${i}: ${accounts[i]} - Error: ${error.message}`);
      }
    }
    
    console.log('\n✅ Role fix completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

fixRoles().catch(console.error); 