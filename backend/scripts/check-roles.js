#!/usr/bin/env node

require('dotenv').config();
const blockchainService = require('../src/services/blockchainService');

async function checkCurrentRoles() {
  console.log('🔍 Checking Current Account Roles...\n');
  
  try {
    await blockchainService.initialize();
    const accounts = await blockchainService.getAccounts();
    
    console.log('📊 Current Account Roles:');
    
    for (let i = 0; i < Math.min(accounts.length, 8); i++) {
      try {
        const profile = await blockchainService.callContractMethod('UserManagement', 'getUserProfile', [accounts[i]]);
        const roleNames = ['User', 'Reviewer', 'Admin'];
        console.log(`Account ${i}: ${accounts[i]} - ${roleNames[profile.role]} (${profile.name})`);
      } catch (error) {
        console.log(`Account ${i}: ${accounts[i]} - Error: ${error.message}`);
      }
    }
    
    // Count roles
    const roleCounts = { admin: 0, reviewer: 0, user: 0 };
    for (let i = 0; i < accounts.length; i++) {
      try {
        const profile = await blockchainService.callContractMethod('UserManagement', 'getUserProfile', [accounts[i]]);
        if (profile.role == 2) roleCounts.admin++;
        else if (profile.role == 1) roleCounts.reviewer++;
        else if (profile.role == 0) roleCounts.user++;
      } catch (error) {
        // Skip error accounts
      }
    }
    
    console.log('\n📈 Role Summary:');
    console.log(`   Admins: ${roleCounts.admin}`);
    console.log(`   Reviewers: ${roleCounts.reviewer}`);
    console.log(`   Users: ${roleCounts.user}`);
    console.log(`   Total: ${roleCounts.admin + roleCounts.reviewer + roleCounts.user}`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkCurrentRoles().catch(console.error); 