const blockchainService = require('../src/services/blockchainService');

async function testProtectionWorkflow() {
    console.log('🚀 Testing Protection Workflow with Automatic Patent Transfer...\n');

    try {
        // Initialize blockchain service
        console.log('📡 Connecting to blockchain...');
        await blockchainService.initialize();
        console.log('✅ Blockchain connected\n');

        // Get accounts
        const accounts = await blockchainService.web3.eth.getAccounts();
        if (accounts.length < 4) {
            throw new Error('Need at least 4 accounts for testing');
        }

        const [deployer, user1, user2, reviewer] = accounts;
        console.log('👥 Test accounts:');
        console.log(`   Deployer: ${deployer}`);
        console.log(`   User1 (Patent Owner): ${user1}`);
        console.log(`   User2 (Rights Claimant): ${user2}`);
        console.log(`   Reviewer: ${reviewer}\n`);

        // Step 1: Register users (with checks for existing users)
        console.log('📝 Step 1: Checking and registering users...');
        
        // Check and register User1 as patent owner
        const user1Registered = await blockchainService.callContractMethod(
            'UserManagement',
            'registeredUsers',
            [user1]
        );
        if (!user1Registered) {
            await blockchainService.callContractMethod(
                'UserManagement',
                'registerUser',
                [user1, 'User1', '', 'ID001'],
                { send: true, from: user1 }
            );
            console.log('✅ User1 registered as patent owner');
        } else {
            console.log('ℹ️ User1 already registered');
        }

        // Check and register User2 as rights claimant
        const user2Registered = await blockchainService.callContractMethod(
            'UserManagement',
            'registeredUsers',
            [user2]
        );
        if (!user2Registered) {
            await blockchainService.callContractMethod(
                'UserManagement',
                'registerUser',
                [user2, 'User2', '', 'ID002'],
                { send: true, from: user2 }
            );
            console.log('✅ User2 registered as rights claimant');
        } else {
            console.log('ℹ️ User2 already registered');
        }

        // Check and register reviewer
        const reviewerRegistered = await blockchainService.callContractMethod(
            'UserManagement',
            'registeredUsers',
            [reviewer]
        );
        if (!reviewerRegistered) {
            await blockchainService.callContractMethod(
                'UserManagement',
                'registerUser',
                [reviewer, 'Reviewer', '', 'ID003'],
                { send: true, from: reviewer }
            );
            console.log('✅ Reviewer registered');
        } else {
            console.log('ℹ️ Reviewer already registered');
        }

        // Check and set reviewer role
        const reviewerRole = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserRole',
            [reviewer]
        );
        if (reviewerRole != 1) { // 1 = REVIEWER
            await blockchainService.callContractMethod(
                'UserManagement',
                'changeUserRole',
                [reviewer, 1], // 1 = REVIEWER
                { send: true, from: deployer }
            );
            console.log('✅ Reviewer role assigned');
        } else {
            console.log('ℹ️ Reviewer role already assigned');
        }
        console.log('');

        // Step 2: Upload a patent (User1)
        console.log('📄 Step 2: Uploading patent by User1...');
        const patentResult = await blockchainService.callContractMethod(
            'PatentRegistry',
            'uploadPatent',
            [
                'Test Patent for Protection',  // name
                'CN202410001234.5',           // number
                'Technology',                 // category
                1000000,                      // price (in wei)
                'A revolutionary patent for testing protection workflow', // abstractText
                Math.floor(Date.now() / 1000) - 365 * 24 * 60 * 60, // applicationDate (1 year ago)
                Math.floor(Date.now() / 1000) + 20 * 365 * 24 * 60 * 60, // expirationDate (20 years from now)
                'User1',                      // ownerName
                'ID001',                      // ownerIdNumber
                false,                        // isAgentSale
                'QmTestPatentHash123',        // documentHash
                'QmTestPatentDocHash456'      // ownershipDocumentHash
            ],
            { send: true, from: user1 }
        );

        const patentId = patentResult.events.PatentUploaded.returnValues.patentId;
        console.log(`✅ Patent uploaded with ID: ${patentId}`);

        // Step 3: Approve the patent (Reviewer)
        console.log('✅ Step 3: Approving patent...');
        await blockchainService.callContractMethod(
            'PatentRegistry',
            'approvePatent',
            [patentId, 'Patent approved for testing'],
            { send: true, from: reviewer }
        );
        console.log('✅ Patent approved\n');

        // Step 4: Verify initial patent ownership
        console.log('🔍 Step 4: Verifying initial patent ownership...');
        const initialPatent = await blockchainService.callContractMethod(
            'PatentRegistry',
            'getPatent',
            [patentId]
        );
        console.log(`   Current owner: ${initialPatent.uploaderAddress}`);
        console.log(`   Expected owner: ${user1}`);
        
        if (initialPatent.uploaderAddress.toLowerCase() !== user1.toLowerCase()) {
            throw new Error('Initial patent ownership verification failed');
        }
        console.log('✅ Initial ownership verified\n');

        // Step 5: Submit protection request (User2)
        console.log('🛡️ Step 5: Submitting protection request by User2...');
        const protectionResult = await blockchainService.callContractMethod(
            'ProtectionManager',
            'submitProtectionRequest',
            [
                patentId,
                user1, // current owner
                'I am the true inventor of this patent. I have evidence to prove my rightful ownership.',
                'QmEvidenceHash789'
            ],
            { send: true, from: user2 }
        );

        const caseId = protectionResult.events.ProtectionRequested.returnValues.caseId;
        console.log(`✅ Protection request submitted with case ID: ${caseId}`);

        // Step 6: Verify protection case details
        console.log('🔍 Step 6: Verifying protection case...');
        const protectionCase = await blockchainService.callContractMethod(
            'ProtectionManager',
            'getProtectionCase',
            [caseId]
        );
        console.log(`   Case ID: ${protectionCase.id}`);
        console.log(`   Patent ID: ${protectionCase.patentId}`);
        console.log(`   Claimant: ${protectionCase.claimantAddress}`);
        console.log(`   Current Owner: ${protectionCase.currentOwnerAddress}`);
        console.log(`   Status: ${protectionCase.status} (0=pending)`);
        console.log('✅ Protection case verified\n');

        // Step 7: Approve protection request (Reviewer) - This should trigger automatic transfer
        console.log('✅ Step 7: Approving protection request (automatic transfer)...');
        try {
            const approveResult = await blockchainService.callContractMethod(
                'ProtectionManager',
                'approveProtection',
                [caseId, 'Protection request approved - patent rights transferred to rightful owner'],
                { send: true, from: reviewer }
            );
            console.log(`✅ Protection approved with transaction: ${approveResult.transactionHash}`);

            // Check for events
            if (approveResult.events) {
                if (approveResult.events.ProtectionApproved) {
                    console.log('📋 ProtectionApproved event emitted');
                }
                if (approveResult.events.ProtectionResolved) {
                    console.log('🎯 ProtectionResolved event emitted');
                }
                if (approveResult.events.PatentOwnershipTransferred) {
                    console.log('🔄 PatentOwnershipTransferred event emitted');
                }
            }
        } catch (error) {
            console.error('❌ Protection approval failed:', error.message);
            throw error;
        }

        // Step 8: Verify patent ownership transfer
        console.log('\n🔍 Step 8: Verifying patent ownership transfer...');
        const finalPatent = await blockchainService.callContractMethod(
            'PatentRegistry',
            'getPatent',
            [patentId]
        );
        console.log(`   Previous owner: ${user1}`);
        console.log(`   New owner: ${finalPatent.uploaderAddress}`);
        console.log(`   Expected new owner: ${user2}`);

        if (finalPatent.uploaderAddress.toLowerCase() !== user2.toLowerCase()) {
            throw new Error('Patent ownership transfer verification failed');
        }
        console.log('✅ Patent ownership successfully transferred!\n');

        // Step 9: Verify protection case status
        console.log('🔍 Step 9: Verifying final protection case status...');
        const finalCase = await blockchainService.callContractMethod(
            'ProtectionManager',
            'getProtectionCase',
            [caseId]
        );
        console.log(`   Status: ${finalCase.status} (3=resolved)`);
        console.log(`   Resolution: ${finalCase.resolution}`);
        console.log(`   Blockchain TX Hash: ${finalCase.blockchainTxHash}`);

        if (finalCase.status != 3) { // 3 = RESOLVED
            throw new Error('Protection case status verification failed');
        }
        console.log('✅ Protection case marked as resolved\n');

        // Step 10: Verify user patent lists
        console.log('🔍 Step 10: Verifying user patent lists...');
        const user1Patents = await blockchainService.callContractMethod(
            'PatentRegistry',
            'getUserPatents',
            [user1]
        );
        const user2Patents = await blockchainService.callContractMethod(
            'PatentRegistry',
            'getUserPatents',
            [user2]
        );

        console.log(`   User1 patents count: ${user1Patents.length}`);
        console.log(`   User2 patents count: ${user2Patents.length}`);

        const user2HasPatent = user2Patents.some(id => id.toString() === patentId.toString());
        const user1HasPatent = user1Patents.some(id => id.toString() === patentId.toString());

        if (!user2HasPatent) {
            throw new Error('Patent not found in User2 patent list');
        }
        if (user1HasPatent) {
            throw new Error('Patent still found in User1 patent list');
        }

        console.log('✅ User patent lists updated correctly\n');

        console.log('🎉 SUCCESS: Complete protection workflow with automatic patent transfer tested successfully!');
        console.log('\n📋 Summary:');
        console.log(`   ✅ Patent ${patentId} successfully transferred from ${user1} to ${user2}`);
        console.log(`   ✅ Protection case ${caseId} resolved automatically`);
        console.log(`   ✅ All ownership records updated correctly`);
        console.log(`   ✅ Smart contract automatic transfer working as expected`);

    } catch (error) {
        console.error('❌ Protection workflow test failed:', error);
        process.exit(1);
    }
}

// Run the test
testProtectionWorkflow()
    .then(() => {
        console.log('\n✅ All tests completed successfully!');
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n❌ Test suite failed:', error);
        process.exit(1);
    }); 