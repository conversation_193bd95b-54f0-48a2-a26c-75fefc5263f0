#!/usr/bin/env node

/**
 * Test script for Ganache accounts auto-registration
 * This script tests the auto-registration functionality without starting the full server
 */

require('dotenv').config();
const blockchainService = require('../src/services/blockchainService');

async function testGanacheRegistration() {
  console.log('🧪 Testing Ganache Accounts Auto-Registration');
  console.log('=' .repeat(50));

  try {
    // Initialize blockchain service
    console.log('1. Initializing blockchain service...');
    await blockchainService.initialize();
    console.log('✅ Blockchain service initialized');

    // Get accounts before registration
    console.log('\n2. Getting Ganache accounts...');
    const accounts = await blockchainService.getAccounts();
    console.log(`📊 Found ${accounts.length} accounts in Ganache`);

    // Check registration status before auto-registration
    console.log('\n3. Checking current registration status...');
    let registeredBefore = 0;
    for (let i = 0; i < Math.min(accounts.length, 5); i++) {
      try {
        const isRegistered = await blockchainService.callContractMethod(
          'UserManagement',
          'registeredUsers',
          [accounts[i]]
        );
        if (isRegistered) registeredBefore++;
        console.log(`   Account ${i}: ${accounts[i]} - ${isRegistered ? 'Registered' : 'Not registered'}`);
      } catch (error) {
        console.log(`   Account ${i}: ${accounts[i]} - Error checking: ${error.message}`);
      }
    }
    
    if (accounts.length > 5) {
      console.log(`   ... and ${accounts.length - 5} more accounts`);
    }

    // Perform auto-registration
    console.log('\n4. Performing auto-registration...');
    console.log('   Testing intelligent skip feature...');
    await blockchainService.autoRegisterGanacheAccounts();

    // Check registration status after auto-registration
    console.log('\n5. Checking registration status after auto-registration...');
    let registeredAfter = 0;
    for (let i = 0; i < Math.min(accounts.length, 10); i++) {
      try {
        const isRegistered = await blockchainService.callContractMethod(
          'UserManagement',
          'registeredUsers',
          [accounts[i]]
        );
        
        if (isRegistered) {
          registeredAfter++;
          
          // Get user profile
          const profile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [accounts[i]]
          );
          
          // Get user role
          const role = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserRole',
            [accounts[i]]
          );
          
          const roleMap = { 0: 'User', 1: 'Reviewer', 2: 'Admin' };
          const roleName = roleMap[role] || 'Unknown';
          
          console.log(`   ✅ Account ${i}: ${accounts[i]} - ${roleName} (${profile.name})`);
        } else {
          console.log(`   ❌ Account ${i}: ${accounts[i]} - Not registered`);
        }
      } catch (error) {
        console.log(`   🔴 Account ${i}: ${accounts[i]} - Error: ${error.message}`);
      }
    }

    // Test intelligent skip: run auto-registration again
    console.log('\n6. Testing intelligent skip feature (running auto-registration again)...');
    await blockchainService.autoRegisterGanacheAccounts();

    // Summary
    console.log('\n7. Summary:');
    console.log(`   📊 Total accounts: ${accounts.length}`);
    console.log(`   📈 Registered before: ${registeredBefore}`);
    console.log(`   📈 Registered after: ${registeredAfter}`);
    console.log(`   🆕 Newly registered: ${registeredAfter - registeredBefore}`);

    // Test user switch API simulation
    console.log('\n8. Testing user switch functionality...');
    if (accounts.length > 0 && registeredAfter > 0) {
      try {
        // Find a registered account
        for (let i = 0; i < Math.min(accounts.length, 3); i++) {
          const isRegistered = await blockchainService.callContractMethod(
            'UserManagement',
            'registeredUsers',
            [accounts[i]]
          );
          
          if (isRegistered) {
            const profile = await blockchainService.callContractMethod(
              'UserManagement',
              'getUserProfile',
              [accounts[i]]
            );
            
            const role = await blockchainService.callContractMethod(
              'UserManagement',
              'getUserRole',
              [accounts[i]]
            );
            
            const roleMap = { 0: 'user', 1: 'reviewer', 2: 'admin' };
            const roleName = roleMap[role] || 'user';
            
            console.log(`   🔄 Simulating switch to: ${accounts[i]} (${profile.name}, ${roleName})`);
            
            // Simulate permission check
            const permissionsMap = {
              user: ['patents.view', 'patents.upload'],
              reviewer: ['patents.view', 'patents.upload', 'patents.review'],
              admin: ['patents.view', 'patents.upload', 'patents.review', 'users.manage']
            };
            
            const permissions = permissionsMap[roleName] || [];
            console.log(`   🔑 User permissions: ${permissions.join(', ')}`);
            break;
          }
        }
      } catch (error) {
        console.log(`   🔴 Error testing user switch: ${error.message}`);
      }
    }

    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Main execution
if (require.main === module) {
  testGanacheRegistration()
    .then(() => {
      console.log('\n✅ All tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { testGanacheRegistration }; 