#!/usr/bin/env node

/**
 * Test script for complete trading workflow with ownership transfer
 * This script tests the full trading process from patent upload to ownership transfer
 */

require('dotenv').config();
const blockchainService = require('../src/services/blockchainService');

async function testTradingWorkflow() {
  console.log('🧪 Testing Complete Trading Workflow with Ownership Transfer');
  console.log('=' .repeat(60));

  try {
    // Initialize blockchain service
    console.log('1. Initializing blockchain service...');
    await blockchainService.initialize();
    console.log('✅ Blockchain service initialized');

    // Get accounts
    const accounts = await blockchainService.getAccounts();
    const [admin, seller, buyer, reviewer] = accounts;

    console.log('\n2. Test Accounts:');
    console.log(`   Admin: ${admin}`);
    console.log(`   Seller: ${seller}`);
    console.log(`   Buyer: ${buyer}`);
    console.log(`   Reviewer: ${reviewer}`);

    // Step 1: Register users (using auto-registration)
    console.log('\n3. Registering test users...');
    await blockchainService.autoRegisterGanacheAccounts();
    console.log('   ✅ Users auto-registered');

    // Set reviewer role
    await blockchainService.callContractMethod(
      'UserManagement',
      'changeUserRole',
      [reviewer, 1], // 1 = REVIEWER
      { send: true, from: admin }
    );
    console.log('   ✅ Reviewer role assigned');

    // Step 2: Upload a patent
    console.log('\n4. Uploading patent for testing...');
    
    const patentData = {
      name: 'Test Trading Patent',
      number: `CN202425${Date.now()}.X`, // Unique number
      category: '测试技术',
      price: blockchainService.web3.utils.toWei('10', 'ether'),
      abstractText: 'This is a test patent for trading workflow verification',
      applicationDate: Math.floor(Date.now() / 1000) - 365 * 24 * 60 * 60, // 1 year ago
      expirationDate: Math.floor(Date.now() / 1000) + 10 * 365 * 24 * 60 * 60, // 10 years from now
      ownerName: 'Patent Seller',
      ownerIdNumber: 'ID123456789',
      isAgentSale: false,
      documentHash: 'QmTestPatentDocument123',
      ownershipDocumentHash: 'QmTestOwnershipDocument123'
    };

    const uploadResult = await blockchainService.callContractMethod(
      'PatentRegistry',
      'uploadPatent',
      [
        patentData.name,
        patentData.number,
        patentData.category,
        patentData.price,
        patentData.abstractText,
        patentData.applicationDate,
        patentData.expirationDate,
        patentData.ownerName,
        patentData.ownerIdNumber,
        patentData.isAgentSale,
        patentData.documentHash,
        patentData.ownershipDocumentHash
      ],
      { send: true, from: seller }
    );

    const patentId = uploadResult.events.PatentUploaded.returnValues.patentId;
    console.log(`   ✅ Patent uploaded with ID: ${patentId}`);

    // Step 3: Approve patent
    console.log('\n5. Approving patent...');
    
    await blockchainService.callContractMethod(
      'PatentRegistry',
      'approvePatent',
      [patentId, 'Patent approved for trading test'],
      { send: true, from: reviewer }
    );
    console.log('   ✅ Patent approved');

    // Step 4: Check initial patent status and ownership
    console.log('\n6. Checking initial patent state...');
    
    let patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentId]
    );
    console.log(`   Status: ${patent.status} (1=APPROVED, 4=NORMAL, 5=TRADING)`);
    console.log(`   Owner: ${patent.uploaderAddress}`);
    console.log(`   Ownership Match: ${patent.uploaderAddress.toLowerCase() === seller.toLowerCase()}`);

    // Step 5: Initiate transaction
    console.log('\n7. Initiating transaction...');
    
    const initiateResult = await blockchainService.callContractMethod(
      'TransactionManager',
      'initiateTransaction',
      [patentId, seller, patentData.price],
      { send: true, from: buyer }
    );

    const transactionId = initiateResult.events.TransactionInitiated.returnValues.transactionId;
    console.log(`   ✅ Transaction initiated with ID: ${transactionId}`);

    // Step 6: Set patent to trading status
    console.log('\n8. Setting patent to trading status...');
    
    await blockchainService.callContractMethod(
      'PatentRegistry',
      'setTradingStatus',
      [patentId, true],
      { send: true, from: buyer }
    );
    
    patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentId]
    );
    console.log(`   ✅ Patent status updated to: ${patent.status} (should be 5=TRADING)`);

    // Step 7: Check transaction status
    console.log('\n9. Checking transaction status...');
    
    let transaction = await blockchainService.callContractMethod(
      'TransactionManager',
      'getTransaction',
      [transactionId]
    );
    console.log(`   Transaction Status: ${transaction.status} (0=PENDING, 1=APPROVED, 3=COMPLETED)`);
    console.log(`   Buyer: ${transaction.buyerAddress}`);
    console.log(`   Seller: ${transaction.sellerAddress}`);
    console.log(`   Price: ${blockchainService.web3.utils.fromWei(transaction.price, 'ether')} ETH`);

    // Step 8: Approve transaction and transfer ownership
    console.log('\n10. Approving transaction and transferring ownership...');
    
    await blockchainService.callContractMethod(
      'TransactionManager',
      'approveTransaction',
      [transactionId, 'Transaction approved - ownership will be transferred'],
      { send: true, from: reviewer }
    );
    console.log('   ✅ Transaction approved');

    // Transfer ownership
    await blockchainService.callContractMethod(
      'PatentRegistry',
      'transferOwnership',
      [patentId, buyer],
      { send: true, from: reviewer }
    );
    console.log('   ✅ Ownership transferred');

    // Complete transaction
    await blockchainService.callContractMethod(
      'TransactionManager',
      'completeTransaction',
      [transactionId, '0x' + Math.random().toString(16).substr(2, 64)], // Mock blockchain hash
      { send: true, from: reviewer }
    );
    console.log('   ✅ Transaction completed');

    // Step 9: Verify final state
    console.log('\n11. Verifying final state...');
    
    // Check patent ownership
    patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentId]
    );
    console.log(`   Patent Status: ${patent.status} (should be 4=NORMAL)`);
    console.log(`   New Owner: ${patent.uploaderAddress}`);
    console.log(`   Expected New Owner: ${buyer}`);
    console.log(`   ✅ Ownership Transfer Success: ${patent.uploaderAddress.toLowerCase() === buyer.toLowerCase()}`);

    // Check transaction status
    transaction = await blockchainService.callContractMethod(
      'TransactionManager',
      'getTransaction',
      [transactionId]
    );
    console.log(`   Transaction Status: ${transaction.status} (should be 3=COMPLETED)`);
    console.log(`   Completion Date: ${new Date(parseInt(transaction.completionDate) * 1000).toLocaleString()}`);

    // Check patent transaction history
    const patentTransactions = await blockchainService.callContractMethod(
      'TransactionManager',
      'getPatentTransactions',
      [patentId]
    );
    console.log(`   Patent Transaction History: ${patentTransactions.length} transactions`);

    // Step 10: Test that patent cannot be purchased again
    console.log('\n12. Testing purchase prevention...');
    
    try {
      await blockchainService.callContractMethod(
        'TransactionManager',
        'initiateTransaction',
        [patentId, buyer, patentData.price], // buyer is now the owner, selling to seller
        { send: true, from: buyer } // buyer trying to buy from themselves
      );
      console.log('   ❌ ERROR: Should not be able to purchase from yourself');
    } catch (error) {
      console.log('   ✅ Correctly prevented self-purchase');
    }

    console.log('\n🎉 Trading Workflow Test Completed Successfully!');
    console.log('\n📊 Summary:');
    console.log(`   ✅ Patent uploaded and approved`);
    console.log(`   ✅ Transaction initiated and set to trading status`);
    console.log(`   ✅ Transaction approved by reviewer`);
    console.log(`   ✅ Ownership transferred from seller to buyer`);
    console.log(`   ✅ Transaction completed with proper status`);
    console.log(`   ✅ Patent status returned to normal`);
    console.log(`   ✅ Prevented duplicate purchases`);

  } catch (error) {
    console.error('\n❌ Test failed:', error);
    if (error.receipt) {
      console.error('Transaction receipt:', error.receipt);
    }
    throw error;
  }
}

// Main execution
if (require.main === module) {
  testTradingWorkflow()
    .then(() => {
      console.log('\n✅ All tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { testTradingWorkflow }; 