# Server Configuration
PORT=3000
NODE_ENV=development

# Blockchain Configuration
GANACHE_URL=http://localhost:7545
NETWORK_ID=5777
PRIVATE_KEY=your_private_key_here

# IPFS Configuration
IPFS_URL=http://localhost:5001
IPFS_GATEWAY=http://localhost:8080

# Smart Contract Addresses (will be populated after deployment)
USER_MANAGEMENT_CONTRACT=
PATENT_REGISTRY_CONTRACT=
TRANSACTION_MANAGER_CONTRACT=
PROTECTION_MANAGER_CONTRACT=
NOTIFICATION_SYSTEM_CONTRACT=

# Security
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# Logging
LOG_LEVEL=info
