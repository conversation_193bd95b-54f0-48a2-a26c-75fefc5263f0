// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "./UserManagement.sol";
import "./PatentRegistry.sol";

/**
 * @title TransactionManager
 * @dev Smart contract for managing patent trading transactions
 */
contract TransactionManager {

    // Transaction status enum
    enum TransactionStatus { PENDING, APPROVED, REJECTED, COMPLETED, CANCELLED }

    // Transaction structure
    struct Transaction {
        uint256 id;
        uint256 patentId;
        address buyerAddress;
        address sellerAddress;
        uint256 price;
        TransactionStatus status;
        uint256 submitDate;
        address reviewedBy;
        string reviewComments;
        uint256 reviewDate;
        string blockchainTxHash;
        uint256 completionDate;
    }

    // Events
    event TransactionInitiated(
        uint256 indexed transactionId,
        uint256 indexed patentId,
        address indexed buyer,
        address seller,
        uint256 price
    );
    event TransactionApproved(uint256 indexed transactionId, address indexed reviewer);
    event TransactionRejected(uint256 indexed transactionId, address indexed reviewer, string reason);
    event TransactionCompleted(uint256 indexed transactionId, string blockchainTxHash);
    event TransactionCancelled(uint256 indexed transactionId, address indexed cancelledBy);

    // State variables
    mapping(uint256 => Transaction) public transactions;
    mapping(address => uint256[]) public userTransactions; // All transactions for a user (buyer or seller)
    mapping(uint256 => uint256[]) public patentTransactions; // All transactions for a patent
    uint256 public nextTransactionId;
    uint256 public totalTransactions;

    UserManagement public userManagement;
    PatentRegistry public patentRegistry;

    // Modifiers
    modifier onlyReviewerOrAdmin() {
        UserManagement.Role role = userManagement.getUserRole(msg.sender);
        require(
            role == UserManagement.Role.REVIEWER ||
            role == UserManagement.Role.ADMIN,
            "Only reviewer or admin can perform this action"
        );
        _;
    }

    modifier transactionExists(uint256 transactionId) {
        require(
            transactionId < nextTransactionId &&
            transactions[transactionId].id == transactionId,
            "Transaction does not exist"
        );
        _;
    }

    modifier onlyTransactionParty(uint256 transactionId) {
        require(
            transactions[transactionId].buyerAddress == msg.sender ||
            transactions[transactionId].sellerAddress == msg.sender,
            "Only transaction parties can perform this action"
        );
        _;
    }

    constructor(address _userManagementAddress, address _patentRegistryAddress) {
        userManagement = UserManagement(_userManagementAddress);
        patentRegistry = PatentRegistry(_patentRegistryAddress);
        nextTransactionId = 1;
        totalTransactions = 0;
    }

    /**
     * @dev Initiate a patent purchase transaction
     */
    function initiateTransaction(
        uint256 patentId,
        address sellerAddress,
        uint256 price
    ) external returns (uint256) {
        // Verify patent exists and is available for trading
        PatentRegistry.Patent memory patent = patentRegistry.getPatent(patentId);
        require(
            patent.status == PatentRegistry.PatentStatus.APPROVED ||
            patent.status == PatentRegistry.PatentStatus.NORMAL,
            "Patent is not available for trading"
        );
        require(patent.uploaderAddress == sellerAddress, "Invalid seller address");
        require(msg.sender != sellerAddress, "Cannot buy your own patent");
        require(price > 0, "Price must be greater than 0");
        require(userManagement.isUserActive(msg.sender), "Buyer is not active");
        require(userManagement.isUserActive(sellerAddress), "Seller is not active");

        uint256 transactionId = nextTransactionId;
        nextTransactionId++;

        transactions[transactionId] = Transaction({
            id: transactionId,
            patentId: patentId,
            buyerAddress: msg.sender,
            sellerAddress: sellerAddress,
            price: price,
            status: TransactionStatus.PENDING,
            submitDate: block.timestamp,
            reviewedBy: address(0),
            reviewComments: "",
            reviewDate: 0,
            blockchainTxHash: "",
            completionDate: 0
        });

        userTransactions[msg.sender].push(transactionId);
        userTransactions[sellerAddress].push(transactionId);
        patentTransactions[patentId].push(transactionId);
        totalTransactions++;

        emit TransactionInitiated(transactionId, patentId, msg.sender, sellerAddress, price);

        return transactionId;
    }

    /**
     * @dev Approve a transaction (reviewer/admin only)
     */
    function approveTransaction(uint256 transactionId, string memory comments)
        external
        onlyReviewerOrAdmin
        transactionExists(transactionId)
    {
        require(
            transactions[transactionId].status == TransactionStatus.PENDING,
            "Transaction is not pending review"
        );

        transactions[transactionId].status = TransactionStatus.APPROVED;
        transactions[transactionId].reviewedBy = msg.sender;
        transactions[transactionId].reviewComments = comments;
        transactions[transactionId].reviewDate = block.timestamp;

        emit TransactionApproved(transactionId, msg.sender);
    }

    /**
     * @dev Reject a transaction (reviewer/admin only)
     */
    function rejectTransaction(uint256 transactionId, string memory reason)
        external
        onlyReviewerOrAdmin
        transactionExists(transactionId)
    {
        require(
            transactions[transactionId].status == TransactionStatus.PENDING,
            "Transaction is not pending review"
        );

        transactions[transactionId].status = TransactionStatus.REJECTED;
        transactions[transactionId].reviewedBy = msg.sender;
        transactions[transactionId].reviewComments = reason;
        transactions[transactionId].reviewDate = block.timestamp;

        emit TransactionRejected(transactionId, msg.sender, reason);
    }

    /**
     * @dev Complete a transaction (after blockchain payment)
     */
    function completeTransaction(uint256 transactionId, string memory blockchainTxHash)
        external
        onlyReviewerOrAdmin
        transactionExists(transactionId)
    {
        require(
            transactions[transactionId].status == TransactionStatus.APPROVED,
            "Transaction is not approved"
        );
        require(bytes(blockchainTxHash).length > 0, "Blockchain transaction hash required");

        transactions[transactionId].status = TransactionStatus.COMPLETED;
        transactions[transactionId].blockchainTxHash = blockchainTxHash;
        transactions[transactionId].completionDate = block.timestamp;

        emit TransactionCompleted(transactionId, blockchainTxHash);
    }

    /**
     * @dev Cancel a transaction (by transaction parties or admin)
     */
    function cancelTransaction(uint256 transactionId)
        external
        transactionExists(transactionId)
    {
        require(
            transactions[transactionId].buyerAddress == msg.sender ||
            transactions[transactionId].sellerAddress == msg.sender ||
            userManagement.getUserRole(msg.sender) == UserManagement.Role.ADMIN,
            "Not authorized to cancel this transaction"
        );
        require(
            transactions[transactionId].status == TransactionStatus.PENDING ||
            transactions[transactionId].status == TransactionStatus.APPROVED,
            "Transaction cannot be cancelled"
        );

        transactions[transactionId].status = TransactionStatus.CANCELLED;

        emit TransactionCancelled(transactionId, msg.sender);
    }

    /**
     * @dev Get transaction details
     */
    function getTransaction(uint256 transactionId)
        external
        view
        transactionExists(transactionId)
        returns (Transaction memory)
    {
        return transactions[transactionId];
    }

    /**
     * @dev Get user's transactions
     */
    function getUserTransactions(address userAddress) external view returns (uint256[] memory) {
        return userTransactions[userAddress];
    }

    /**
     * @dev Get patent's transactions
     */
    function getPatentTransactions(uint256 patentId) external view returns (uint256[] memory) {
        return patentTransactions[patentId];
    }

    /**
     * @dev Get pending transactions for review
     */
    function getPendingTransactions() external view onlyReviewerOrAdmin returns (uint256[] memory) {
        uint256[] memory pendingTransactions = new uint256[](totalTransactions);
        uint256 count = 0;

        for (uint256 i = 1; i < nextTransactionId; i++) {
            if (transactions[i].status == TransactionStatus.PENDING) {
                pendingTransactions[count] = i;
                count++;
            }
        }

        // Resize array to actual count
        uint256[] memory result = new uint256[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = pendingTransactions[i];
        }

        return result;
    }

    /**
     * @dev Get total transactions count
     */
    function getTotalTransactions() external view returns (uint256) {
        return totalTransactions;
    }

    /**
     * @dev Get transactions by status
     */
    function getTransactionsByStatus(TransactionStatus status)
        external
        view
        onlyReviewerOrAdmin
        returns (uint256[] memory)
    {
        uint256[] memory statusTransactions = new uint256[](totalTransactions);
        uint256 count = 0;

        for (uint256 i = 1; i < nextTransactionId; i++) {
            if (transactions[i].status == status) {
                statusTransactions[count] = i;
                count++;
            }
        }

        // Resize array to actual count
        uint256[] memory result = new uint256[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = statusTransactions[i];
        }

        return result;
    }

    /**
     * @dev Get buyer's transactions
     */
    function getBuyerTransactions(address buyerAddress) external view returns (uint256[] memory) {
        uint256[] memory buyerTxs = new uint256[](totalTransactions);
        uint256 count = 0;

        for (uint256 i = 1; i < nextTransactionId; i++) {
            if (transactions[i].buyerAddress == buyerAddress) {
                buyerTxs[count] = i;
                count++;
            }
        }

        // Resize array to actual count
        uint256[] memory result = new uint256[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = buyerTxs[i];
        }

        return result;
    }

    /**
     * @dev Get seller's transactions
     */
    function getSellerTransactions(address sellerAddress) external view returns (uint256[] memory) {
        uint256[] memory sellerTxs = new uint256[](totalTransactions);
        uint256 count = 0;

        for (uint256 i = 1; i < nextTransactionId; i++) {
            if (transactions[i].sellerAddress == sellerAddress) {
                sellerTxs[count] = i;
                count++;
            }
        }

        // Resize array to actual count
        uint256[] memory result = new uint256[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = sellerTxs[i];
        }

        return result;
    }
}
