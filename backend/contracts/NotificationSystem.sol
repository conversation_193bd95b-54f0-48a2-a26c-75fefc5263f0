// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "./UserManagement.sol";

/**
 * @title NotificationSystem
 * @dev Smart contract for managing user notifications
 */
contract NotificationSystem {
    
    // Notification type enum
    enum NotificationType { 
        PATENT_APPROVED, 
        PATENT_REJECTED, 
        TRANSACTION_INITIATED,
        TRANSACTION_APPROVED, 
        TRANSACTION_REJECTED,
        TRANSACTION_COMPLETED,
        PROTECTION_REQUESTED,
        PROTECTION_APPROVED,
        PROTECTION_REJECTED,
        SYSTEM_ANNOUNCEMENT
    }
    
    // Notification severity enum
    enum NotificationSeverity { INFO, SUCCESS, WARNING, ERROR }
    
    // Notification structure
    struct Notification {
        uint256 id;
        address recipientAddress;
        NotificationType notificationType;
        string title;
        string message;
        NotificationSeverity severity;
        bool isRead;
        uint256 timestamp;
        string relatedId; // Related patent ID, transaction ID, etc.
        address senderAddress;
    }
    
    // Events
    event NotificationSent(
        uint256 indexed notificationId, 
        address indexed recipient, 
        NotificationType notificationType,
        string title
    );
    event NotificationRead(uint256 indexed notificationId, address indexed recipient);
    event AllNotificationsRead(address indexed recipient);
    
    // State variables
    mapping(uint256 => Notification) public notifications;
    mapping(address => uint256[]) public userNotifications;
    mapping(address => uint256) public unreadCounts;
    uint256 public nextNotificationId;
    uint256 public totalNotifications;
    
    UserManagement public userManagement;
    
    // Modifiers
    modifier onlyAdmin() {
        UserManagement.Role role = userManagement.getUserRole(msg.sender);
        require(
            role == UserManagement.Role.ADMIN,
            "Only admin can perform this action"
        );
        _;
    }
    
    modifier notificationExists(uint256 notificationId) {
        require(
            notificationId < nextNotificationId && 
            notifications[notificationId].id == notificationId, 
            "Notification does not exist"
        );
        _;
    }
    
    modifier onlyRecipient(uint256 notificationId) {
        require(
            notifications[notificationId].recipientAddress == msg.sender,
            "Only recipient can perform this action"
        );
        _;
    }
    
    constructor(address _userManagementAddress) {
        userManagement = UserManagement(_userManagementAddress);
        nextNotificationId = 1;
        totalNotifications = 0;
    }
    
    /**
     * @dev Send a notification to a user
     */
    function sendNotification(
        address recipientAddress,
        NotificationType notificationType,
        string memory title,
        string memory message,
        NotificationSeverity severity,
        string memory relatedId
    ) external returns (uint256) {
        require(userManagement.isUserActive(recipientAddress), "Recipient is not active");
        require(bytes(title).length > 0, "Title cannot be empty");
        require(bytes(message).length > 0, "Message cannot be empty");
        
        uint256 notificationId = nextNotificationId;
        nextNotificationId++;
        
        notifications[notificationId] = Notification({
            id: notificationId,
            recipientAddress: recipientAddress,
            notificationType: notificationType,
            title: title,
            message: message,
            severity: severity,
            isRead: false,
            timestamp: block.timestamp,
            relatedId: relatedId,
            senderAddress: msg.sender
        });
        
        userNotifications[recipientAddress].push(notificationId);
        unreadCounts[recipientAddress]++;
        totalNotifications++;
        
        emit NotificationSent(notificationId, recipientAddress, notificationType, title);
        
        return notificationId;
    }
    
    /**
     * @dev Send system announcement to all users (admin only)
     */
    function sendSystemAnnouncement(
        string memory title,
        string memory message,
        NotificationSeverity severity
    ) external onlyAdmin {
        require(bytes(title).length > 0, "Title cannot be empty");
        require(bytes(message).length > 0, "Message cannot be empty");
        
        // Note: In a real implementation, you might want to iterate through all users
        // For now, this is a placeholder that would need to be called for each user
        // or implemented differently to handle mass notifications efficiently
        
        emit NotificationSent(0, address(0), NotificationType.SYSTEM_ANNOUNCEMENT, title);
    }
    
    /**
     * @dev Mark a notification as read
     */
    function markAsRead(uint256 notificationId) 
        external 
        notificationExists(notificationId) 
        onlyRecipient(notificationId) 
    {
        require(!notifications[notificationId].isRead, "Notification already read");
        
        notifications[notificationId].isRead = true;
        unreadCounts[msg.sender]--;
        
        emit NotificationRead(notificationId, msg.sender);
    }
    
    /**
     * @dev Mark all notifications as read for a user
     */
    function markAllAsRead(address userAddress) external {
        require(
            msg.sender == userAddress || 
            userManagement.getUserRole(msg.sender) == UserManagement.Role.ADMIN,
            "Not authorized to mark notifications as read"
        );
        
        uint256[] memory userNotifs = userNotifications[userAddress];
        for (uint256 i = 0; i < userNotifs.length; i++) {
            if (!notifications[userNotifs[i]].isRead) {
                notifications[userNotifs[i]].isRead = true;
            }
        }
        
        unreadCounts[userAddress] = 0;
        
        emit AllNotificationsRead(userAddress);
    }
    
    /**
     * @dev Get notification details
     */
    function getNotification(uint256 notificationId) 
        external 
        view 
        notificationExists(notificationId) 
        returns (Notification memory) 
    {
        return notifications[notificationId];
    }
    
    /**
     * @dev Get user's notifications
     */
    function getUserNotifications(address userAddress) external view returns (uint256[] memory) {
        return userNotifications[userAddress];
    }
    
    /**
     * @dev Get user's unread notifications
     */
    function getUserUnreadNotifications(address userAddress) external view returns (uint256[] memory) {
        uint256[] memory userNotifs = userNotifications[userAddress];
        uint256[] memory unreadNotifs = new uint256[](userNotifs.length);
        uint256 count = 0;
        
        for (uint256 i = 0; i < userNotifs.length; i++) {
            if (!notifications[userNotifs[i]].isRead) {
                unreadNotifs[count] = userNotifs[i];
                count++;
            }
        }
        
        // Resize array to actual count
        uint256[] memory result = new uint256[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = unreadNotifs[i];
        }
        
        return result;
    }
    
    /**
     * @dev Get user's unread count
     */
    function getUnreadCount(address userAddress) external view returns (uint256) {
        return unreadCounts[userAddress];
    }
    
    /**
     * @dev Get total notifications count
     */
    function getTotalNotifications() external view returns (uint256) {
        return totalNotifications;
    }
    
    /**
     * @dev Get notifications by type for a user
     */
    function getUserNotificationsByType(address userAddress, NotificationType notificationType) 
        external 
        view 
        returns (uint256[] memory) 
    {
        uint256[] memory userNotifs = userNotifications[userAddress];
        uint256[] memory typeNotifs = new uint256[](userNotifs.length);
        uint256 count = 0;
        
        for (uint256 i = 0; i < userNotifs.length; i++) {
            if (notifications[userNotifs[i]].notificationType == notificationType) {
                typeNotifs[count] = userNotifs[i];
                count++;
            }
        }
        
        // Resize array to actual count
        uint256[] memory result = new uint256[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = typeNotifs[i];
        }
        
        return result;
    }
    
    /**
     * @dev Delete old notifications (admin only, for cleanup)
     */
    function deleteOldNotifications(uint256 olderThanTimestamp) external onlyAdmin {
        // This is a simplified version - in practice, you'd want more sophisticated cleanup
        // that properly handles array management and gas costs
        require(olderThanTimestamp < block.timestamp, "Invalid timestamp");
        
        // Implementation would iterate through notifications and remove old ones
        // This is left as a placeholder due to complexity of array management in Solidity
    }
}
