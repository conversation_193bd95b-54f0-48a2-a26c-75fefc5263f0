// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title UserManagement
 * @dev Smart contract for managing user roles and profiles in the patent exchange platform
 */
contract UserManagement {
    
    // User roles enum
    enum Role { USER, REVIEWER, ADMIN }
    
    // User profile structure
    struct UserProfile {
        string name;
        string phone;
        string idNumber;
        Role role;
        bool isActive;
        uint256 registrationDate;
        uint256 lastLoginDate;
        address registeredBy; // For tracking who registered/updated the user
    }
    
    // Events
    event UserRegistered(address indexed userAddress, string name, Role role);
    event UserUpdated(address indexed userAddress, address indexed updatedBy);
    event RoleChanged(address indexed userAddress, Role oldRole, Role newRole, address indexed changedBy);
    event UserStatusChanged(address indexed userAddress, bool isActive, address indexed changedBy);
    
    // State variables
    mapping(address => UserProfile) public users;
    mapping(address => bool) public registeredUsers;
    address public owner;
    uint256 public totalUsers;
    
    // Role-based access control
    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can perform this action");
        _;
    }
    
    modifier onlyAdmin() {
        require(users[msg.sender].role == Role.ADMIN || msg.sender == owner, "Only admin can perform this action");
        _;
    }
    
    modifier onlyReviewerOrAdmin() {
        require(
            users[msg.sender].role == Role.REVIEWER || 
            users[msg.sender].role == Role.ADMIN || 
            msg.sender == owner, 
            "Only reviewer or admin can perform this action"
        );
        _;
    }
    
    modifier userExists(address userAddress) {
        require(registeredUsers[userAddress], "User does not exist");
        _;
    }
    
    constructor() {
        owner = msg.sender;
        // Register the owner as the first admin
        users[owner] = UserProfile({
            name: "System Administrator",
            phone: "",
            idNumber: "",
            role: Role.ADMIN,
            isActive: true,
            registrationDate: block.timestamp,
            lastLoginDate: block.timestamp,
            registeredBy: owner
        });
        registeredUsers[owner] = true;
        totalUsers = 1;
        
        emit UserRegistered(owner, "System Administrator", Role.ADMIN);
    }
    
    /**
     * @dev Register a new user
     * @param userAddress The address of the user to register
     * @param name The name of the user
     * @param phone The phone number of the user
     * @param idNumber The ID number of the user
     */
    function registerUser(
        address userAddress,
        string memory name,
        string memory phone,
        string memory idNumber
    ) external {
        require(!registeredUsers[userAddress], "User already registered");
        require(bytes(name).length > 0, "Name cannot be empty");
        
        users[userAddress] = UserProfile({
            name: name,
            phone: phone,
            idNumber: idNumber,
            role: Role.USER,
            isActive: true,
            registrationDate: block.timestamp,
            lastLoginDate: block.timestamp,
            registeredBy: msg.sender
        });
        
        registeredUsers[userAddress] = true;
        totalUsers++;
        
        emit UserRegistered(userAddress, name, Role.USER);
    }
    
    /**
     * @dev Update user profile
     * @param userAddress The address of the user to update
     * @param name The new name
     * @param phone The new phone number
     * @param idNumber The new ID number
     */
    function updateUserProfile(
        address userAddress,
        string memory name,
        string memory phone,
        string memory idNumber
    ) external userExists(userAddress) {
        require(
            msg.sender == userAddress || 
            users[msg.sender].role == Role.ADMIN || 
            msg.sender == owner,
            "Not authorized to update this profile"
        );
        require(bytes(name).length > 0, "Name cannot be empty");
        
        users[userAddress].name = name;
        users[userAddress].phone = phone;
        users[userAddress].idNumber = idNumber;
        
        emit UserUpdated(userAddress, msg.sender);
    }
    
    /**
     * @dev Change user role (admin only)
     * @param userAddress The address of the user
     * @param newRole The new role to assign
     */
    function changeUserRole(address userAddress, Role newRole) external onlyAdmin userExists(userAddress) {
        Role oldRole = users[userAddress].role;
        users[userAddress].role = newRole;
        
        emit RoleChanged(userAddress, oldRole, newRole, msg.sender);
    }
    
    /**
     * @dev Activate or deactivate a user (admin only)
     * @param userAddress The address of the user
     * @param isActive The new status
     */
    function setUserStatus(address userAddress, bool isActive) external onlyAdmin userExists(userAddress) {
        users[userAddress].isActive = isActive;
        
        emit UserStatusChanged(userAddress, isActive, msg.sender);
    }
    
    /**
     * @dev Update last login time
     * @param userAddress The address of the user
     */
    function updateLastLogin(address userAddress) external userExists(userAddress) {
        require(msg.sender == userAddress, "Can only update own login time");
        users[userAddress].lastLoginDate = block.timestamp;
    }
    
    /**
     * @dev Get user profile
     * @param userAddress The address of the user
     * @return UserProfile struct
     */
    function getUserProfile(address userAddress) external view returns (UserProfile memory) {
        require(registeredUsers[userAddress], "User does not exist");
        return users[userAddress];
    }
    
    /**
     * @dev Get user role
     * @param userAddress The address of the user
     * @return Role enum
     */
    function getUserRole(address userAddress) external view returns (Role) {
        if (!registeredUsers[userAddress]) {
            return Role.USER; // Default role for unregistered users
        }
        return users[userAddress].role;
    }
    
    /**
     * @dev Check if user is active
     * @param userAddress The address of the user
     * @return bool indicating if user is active
     */
    function isUserActive(address userAddress) external view returns (bool) {
        if (!registeredUsers[userAddress]) {
            return false;
        }
        return users[userAddress].isActive;
    }
    
    /**
     * @dev Check if user has specific role
     * @param userAddress The address of the user
     * @param role The role to check
     * @return bool indicating if user has the role
     */
    function hasRole(address userAddress, Role role) external view returns (bool) {
        if (!registeredUsers[userAddress]) {
            return role == Role.USER; // Unregistered users are considered regular users
        }
        return users[userAddress].role == role;
    }
    
    /**
     * @dev Get total number of registered users
     * @return uint256 total user count
     */
    function getTotalUsers() external view returns (uint256) {
        return totalUsers;
    }
}
