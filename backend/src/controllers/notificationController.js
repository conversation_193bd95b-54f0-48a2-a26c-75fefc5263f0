const blockchainService = require('../services/blockchainService');
const { formatDate, formatAddress, parsePaginationParams, createPagination } = require('../middleware/responseFormatter');
const { NotFoundError, ValidationError, AuthorizationError } = require('../middleware/errorHandler');

/**
 * Notification Controller
 * Handles notification operations
 */

/**
 * Get user's notifications
 */
const getUserNotifications = async (req, res) => {
  try {
    const { address } = req.params;
    const { page = 1, limit = 20, unreadOnly = false } = req.query;

    // Validate address
    if (!blockchainService.web3.utils.isAddress(address)) {
      throw new ValidationError('Invalid address format');
    }

    // Check authorization
    if (req.userRole === 'user' && req.userAddress.toLowerCase() !== address.toLowerCase()) {
      throw new AuthorizationError('You can only view your own notifications');
    }

    const { offset, pageSize } = parsePaginationParams(page, limit);

    // Get user's notification IDs from blockchain
    const notificationIds = await blockchainService.callContractMethod(
      'NotificationSystem',
      'getUserNotifications',
      [address]
    );

    const notifications = [];
    let unreadCount = 0;

    // Get details for each notification
    for (const notificationId of notificationIds) {
      try {
        const notification = await blockchainService.callContractMethod(
          'NotificationSystem',
          'getNotification',
          [notificationId]
        );

        // Apply unread filter if requested
        if (unreadOnly === 'true' && notification.isRead) {
          continue;
        }

        if (!notification.isRead) {
          unreadCount++;
        }

        const typeNames = {
          0: 'patent_approved',
          1: 'patent_rejected',
          2: 'transaction_approved',
          3: 'transaction_rejected',
          4: 'protection_approved',
          5: 'protection_rejected',
          6: 'system_announcement'
        };

        const severityNames = {
          0: 'info',
          1: 'success',
          2: 'warning',
          3: 'error'
        };

        notifications.push({
          id: notification.id.toString(),
          type: typeNames[notification.notificationType] || 'unknown',
          title: notification.title,
          message: notification.message,
          severity: severityNames[notification.severity] || 'info',
          read: notification.isRead,
          timestamp: formatDate(new Date(parseInt(notification.timestamp) * 1000)),
          relatedId: notification.relatedId
        });
      } catch (error) {
        console.warn(`Failed to get notification details for ID ${notificationId}:`, error);
        continue;
      }
    }

    // Sort by timestamp (newest first)
    notifications.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Apply pagination
    const paginatedNotifications = notifications.slice(offset, offset + pageSize);
    const pagination = createPagination(page, pageSize, notifications.length);

    res.success({
      notifications: paginatedNotifications,
      unreadCount,
      pagination
    });

  } catch (error) {
    console.error('Error getting user notifications:', error);
    throw error;
  }
};

/**
 * Mark notification as read
 */
const markNotificationAsRead = async (req, res) => {
  try {
    const { id } = req.params;
    const notificationId = parseInt(id);

    if (isNaN(notificationId) || notificationId < 0) {
      throw new ValidationError('Invalid notification ID');
    }

    // Get notification details to verify ownership
    const notification = await blockchainService.callContractMethod(
      'NotificationSystem',
      'getNotification',
      [notificationId]
    );

    if (!notification || notification.id.toString() !== id) {
      throw new NotFoundError('Notification not found');
    }

    // Check authorization
    if (req.userRole === 'user' &&
        req.userAddress.toLowerCase() !== notification.recipientAddress.toLowerCase()) {
      throw new AuthorizationError('You can only mark your own notifications as read');
    }

    // Mark notification as read on blockchain
    const result = await blockchainService.callContractMethod(
      'NotificationSystem',
      'markAsRead',
      [notificationId],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      notificationId: id,
      message: '通知已标记为已读'
    }, '通知已标记为已读');

  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

/**
 * Mark all notifications as read
 */
const markAllNotificationsAsRead = async (req, res) => {
  try {
    const { address } = req.params;

    // Validate address
    if (!blockchainService.web3.utils.isAddress(address)) {
      throw new ValidationError('Invalid address format');
    }

    // Check authorization
    if (req.userRole === 'user' && req.userAddress.toLowerCase() !== address.toLowerCase()) {
      throw new AuthorizationError('You can only mark your own notifications as read');
    }

    // Mark all notifications as read on blockchain
    const result = await blockchainService.callContractMethod(
      'NotificationSystem',
      'markAllAsRead',
      [address],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      address: formatAddress(address),
      message: '所有通知已标记为已读'
    }, '所有通知已标记为已读');

  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
};

/**
 * Send notification to user
 */
const sendNotification = async (req, res) => {
  try {
    const { recipientAddress, type, title, message, severity, relatedId } = req.body;

    // Check authorization - only admin can send notifications
    if (req.userRole !== 'admin') {
      throw new AuthorizationError('Only admins can send notifications');
    }

    // Validate inputs
    if (!recipientAddress || !type || !title || !message) {
      throw new ValidationError('Recipient address, type, title, and message are required');
    }

    if (!blockchainService.web3.utils.isAddress(recipientAddress)) {
      throw new ValidationError('Invalid recipient address format');
    }

    // Map type and severity to enum values
    const typeMap = {
      'patent_approved': 0,
      'patent_rejected': 1,
      'transaction_approved': 2,
      'transaction_rejected': 3,
      'protection_approved': 4,
      'protection_rejected': 5,
      'system_announcement': 6
    };

    const severityMap = {
      'info': 0,
      'success': 1,
      'warning': 2,
      'error': 3
    };

    const notificationType = typeMap[type];
    const notificationSeverity = severityMap[severity] || 0;

    if (notificationType === undefined) {
      throw new ValidationError('Invalid notification type');
    }

    // Send notification on blockchain
    const result = await blockchainService.callContractMethod(
      'NotificationSystem',
      'sendNotification',
      [recipientAddress, notificationType, title, message, notificationSeverity, relatedId || ''],
      { send: true, from: req.userAddress }
    );

    // Extract notification ID from events
    let notificationId = null;
    if (result.events && result.events.NotificationSent) {
      notificationId = result.events.NotificationSent.returnValues.notificationId;
    }

    res.created({
      notificationId: notificationId || 'pending',
      transactionHash: result.transactionHash,
      recipientAddress: formatAddress(recipientAddress),
      message: 'Notification sent successfully'
    }, 'Notification sent successfully');

  } catch (error) {
    console.error('Error sending notification:', error);
    throw error;
  }
};

/**
 * Get unread notification count
 */
const getUnreadCount = async (req, res) => {
  try {
    const { address } = req.params;

    // Validate address
    if (!blockchainService.web3.utils.isAddress(address)) {
      throw new ValidationError('Invalid address format');
    }

    // Check authorization
    if (req.userRole === 'user' && req.userAddress.toLowerCase() !== address.toLowerCase()) {
      throw new AuthorizationError('You can only view your own notification count');
    }

    // Get unread count from blockchain
    const unreadCount = await blockchainService.callContractMethod(
      'NotificationSystem',
      'getUnreadCount',
      [address]
    );

    res.success({
      address: formatAddress(address),
      unreadCount: parseInt(unreadCount)
    });

  } catch (error) {
    console.error('Error getting unread count:', error);
    throw error;
  }
};

module.exports = {
  getUserNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  sendNotification,
  getUnreadCount
};
