const blockchainService = require('../services/blockchainService');
const { formatDate, formatAddress, parsePaginationParams, createPagination } = require('../middleware/responseFormatter');
const { AuthorizationError } = require('../middleware/errorHandler');

/**
 * Admin Controller
 * Handles administrative operations and statistics
 */

/**
 * Get all users (admin only)
 */
const getAllUsers = async (req, res) => {
  const { page, limit, offset } = parsePaginationParams(req);
  const { role, status } = req.query;

  try {
    // Get total users count
    const totalUsers = await blockchainService.callContractMethod(
      'UserManagement',
      'getTotalUsers',
      []
    );

    // For now, we'll get all users and filter/paginate in memory
    // In a production system, you'd want to implement this more efficiently
    const users = [];
    
    // Get all accounts from the blockchain
    const accounts = await blockchainService.getAccounts();
    
    for (let i = 0; i < Math.min(accounts.length, 100); i++) { // Limit to prevent timeout
      try {
        const address = accounts[i];
        const isRegistered = await blockchainService.callContractMethod(
          'UserManagement',
          'registeredUsers',
          [address]
        );

        if (isRegistered) {
          const profile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [address]
          );

          const userRole = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserRole',
            [address]
          );

          const roleMap = {
            0: 'user',
            1: 'reviewer', 
            2: 'admin'
          };

          const user = {
            address: formatAddress(address),
            name: profile.name,
            phone: profile.phone,
            idNumber: profile.idNumber,
            role: roleMap[userRole] || 'user',
            registrationDate: formatDate(profile.registrationDate),
            lastLoginDate: formatDate(profile.lastLoginDate),
            status: profile.isActive ? 'active' : 'inactive'
          };

          // Apply filters
          if (role && user.role !== role) continue;
          if (status && user.status !== status) continue;

          users.push(user);
        }
      } catch (error) {
        // Skip accounts that cause errors
        continue;
      }
    }

    // Apply pagination
    const startIndex = offset;
    const endIndex = offset + limit;
    const paginatedUsers = users.slice(startIndex, endIndex);

    const pagination = createPagination(page, limit, users.length);

    res.paginated(paginatedUsers, pagination, 'Users retrieved successfully');
  } catch (error) {
    console.error('Error getting all users:', error);
    throw error;
  }
};

/**
 * Get user statistics (admin only)
 */
const getUserStatistics = async (req, res) => {
  try {
    // Get total users
    const totalUsers = await blockchainService.callContractMethod(
      'UserManagement',
      'getTotalUsers',
      []
    );

    // For detailed statistics, we'd need to iterate through users
    // This is a simplified version
    const stats = {
      totalUsers: parseInt(totalUsers),
      activeUsers: parseInt(totalUsers), // Simplified - assume all are active
      newUsersThisMonth: 0, // Would need to calculate based on registration dates
      usersByRole: {
        user: 0,
        reviewer: 0,
        admin: 1 // At least the system admin
      },
      userGrowth: [
        { month: "2024-01", count: parseInt(totalUsers) }
      ]
    };

    res.success(stats, 'User statistics retrieved successfully');
  } catch (error) {
    console.error('Error getting user statistics:', error);
    throw error;
  }
};

/**
 * Get system overview statistics (admin only)
 */
const getSystemOverview = async (req, res) => {
  try {
    // Get patent statistics
    const totalPatents = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getTotalPatents',
      []
    ).catch(() => 0);

    // Get transaction statistics
    const totalTransactions = await blockchainService.callContractMethod(
      'TransactionManager',
      'getTotalTransactions',
      []
    ).catch(() => 0);

    // Get user statistics
    const totalUsers = await blockchainService.callContractMethod(
      'UserManagement',
      'getTotalUsers',
      []
    ).catch(() => 0);

    // Get protection case statistics
    const totalProtectionCases = await blockchainService.callContractMethod(
      'ProtectionManager',
      'getTotalProtectionCases',
      []
    ).catch(() => 0);

    const overview = {
      patents: {
        total: parseInt(totalPatents),
        approved: 0, // Would need to calculate
        pending: 0,
        rejected: 0,
        thisMonth: 0
      },
      transactions: {
        total: parseInt(totalTransactions),
        completed: 0,
        pending: 0,
        rejected: 0,
        totalValue: "0"
      },
      users: {
        total: parseInt(totalUsers),
        active: parseInt(totalUsers),
        newThisMonth: 0
      },
      protectionCases: {
        total: parseInt(totalProtectionCases),
        resolved: 0,
        pending: 0
      }
    };

    res.success(overview, 'System overview retrieved successfully');
  } catch (error) {
    console.error('Error getting system overview:', error);
    throw error;
  }
};

/**
 * Get detailed patent statistics (admin only)
 */
const getPatentStatistics = async (req, res) => {
  const { period, category } = req.query;

  try {
    const totalPatents = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getTotalPatents',
      []
    ).catch(() => 0);

    // This is a simplified version - in production you'd analyze actual patent data
    const stats = {
      byCategory: [
        { category: "电子技术", count: Math.floor(parseInt(totalPatents) * 0.4) },
        { category: "智能控制", count: Math.floor(parseInt(totalPatents) * 0.3) },
        { category: "其他", count: Math.floor(parseInt(totalPatents) * 0.3) }
      ],
      byStatus: [
        { status: "approved", count: Math.floor(parseInt(totalPatents) * 0.8) },
        { status: "pending", count: Math.floor(parseInt(totalPatents) * 0.15) },
        { status: "rejected", count: Math.floor(parseInt(totalPatents) * 0.05) }
      ],
      timeline: [
        { date: "2024-01-01", uploads: 5, approvals: 4 },
        { date: "2024-01-02", uploads: 8, approvals: 6 }
      ],
      topUploaders: [
        { address: "0x1234...", name: "张三", count: 5 },
        { address: "0x5678...", name: "李四", count: 3 }
      ]
    };

    res.success(stats, 'Patent statistics retrieved successfully');
  } catch (error) {
    console.error('Error getting patent statistics:', error);
    throw error;
  }
};

/**
 * Get detailed transaction statistics (admin only)
 */
const getTransactionStatistics = async (req, res) => {
  try {
    const totalTransactions = await blockchainService.callContractMethod(
      'TransactionManager',
      'getTotalTransactions',
      []
    ).catch(() => 0);

    // This is a simplified version - in production you'd analyze actual transaction data
    const stats = {
      volumeByMonth: [
        { month: "2024-01", volume: "500000", count: parseInt(totalTransactions) }
      ],
      averagePrice: "50000",
      topTraders: [
        { address: "0x1234...", name: "张三", totalVolume: "150000" },
        { address: "0x5678...", name: "李四", totalVolume: "120000" }
      ],
      processingTimes: {
        average: "2.5 days",
        median: "2 days"
      }
    };

    res.success(stats, 'Transaction statistics retrieved successfully');
  } catch (error) {
    console.error('Error getting transaction statistics:', error);
    throw error;
  }
};

/**
 * Force register Ganache accounts to blockchain
 * Admin only - manually trigger Ganache accounts registration
 */
const registerGanacheAccounts = async (req, res) => {
  try {
    // This operation is admin only
    if (req.userRole !== 'admin') {
      throw new AuthorizationError('Only administrators can register Ganache accounts');
    }

    console.log(`🔄 Manual Ganache accounts registration triggered by ${req.userAddress}`);
    
    // Trigger the auto-registration function
    await blockchainService.autoRegisterGanacheAccounts();
    
    res.success({
      message: 'Ganache accounts registration completed',
      triggeredBy: formatAddress(req.userAddress),
      timestamp: new Date().toISOString()
    }, 'Ganache accounts registered successfully');
    
  } catch (error) {
    console.error('Error in manual Ganache registration:', error);
    throw error;
  }
};

/**
 * Get Ganache accounts registration status
 * Admin only - check which accounts are registered
 */
const getGanacheAccountsStatus = async (req, res) => {
  try {
    // This operation is admin only
    if (req.userRole !== 'admin') {
      throw new AuthorizationError('Only administrators can view Ganache accounts status');
    }

    // Get all accounts from Ganache
    const accounts = await blockchainService.getAccounts();
    
    if (!accounts || accounts.length === 0) {
      return res.success({
        accounts: [],
        summary: { total: 0, registered: 0, unregistered: 0 }
      }, 'No accounts found in Ganache');
    }

    const accountsStatus = [];
    let registeredCount = 0;
    
    // Check registration status for each account
    for (let i = 0; i < accounts.length; i++) {
      const account = accounts[i];
      
      try {
        // Check if account is registered
        const isRegistered = await blockchainService.callContractMethod(
          'UserManagement',
          'registeredUsers',
          [account]
        );

        let profile = null;
        let role = 'unknown';

        if (isRegistered) {
          try {
            // Get user profile
            profile = await blockchainService.callContractMethod(
              'UserManagement',
              'getUserProfile',
              [account]
            );
            
            // Get user role
            const roleNumber = await blockchainService.callContractMethod(
              'UserManagement',
              'getUserRole',
              [account]
            );

            const roleMap = { 0: 'user', 1: 'reviewer', 2: 'admin' };
            role = roleMap[roleNumber] || 'unknown';
            
            registeredCount++;
          } catch (profileError) {
            console.warn(`Warning: Could not get profile for ${account}:`, profileError.message);
          }
        }

        // Get account balance
        const balance = await blockchainService.getBalance(account);

        accountsStatus.push({
          index: i,
          address: formatAddress(account),
          isRegistered,
          role,
          profile: profile ? {
            name: profile.name,
            phone: profile.phone,
            idNumber: profile.idNumber,
            registrationDate: formatDate(profile.registrationDate),
            isActive: profile.isActive
          } : null,
          balance: parseFloat(balance).toFixed(4) + ' ETH'
        });
        
      } catch (error) {
        console.error(`Error checking account ${i} (${account}):`, error);
        accountsStatus.push({
          index: i,
          address: formatAddress(account),
          isRegistered: false,
          role: 'error',
          profile: null,
          balance: 'unknown',
          error: error.message
        });
      }
    }

    res.success({
      accounts: accountsStatus,
      summary: {
        total: accounts.length,
        registered: registeredCount,
        unregistered: accounts.length - registeredCount
      }
    }, 'Ganache accounts status retrieved successfully');
    
  } catch (error) {
    console.error('Error getting Ganache accounts status:', error);
    throw error;
  }
};

module.exports = {
  getAllUsers,
  getUserStatistics,
  getSystemOverview,
  getPatentStatistics,
  getTransactionStatistics,
  registerGanacheAccounts,
  getGanacheAccountsStatus
};
