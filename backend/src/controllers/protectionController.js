const blockchainService = require('../services/blockchainService');
const { formatDate, formatAddress, parsePaginationParams, createPagination } = require('../middleware/responseFormatter');
const { NotFoundError, ValidationError, AuthorizationError } = require('../middleware/errorHandler');

/**
 * Protection Controller
 * Handles rights protection operations
 */

/**
 * Submit a rights protection request
 */
const submitProtectionRequest = async (req, res) => {
  try {
    const { patentId, patentName, claimantAddress, description, evidenceFiles } = req.body;

    // Validate inputs
    if (!patentId || !patentName || !description) {
      throw new ValidationError('Patent ID, patent name, and description are required');
    }

    // Validate patent ID is a number
    const parsedPatentId = parseInt(patentId);
    if (isNaN(parsedPatentId) || parsedPatentId < 1) {
      throw new ValidationError('Invalid patent ID');
    }

    // Verify patent exists
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [parsedPatentId]
    );

    if (!patent || patent.id.toString() !== parsedPatentId.toString()) {
      throw new NotFoundError('Patent not found');
    }

    // Verify the claimant is not the current owner
    if (patent.uploaderAddress.toLowerCase() === req.userAddress.toLowerCase()) {
      throw new ValidationError('Cannot file protection request against your own patent');
    }

    // Submit protection request to blockchain
    const result = await blockchainService.callContractMethod(
      'ProtectionManager',
      'submitProtectionRequest',
      [parsedPatentId, patent.uploaderAddress, description, evidenceFiles ? evidenceFiles.join(',') : ''],
      { send: true, from: req.userAddress }
    );

    // Extract protection case ID from events
    let protectionId = null;
    if (result.events && result.events.ProtectionRequested) {
      protectionId = result.events.ProtectionRequested.returnValues.caseId;
    }

    res.created({
      protectionId: protectionId || 'pending',
      status: 'pending',
      transactionHash: result.transactionHash,
      message: '维权申请已成功提交！申请将由专业审核员进行审核，一旦审核通过，专利所有权将自动转移给您。'
    }, '维权申请已成功提交');

  } catch (error) {
    console.error('Error submitting protection request:', error);
    throw error;
  }
};

/**
 * Get user's protection cases
 */
const getUserProtectionCases = async (req, res) => {
  try {
    const userAddress = req.userAddress;

    // Get user's protection cases from blockchain
    let userCaseIds = [];

    try {
      userCaseIds = await blockchainService.callContractMethod(
        'ProtectionManager',
        'getUserProtectionCases',
        [userAddress]
      );
    } catch (error) {
      // If user doesn't exist in contract or has no cases, return empty array
      console.log(`User ${userAddress} has no protection cases or doesn't exist in contract:`, error.message);
      return res.success({ data: [] });
    }

    // If no case IDs returned, user has no cases
    if (!userCaseIds || userCaseIds.length === 0) {
      return res.success({ data: [] });
    }

    const userCases = [];

    // Get details for each case
    for (const caseId of userCaseIds) {
      // Validate case ID
      if (!caseId || isNaN(parseInt(caseId)) || parseInt(caseId) <= 0) {
        console.warn(`Invalid case ID: ${caseId}, skipping...`);
        continue;
      }

      try {
        const protectionCase = await blockchainService.callContractMethod(
          'ProtectionManager',
          'getProtectionCase',
          [caseId]
        );

        // Verify case exists and has valid data
        if (!protectionCase || !protectionCase.id || protectionCase.id.toString() !== caseId.toString()) {
          console.warn(`Protection case ${caseId} not found or invalid data, skipping...`);
          continue;
        }

        // Get patent details
        let patent = null;
        try {
          patent = await blockchainService.callContractMethod(
            'PatentRegistry',
            'getPatent',
            [protectionCase.patentId]
          );
        } catch (error) {
          console.warn(`Failed to get patent ${protectionCase.patentId} for case ${caseId}:`, error.message);
          // Continue with partial data
        }

        // Map status from blockchain enum to string
        const statusMap = ['pending', 'approved', 'rejected', 'resolved'];
        const status = statusMap[protectionCase.status] || 'unknown';

        userCases.push({
          id: protectionCase.id.toString(),
          patentId: protectionCase.patentId.toString(),
          patentName: patent ? patent.name : 'Unknown Patent',
          patentNumber: patent ? patent.number : 'Unknown',
          description: protectionCase.description,
          submitDate: formatDate(new Date(parseInt(protectionCase.submitDate) * 1000)),
          status: status,
          evidenceHash: protectionCase.evidenceHash,
          resolution: protectionCase.resolution,
          reviewDate: protectionCase.reviewDate > 0 ? formatDate(new Date(parseInt(protectionCase.reviewDate) * 1000)) : null,
          blockchainTxHash: protectionCase.blockchainTxHash
        });
      } catch (error) {
        console.warn(`Failed to get protection case details for ID ${caseId}:`, error.message);
        continue;
      }
    }

    // Sort by submit date (newest first)
    userCases.sort((a, b) => new Date(b.submitDate) - new Date(a.submitDate));

    res.success({ data: userCases });

  } catch (error) {
    console.error('Error getting user protection cases:', error);

    // For common blockchain errors, return empty array instead of error
    if (error.message && (
      error.message.includes('execution reverted') ||
      error.message.includes('User does not exist') ||
      error.message.includes('Invalid case ID') ||
      error.message.includes('Case does not exist')
    )) {
      console.log('Blockchain error likely indicates user has no cases, returning empty array');
      return res.success({ data: [] });
    }

    // For other errors, throw them
    throw error;
  }
};

/**
 * Get pending protection requests for review (reviewer/admin only)
 */
const getPendingProtectionRequests = async (req, res) => {
  try {
    // Check authorization - only reviewers and admins can view pending protection requests
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can view pending protection requests');
    }

    // Get pending protection cases from blockchain
    const pendingCases = await blockchainService.callContractMethod(
      'ProtectionManager',
      'getPendingProtectionCases',
      [],
      { from: req.userAddress }
    );

    const protectionRequests = [];

    // Get details for each pending case
    for (const caseId of pendingCases) {
      try {
        const protectionCase = await blockchainService.callContractMethod(
          'ProtectionManager',
          'getProtectionCase',
          [caseId]
        );

        // Get patent details
        const patent = await blockchainService.callContractMethod(
          'PatentRegistry',
          'getPatent',
          [protectionCase.patentId]
        );

        // Get applicant profile
        let applicantName = 'Unknown';
        try {
          const applicantProfile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [protectionCase.claimantAddress]
          );
          applicantName = applicantProfile.name || 'Unknown';
        } catch (error) {
          console.warn('Failed to get applicant profile:', error);
        }

        protectionRequests.push({
          id: protectionCase.id.toString(),
          patentAddress: formatAddress(patent.uploaderAddress),
          patentName: patent.name,
          applicantAddress: formatAddress(protectionCase.claimantAddress),
          applicantName,
          description: protectionCase.description,
          submitDate: formatDate(new Date(parseInt(protectionCase.submitDate) * 1000)),
          evidenceUrl: protectionCase.evidenceHash ? `/documents/evidence_${protectionCase.id}.pdf` : null,
          status: 'pending'
        });
      } catch (error) {
        console.warn(`Failed to get protection case details for ID ${caseId}:`, error);
        continue;
      }
    }

    // Sort by submit date (oldest first for review queue)
    protectionRequests.sort((a, b) => new Date(a.submitDate) - new Date(b.submitDate));

    res.success({ data: protectionRequests });

  } catch (error) {
    console.error('Error getting pending protection requests:', error);
    throw error;
  }
};

/**
 * Get pending protection cases
 */
const getPendingProtectionCases = async (req, res) => {
  try {
    // Check authorization - only reviewers and admins can view pending protection cases
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can view pending protection cases');
    }

    // Get pending protection cases from blockchain
    const pendingCases = await blockchainService.callContractMethod(
      'ProtectionManager',
      'getPendingProtectionCases',
      [],
      { from: req.userAddress }
    );

    const protectionCases = [];

    // Get details for each pending case
    for (const caseId of pendingCases) {
      try {
        const protectionCase = await blockchainService.callContractMethod(
          'ProtectionManager',
          'getProtectionCase',
          [caseId]
        );

        // Get patent details
        const patent = await blockchainService.callContractMethod(
          'PatentRegistry',
          'getPatent',
          [protectionCase.patentId]
        );

        // Get applicant profile
        let applicantName = 'Unknown';
        try {
          const applicantProfile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [protectionCase.claimantAddress]
          );
          applicantName = applicantProfile.name || 'Unknown';
        } catch (error) {
          console.warn('Failed to get applicant profile:', error);
        }

        protectionCases.push({
          id: protectionCase.id.toString(),
          patentId: protectionCase.patentId.toString(),
          patentName: patent.name,
          patentNumber: patent.number,
          applicantAddress: formatAddress(protectionCase.claimantAddress),
          applicantName,
          description: protectionCase.description,
          submitDate: formatDate(new Date(parseInt(protectionCase.submitDate) * 1000)),
          evidenceUrl: protectionCase.evidenceHash ? `/documents/evidence_${protectionCase.id}.pdf` : null,
          status: 'pending'
        });
      } catch (error) {
        console.warn(`Failed to get protection case details for ID ${caseId}:`, error);
        continue;
      }
    }

    // Sort by submit date (oldest first for review queue)
    protectionCases.sort((a, b) => new Date(a.submitDate) - new Date(b.submitDate));

    res.success({ data: protectionCases });

  } catch (error) {
    console.error('Error getting pending protection cases:', error);
    throw error;
  }
};

/**
 * Get protection case details
 */
const getProtectionCaseDetails = async (req, res) => {
  try {
    const { id } = req.params;
    const caseId = parseInt(id);

    if (isNaN(caseId) || caseId < 0) {
      throw new ValidationError('Invalid case ID');
    }

    // Get protection case from blockchain
    const protectionCase = await blockchainService.callContractMethod(
      'ProtectionManager',
      'getProtectionCase',
      [caseId]
    );

    if (!protectionCase || protectionCase.id.toString() !== id) {
      throw new NotFoundError('Protection case not found');
    }

    // Check authorization - only claimant, current owner, or reviewer/admin can view details
    const isClaimant = protectionCase.claimantAddress.toLowerCase() === req.userAddress.toLowerCase();
    const isCurrentOwner = protectionCase.currentOwnerAddress.toLowerCase() === req.userAddress.toLowerCase();
    const isReviewerOrAdmin = req.userRole === 'reviewer' || req.userRole === 'admin';

    if (!isClaimant && !isCurrentOwner && !isReviewerOrAdmin) {
      throw new AuthorizationError('Not authorized to view this protection case');
    }

    // Get patent details
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [protectionCase.patentId]
    );

    // Get user profiles
    let claimantName = 'Unknown';
    let currentOwnerName = 'Unknown';

    try {
      const claimantProfile = await blockchainService.callContractMethod(
        'UserManagement',
        'getUserProfile',
        [protectionCase.claimantAddress]
      );
      claimantName = claimantProfile.name || 'Unknown';
    } catch (error) {
      console.warn('Failed to get claimant profile:', error);
    }

    try {
      const ownerProfile = await blockchainService.callContractMethod(
        'UserManagement',
        'getUserProfile',
        [protectionCase.currentOwnerAddress]
      );
      currentOwnerName = ownerProfile.name || 'Unknown';
    } catch (error) {
      console.warn('Failed to get current owner profile:', error);
    }

    // Map status from blockchain enum to string
    const statusMap = ['pending', 'approved', 'rejected', 'resolved'];
    const status = statusMap[protectionCase.status] || 'unknown';

    const caseDetails = {
      id: protectionCase.id.toString(),
      patentId: protectionCase.patentId.toString(),
      patentName: patent.name,
      patentNumber: patent.number,
      claimantAddress: protectionCase.claimantAddress,
      claimantName,
      currentOwnerAddress: protectionCase.currentOwnerAddress,
      currentOwnerName,
      description: protectionCase.description,
      evidenceHash: protectionCase.evidenceHash,
      evidenceUrl: protectionCase.evidenceHash ? `/documents/evidence_${protectionCase.id}.pdf` : null,
      status: status,
      submitDate: formatDate(new Date(parseInt(protectionCase.submitDate) * 1000)),
      reviewedBy: protectionCase.reviewedBy,
      resolution: protectionCase.resolution,
      reviewDate: protectionCase.reviewDate > 0 ? formatDate(new Date(parseInt(protectionCase.reviewDate) * 1000)) : null,
      blockchainTxHash: protectionCase.blockchainTxHash
    };

    res.success({ data: caseDetails });

  } catch (error) {
    console.error('Error getting protection case details:', error);
    throw error;
  }
};

/**
 * Approve a protection request
 */
const approveProtectionRequest = async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewerAddress, resolution } = req.body;
    const caseId = parseInt(id);

    // Check authorization
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can approve protection requests');
    }

    if (isNaN(caseId) || caseId < 0) {
      throw new ValidationError('Invalid case ID');
    }

    if (!resolution) {
      throw new ValidationError('Resolution is required');
    }

    // Verify protection case exists
    const protectionCase = await blockchainService.callContractMethod(
      'ProtectionManager',
      'getProtectionCase',
      [caseId]
    );

    console.log(`🔍 Protection case ${caseId} details:`, {
      id: protectionCase?.id?.toString(),
      status: protectionCase?.status?.toString(),
      statusValue: parseInt(protectionCase?.status?.toString() || '0'),
      claimantAddress: protectionCase?.claimantAddress,
      currentOwnerAddress: protectionCase?.currentOwnerAddress,
      patentId: protectionCase?.patentId?.toString()
    });

    if (!protectionCase || protectionCase.id.toString() !== id) {
      throw new NotFoundError('Protection case not found');
    }

    // Check if case is pending
    const statusValue = parseInt(protectionCase.status.toString());
    if (statusValue !== 0) { // 0 = pending
      console.error(`❌ Protection case ${caseId} is not pending. Current status: ${statusValue} (0=pending, 1=approved, 2=rejected, 3=resolved)`);
      throw new ValidationError('Protection case is not pending review');
    }

    // Get patent details to verify current ownership
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [protectionCase.patentId]
    );

    if (!patent || patent.id.toString() !== protectionCase.patentId.toString()) {
      throw new NotFoundError('Patent not found');
    }

    // Verify the current owner matches the protection case
    if (patent.uploaderAddress.toLowerCase() !== protectionCase.currentOwnerAddress.toLowerCase()) {
      throw new ValidationError('Patent ownership has changed since protection request was filed');
    }

    console.log(`Approving protection case ${caseId} and transferring patent ${protectionCase.patentId} ownership from ${patent.uploaderAddress} to ${protectionCase.claimantAddress}`);

    // Step 1: Approve protection request on blockchain
    const approvalResult = await blockchainService.callContractMethod(
      'ProtectionManager',
      'approveProtection',
      [caseId, resolution],
      { send: true, from: req.userAddress }
    );

    console.log(`Protection approved with transaction hash: ${approvalResult.transactionHash}`);

    // Step 2: Transfer patent ownership to the claimant
    try {
      const transferResult = await blockchainService.callContractMethod(
        'PatentRegistry',
        'transferOwnership',
        [protectionCase.patentId, protectionCase.claimantAddress],
        { send: true, from: req.userAddress }
      );

      console.log(`Patent ownership transferred with transaction hash: ${transferResult.transactionHash}`);

      // Step 3: Mark protection case as resolved with transfer transaction hash
      try {
        await blockchainService.callContractMethod(
          'ProtectionManager',
          'resolveProtection',
          [caseId, transferResult.transactionHash],
          { send: true, from: req.userAddress }
        );

        console.log(`Protection case marked as resolved`);
      } catch (resolveError) {
        console.warn('Failed to mark protection case as resolved, but ownership transfer succeeded:', resolveError);
        // Don't fail the request since the main goal (ownership transfer) succeeded
      }

      res.success({
        approvalTransactionHash: approvalResult.transactionHash,
        transferTransactionHash: transferResult.transactionHash,
        caseId: id,
        patentId: protectionCase.patentId.toString(),
        newOwner: protectionCase.claimantAddress,
        message: '维权申请已批准，专利所有权已转移给申请人'
      }, '维权申请已批准，专利所有权已转移');

    } catch (transferError) {
      console.error('Failed to transfer patent ownership after approval:', transferError);

      // Try to revert the approval if possible (though this might not always work)
      try {
        await blockchainService.callContractMethod(
          'ProtectionManager',
          'rejectProtection',
          [caseId, `系统错误：所有权转移失败 - ${transferError.message}`],
          { send: true, from: req.userAddress }
        );
        console.log('Reverted protection approval due to transfer failure');
      } catch (revertError) {
        console.error('Failed to revert protection approval:', revertError);
      }

      throw new Error(`维权申请已批准，但专利所有权转移失败：${transferError.message}`);
    }

  } catch (error) {
    console.error('Error approving protection request:', error);
    throw error;
  }
};

/**
 * Reject a protection request
 */
const rejectProtectionRequest = async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewerAddress, reason } = req.body;
    const caseId = parseInt(id);

    // Check authorization
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can reject protection requests');
    }

    if (isNaN(caseId) || caseId < 0) {
      throw new ValidationError('Invalid case ID');
    }

    if (!reason) {
      throw new ValidationError('Rejection reason is required');
    }

    // Verify protection case exists
    const protectionCase = await blockchainService.callContractMethod(
      'ProtectionManager',
      'getProtectionCase',
      [caseId]
    );

    console.log(`🔍 Protection case ${caseId} details (reject):`, {
      id: protectionCase?.id?.toString(),
      status: protectionCase?.status?.toString(),
      statusValue: parseInt(protectionCase?.status?.toString() || '0'),
      claimantAddress: protectionCase?.claimantAddress,
      currentOwnerAddress: protectionCase?.currentOwnerAddress,
      patentId: protectionCase?.patentId?.toString()
    });

    if (!protectionCase || protectionCase.id.toString() !== id) {
      throw new NotFoundError('Protection case not found');
    }

    // Check if case is pending
    const statusValue = parseInt(protectionCase.status.toString());
    if (statusValue !== 0) { // 0 = pending
      console.error(`❌ Protection case ${caseId} is not pending for rejection. Current status: ${statusValue} (0=pending, 1=approved, 2=rejected, 3=resolved)`);
      throw new ValidationError('Protection case is not pending review');
    }

    // Reject protection request on blockchain
    const result = await blockchainService.callContractMethod(
      'ProtectionManager',
      'rejectProtection',
      [caseId, reason],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      caseId: id,
      message: '维权申请已拒绝'
    }, '维权申请已拒绝');

  } catch (error) {
    console.error('Error rejecting protection request:', error);
    throw error;
  }
};

module.exports = {
  submitProtectionRequest,
  getUserProtectionCases,
  getPendingProtectionRequests,
  getPendingProtectionCases,
  approveProtectionRequest,
  rejectProtectionRequest,
  getProtectionCaseDetails
};
