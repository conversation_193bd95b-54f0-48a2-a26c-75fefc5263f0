const blockchainService = require('../services/blockchainService');
const { formatDate, formatAddress } = require('../middleware/responseFormatter');
const { AuthorizationError, NotFoundError, ConflictError, ValidationError } = require('../middleware/errorHandler');

/**
 * User Controller
 * Handles user management operations
 */

/**
 * Get user role by blockchain address
 */
const getUserRole = async (req, res) => {
  const { address } = req.params;

  try {
    // Get role from blockchain
    const role = await blockchainService.callContractMethod(
      'UserManagement',
      'getUserRole',
      [address]
    );

    // Get user profile if exists
    let userProfile = null;
    try {
      const profile = await blockchainService.callContractMethod(
        'UserManagement',
        'getUserProfile',
        [address]
      );
      userProfile = profile;
    } catch (error) {
      // User might not be registered yet
    }

    // Convert role number to string
    const roleMap = {
      0: 'user',
      1: 'reviewer', 
      2: 'admin'
    };

    const roleString = roleMap[role] || 'user';

    // Define permissions based on role
    const permissionsMap = {
      user: ['upload', 'search', 'trade', 'profile'],
      reviewer: ['upload', 'search', 'trade', 'profile', 'review', 'approve', 'reject'],
      admin: ['upload', 'search', 'trade', 'profile', 'review', 'approve', 'reject', 'manage_users', 'statistics']
    };

    const response = {
      address: formatAddress(address),
      role: roleString,
      permissions: permissionsMap[roleString],
      lastUpdated: userProfile ? formatDate(userProfile.registrationDate) : null
    };

    res.success(response, 'User role retrieved successfully');
  } catch (error) {
    console.error('Error getting user role:', error);
    throw error;
  }
};

/**
 * Update user role (admin only)
 */
const updateUserRole = async (req, res) => {
  const { address, role, updatedBy } = req.body;

  try {
    // Verify the updater is admin
    if (req.userAddress !== updatedBy) {
      throw new AuthorizationError('UpdatedBy address must match authenticated user');
    }

    // Convert role string to number
    const roleMap = {
      'user': 0,
      'reviewer': 1,
      'admin': 2
    };

    const roleNumber = roleMap[role];
    if (roleNumber === undefined) {
      throw new ValidationError('Invalid role specified');
    }

    // Update role in blockchain
    const tx = await blockchainService.callContractMethod(
      'UserManagement',
      'changeUserRole',
      [address, roleNumber],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: tx.transactionHash,
      address: formatAddress(address),
      newRole: role,
      updatedBy: formatAddress(updatedBy)
    }, 'User role updated successfully');
  } catch (error) {
    console.error('Error updating user role:', error);
    throw error;
  }
};

/**
 * Get user profile information
 */
const getUserProfile = async (req, res) => {
  const { address } = req.params;

  try {
    // Check if user is registered
    const isRegistered = await blockchainService.callContractMethod(
      'UserManagement',
      'registeredUsers',
      [address]
    );

    if (!isRegistered) {
      throw new NotFoundError('User not found');
    }

    // Get user profile from blockchain
    const profile = await blockchainService.callContractMethod(
      'UserManagement',
      'getUserProfile',
      [address]
    );

    // Get user role
    const role = await blockchainService.callContractMethod(
      'UserManagement',
      'getUserRole',
      [address]
    );

    const roleMap = {
      0: 'user',
      1: 'reviewer', 
      2: 'admin'
    };

    const response = {
      address: formatAddress(address),
      name: profile.name,
      phone: profile.phone,
      idNumber: profile.idNumber,
      role: roleMap[role] || 'user',
      registrationDate: formatDate(profile.registrationDate),
      lastLoginDate: formatDate(profile.lastLoginDate),
      status: profile.isActive ? 'active' : 'inactive'
    };

    res.success(response, 'User profile retrieved successfully');
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
};

/**
 * Update user profile
 */
const updateUserProfile = async (req, res) => {
  const { address } = req.params;
  const { name, phone, idNumber } = req.body;

  try {
    // Check authorization - user can update own profile or admin can update any
    if (req.userRole !== 'admin' && req.userAddress !== address) {
      throw new AuthorizationError('You can only update your own profile');
    }

    // Update profile in blockchain
    const tx = await blockchainService.callContractMethod(
      'UserManagement',
      'updateUserProfile',
      [address, name, phone, idNumber],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: tx.transactionHash,
      address: formatAddress(address),
      updatedFields: { name, phone, idNumber }
    }, 'User profile updated successfully');
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

/**
 * Register a new user
 */
const registerUser = async (req, res) => {
  const { name, phone, idNumber } = req.body;
  const userAddress = req.userAddress;

  try {
    // Check if user is already registered
    const isRegistered = await blockchainService.callContractMethod(
      'UserManagement',
      'registeredUsers',
      [userAddress]
    );

    if (isRegistered) {
      throw new ConflictError('User is already registered');
    }

    // Register user in blockchain
    const tx = await blockchainService.callContractMethod(
      'UserManagement',
      'registerUser',
      [userAddress, name, phone, idNumber],
      { send: true, from: userAddress }
    );

    res.created({
      transactionHash: tx.transactionHash,
      address: formatAddress(userAddress),
      name,
      role: 'user'
    }, 'User registered successfully');
  } catch (error) {
    console.error('Error registering user:', error);
    throw error;
  }
};

/**
 * Update last login time
 */
const updateLastLogin = async (req, res) => {
  const userAddress = req.userAddress;

  try {
    // Update last login time in blockchain
    const tx = await blockchainService.callContractMethod(
      'UserManagement',
      'updateLastLogin',
      [userAddress],
      { send: true, from: userAddress }
    );

    res.success({
      transactionHash: tx.transactionHash,
      address: formatAddress(userAddress),
      loginTime: new Date().toISOString()
    }, 'Last login time updated successfully');
  } catch (error) {
    console.error('Error updating last login:', error);
    throw error;
  }
};

/**
 * Get user permissions
 */
const getUserPermissions = async (req, res) => {
  const { address } = req.params;

  try {
    // Get user role
    const role = await blockchainService.callContractMethod(
      'UserManagement',
      'getUserRole',
      [address]
    );

    // Check if user is active
    const isActive = await blockchainService.callContractMethod(
      'UserManagement',
      'isUserActive',
      [address]
    );

    const roleMap = {
      0: 'user',
      1: 'reviewer', 
      2: 'admin'
    };

    const roleString = roleMap[role] || 'user';

    // Define permissions based on role
    const permissionsMap = {
      user: ['upload', 'search', 'trade', 'profile'],
      reviewer: ['upload', 'search', 'trade', 'profile', 'review', 'approve', 'reject'],
      admin: ['upload', 'search', 'trade', 'profile', 'review', 'approve', 'reject', 'manage_users', 'statistics']
    };

    const response = {
      address: formatAddress(address),
      role: roleString,
      isActive,
      permissions: isActive ? permissionsMap[roleString] : []
    };

    res.success(response, 'User permissions retrieved successfully');
  } catch (error) {
    console.error('Error getting user permissions:', error);
    throw error;
  }
};

/**
 * Update user status (activate/deactivate)
 */
const updateUserStatus = async (req, res) => {
  const { address } = req.params;
  const { isActive } = req.body;

  try {
    // Update user status in blockchain
    const tx = await blockchainService.callContractMethod(
      'UserManagement',
      'setUserStatus',
      [address, isActive],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: tx.transactionHash,
      address: formatAddress(address),
      status: isActive ? 'active' : 'inactive'
    }, `User ${isActive ? 'activated' : 'deactivated'} successfully`);
  } catch (error) {
    console.error('Error updating user status:', error);
    throw error;
  }
};

/**
 * Switch user context (for MetaMask user switching)
 * Returns user information and permissions for the specified address
 */
const switchUser = async (req, res) => {
  const { address } = req.params;

  try {
    // Validate address format
    if (!blockchainService.isValidAddress(address)) {
      throw new ValidationError('Invalid Ethereum address format');
    }

    // Check if user is registered
    const isRegistered = await blockchainService.callContractMethod(
      'UserManagement',
      'registeredUsers',
      [address]
    );

    if (!isRegistered) {
      // Return unregistered user info
      return res.success({
        address: formatAddress(address),
        isRegistered: false,
        role: 'unregistered',
        permissions: [],
        profile: null,
        needsRegistration: true
      }, 'User not registered - registration required');
    }

    // Get user profile
    const profile = await blockchainService.callContractMethod(
      'UserManagement',
      'getUserProfile',
      [address]
    );

    // Get user role
    const roleNumber = await blockchainService.callContractMethod(
      'UserManagement',
      'getUserRole',
      [address]
    );

    // Check if user is active
    const isActive = await blockchainService.callContractMethod(
      'UserManagement',
      'isUserActive',
      [address]
    );

    const roleMap = { 0: 'user', 1: 'reviewer', 2: 'admin' };
    const role = roleMap[roleNumber] || 'user';

    // Define permissions based on role
    const permissionsMap = {
      user: [
        'patents.view',
        'patents.search', 
        'patents.upload',
        'patents.own.manage',
        'transactions.initiate',
        'transactions.own.view',
        'profile.own.view',
        'profile.own.edit',
        'notifications.own.view'
      ],
      reviewer: [
        'patents.view',
        'patents.search',
        'patents.upload', 
        'patents.own.manage',
        'patents.review',
        'patents.approve',
        'patents.reject',
        'transactions.initiate',
        'transactions.own.view',
        'transactions.review',
        'uploads.review',
        'uploads.approve',
        'uploads.reject',
        'protection.review',
        'profile.own.view',
        'profile.own.edit',
        'notifications.own.view'
      ],
      admin: [
        'patents.view',
        'patents.search',
        'patents.upload',
        'patents.own.manage',
        'patents.review',
        'patents.approve',
        'patents.reject',
        'patents.all.manage',
        'transactions.initiate',
        'transactions.own.view',
        'transactions.review',
        'transactions.all.view',
        'uploads.review',
        'uploads.approve', 
        'uploads.reject',
        'protection.review',
        'protection.approve',
        'protection.reject',
        'users.view',
        'users.manage',
        'users.roles.edit',
        'statistics.view',
        'system.manage',
        'ganache.manage',
        'profile.own.view',
        'profile.own.edit',
        'profile.all.view',
        'profile.all.edit',
        'notifications.own.view',
        'notifications.all.view',
        'notifications.send'
      ]
    };

    const userInfo = {
      address: formatAddress(address),
      isRegistered: true,
      role,
      permissions: isActive ? permissionsMap[role] : [],
      profile: {
        name: profile.name,
        phone: profile.phone,
        idNumber: profile.idNumber,
        registrationDate: formatDate(profile.registrationDate),
        lastLoginDate: formatDate(profile.lastLoginDate),
        isActive
      },
      needsRegistration: false
    };

    // Update last login time
    try {
      await blockchainService.callContractMethod(
        'UserManagement',
        'updateLastLogin',
        [address],
        { send: true, from: address }
      );
    } catch (loginError) {
      console.warn('Failed to update last login time:', loginError.message);
      // Don't fail the switch operation if login update fails
    }

    res.success(userInfo, `Switched to user: ${profile.name} (${role})`);
  } catch (error) {
    console.error('Error switching user:', error);
    throw error;
  }
};

/**
 * Check if user has specific permission
 */
const checkPermission = async (req, res) => {
  const { address, permission } = req.params;

  try {
    // Get user info first
    const isRegistered = await blockchainService.callContractMethod(
      'UserManagement',
      'registeredUsers',
      [address]
    );

    if (!isRegistered) {
      return res.success({
        hasPermission: false,
        reason: 'User not registered'
      }, 'Permission check completed');
    }

    // Get user role
    const roleNumber = await blockchainService.callContractMethod(
      'UserManagement',
      'getUserRole',
      [address]
    );

    // Check if user is active
    const isActive = await blockchainService.callContractMethod(
      'UserManagement',
      'isUserActive',
      [address]
    );

    if (!isActive) {
      return res.success({
        hasPermission: false,
        reason: 'User account is inactive'
      }, 'Permission check completed');
    }

    const roleMap = { 0: 'user', 1: 'reviewer', 2: 'admin' };
    const role = roleMap[roleNumber] || 'user';

    // Define permissions (same as in switchUser)
    const permissionsMap = {
      user: [
        'patents.view', 'patents.search', 'patents.upload', 'patents.own.manage',
        'transactions.initiate', 'transactions.own.view',
        'profile.own.view', 'profile.own.edit', 'notifications.own.view'
      ],
      reviewer: [
        'patents.view', 'patents.search', 'patents.upload', 'patents.own.manage',
        'patents.review', 'patents.approve', 'patents.reject',
        'transactions.initiate', 'transactions.own.view', 'transactions.review',
        'uploads.review', 'uploads.approve', 'uploads.reject', 'protection.review',
        'profile.own.view', 'profile.own.edit', 'notifications.own.view'
      ],
      admin: [
        'patents.view', 'patents.search', 'patents.upload', 'patents.own.manage',
        'patents.review', 'patents.approve', 'patents.reject', 'patents.all.manage',
        'transactions.initiate', 'transactions.own.view', 'transactions.review', 'transactions.all.view',
        'uploads.review', 'uploads.approve', 'uploads.reject',
        'protection.review', 'protection.approve', 'protection.reject',
        'users.view', 'users.manage', 'users.roles.edit', 'statistics.view', 'system.manage', 'ganache.manage',
        'profile.own.view', 'profile.own.edit', 'profile.all.view', 'profile.all.edit',
        'notifications.own.view', 'notifications.all.view', 'notifications.send'
      ]
    };

    const userPermissions = permissionsMap[role] || [];
    const hasPermission = userPermissions.includes(permission);

    res.success({
      hasPermission,
      role,
      permission,
      reason: hasPermission ? 'Permission granted' : 'Permission denied'
    }, 'Permission check completed');

  } catch (error) {
    console.error('Error checking permission:', error);
    throw error;
  }
};

module.exports = {
  getUserRole,
  updateUserRole,
  getUserProfile,
  updateUserProfile,
  registerUser,
  updateLastLogin,
  getUserPermissions,
  updateUserStatus,
  switchUser,
  checkPermission
};
