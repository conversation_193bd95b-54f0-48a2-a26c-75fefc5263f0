const ipfsService = require('../services/ipfsService');
const { formatFileSize } = require('../middleware/responseFormatter');
const { ValidationError, IPFSError } = require('../middleware/errorHandler');

/**
 * IPFS Controller
 * Handles file operations with IPFS
 */

/**
 * Upload multiple files to IPFS
 */
const uploadFiles = async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      throw new ValidationError('No files provided');
    }

    const files = req.files.map(file => ({
      buffer: file.buffer,
      filename: file.originalname,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size
    }));

    const results = await ipfsService.uploadMultipleFiles(files);

    // Pin important files automatically
    for (const result of results) {
      await ipfsService.pinFile(result.hash).catch(error => {
        console.warn('Failed to pin file:', error);
      });
    }

    const response = {
      hashes: results.map(r => r.hash),
      urls: results.map(r => r.url),
      files: results.map(r => ({
        filename: r.filename,
        originalname: r.originalname,
        hash: r.hash,
        url: r.url,
        size: formatFileSize(r.size)
      }))
    };

    res.created(response, 'Files uploaded to IPFS successfully');
  } catch (error) {
    console.error('Error uploading files to IPFS:', error);
    if (error.message.includes('IPFS')) {
      throw new IPFSError(error.message);
    }
    throw error;
  }
};

/**
 * Upload a single file to IPFS
 */
const uploadSingleFile = async (req, res) => {
  try {
    if (!req.file) {
      throw new ValidationError('No file provided');
    }

    const result = await ipfsService.uploadFile(req.file.buffer, req.file.originalname);

    // Pin the file automatically
    await ipfsService.pinFile(result.hash).catch(error => {
      console.warn('Failed to pin file:', error);
    });

    const response = {
      hash: result.hash,
      url: result.url,
      filename: req.file.originalname,
      size: formatFileSize(result.size),
      mimetype: req.file.mimetype
    };

    res.created(response, 'File uploaded to IPFS successfully');
  } catch (error) {
    console.error('Error uploading file to IPFS:', error);
    if (error.message.includes('IPFS')) {
      throw new IPFSError(error.message);
    }
    throw error;
  }
};

/**
 * Download file from IPFS
 */
const downloadFile = async (req, res) => {
  const { hash } = req.params;

  try {
    // Validate IPFS hash
    if (!ipfsService.isValidHash(hash)) {
      throw new ValidationError('Invalid IPFS hash format');
    }

    const fileBuffer = await ipfsService.downloadFile(hash);

    // Set appropriate headers
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Disposition', `attachment; filename="${hash}"`);
    res.setHeader('Content-Length', fileBuffer.length);

    res.send(fileBuffer);
  } catch (error) {
    console.error('Error downloading file from IPFS:', error);
    if (error.message.includes('IPFS')) {
      throw new IPFSError(error.message);
    }
    throw error;
  }
};

/**
 * Get file information from IPFS
 */
const getFileInfo = async (req, res) => {
  const { hash } = req.params;

  try {
    // Validate IPFS hash
    if (!ipfsService.isValidHash(hash)) {
      throw new ValidationError('Invalid IPFS hash format');
    }

    const fileInfo = await ipfsService.getFileInfo(hash);

    const response = {
      hash: fileInfo.hash,
      size: formatFileSize(fileInfo.size),
      type: fileInfo.type,
      url: fileInfo.url,
      gatewayUrl: ipfsService.getGatewayUrl(hash)
    };

    res.success(response, 'File information retrieved successfully');
  } catch (error) {
    console.error('Error getting file info from IPFS:', error);
    if (error.message.includes('IPFS')) {
      throw new IPFSError(error.message);
    }
    throw error;
  }
};

/**
 * Pin file to IPFS
 */
const pinFile = async (req, res) => {
  const { hash } = req.params;

  try {
    // Validate IPFS hash
    if (!ipfsService.isValidHash(hash)) {
      throw new ValidationError('Invalid IPFS hash format');
    }

    const success = await ipfsService.pinFile(hash);

    if (success) {
      res.success({ hash, pinned: true }, 'File pinned successfully');
    } else {
      throw new IPFSError('Failed to pin file');
    }
  } catch (error) {
    console.error('Error pinning file:', error);
    if (error.message.includes('IPFS')) {
      throw new IPFSError(error.message);
    }
    throw error;
  }
};

/**
 * Unpin file from IPFS
 */
const unpinFile = async (req, res) => {
  const { hash } = req.params;

  try {
    // Validate IPFS hash
    if (!ipfsService.isValidHash(hash)) {
      throw new ValidationError('Invalid IPFS hash format');
    }

    const success = await ipfsService.unpinFile(hash);

    if (success) {
      res.success({ hash, pinned: false }, 'File unpinned successfully');
    } else {
      throw new IPFSError('Failed to unpin file');
    }
  } catch (error) {
    console.error('Error unpinning file:', error);
    if (error.message.includes('IPFS')) {
      throw new IPFSError(error.message);
    }
    throw error;
  }
};

/**
 * List pinned files
 */
const listPinnedFiles = async (req, res) => {
  try {
    const pinnedFiles = await ipfsService.listPinnedFiles();

    const response = {
      count: pinnedFiles.length,
      files: pinnedFiles.map(hash => ({
        hash,
        url: ipfsService.getGatewayUrl(hash)
      }))
    };

    res.success(response, 'Pinned files retrieved successfully');
  } catch (error) {
    console.error('Error listing pinned files:', error);
    if (error.message.includes('IPFS')) {
      throw new IPFSError(error.message);
    }
    throw error;
  }
};

/**
 * Upload JSON data to IPFS
 */
const uploadJSON = async (req, res) => {
  try {
    const { data, filename } = req.body;

    if (!data) {
      throw new ValidationError('JSON data is required');
    }

    const result = await ipfsService.uploadJSON(data, filename);

    // Pin the JSON file automatically
    await ipfsService.pinFile(result.hash).catch(error => {
      console.warn('Failed to pin JSON file:', error);
    });

    const response = {
      hash: result.hash,
      url: result.url,
      filename: filename || 'data.json',
      size: formatFileSize(result.size)
    };

    res.created(response, 'JSON data uploaded to IPFS successfully');
  } catch (error) {
    console.error('Error uploading JSON to IPFS:', error);
    if (error.message.includes('IPFS')) {
      throw new IPFSError(error.message);
    }
    throw error;
  }
};

/**
 * Download and parse JSON from IPFS
 */
const downloadJSON = async (req, res) => {
  const { hash } = req.params;

  try {
    // Validate IPFS hash
    if (!ipfsService.isValidHash(hash)) {
      throw new ValidationError('Invalid IPFS hash format');
    }

    const jsonData = await ipfsService.downloadJSON(hash);

    res.success(jsonData, 'JSON data retrieved from IPFS successfully');
  } catch (error) {
    console.error('Error downloading JSON from IPFS:', error);
    if (error.message.includes('IPFS')) {
      throw new IPFSError(error.message);
    }
    throw error;
  }
};

module.exports = {
  uploadFiles,
  uploadSingleFile,
  downloadFile,
  getFileInfo,
  pinFile,
  unpinFile,
  listPinnedFiles,
  uploadJSON,
  downloadJSON
};
