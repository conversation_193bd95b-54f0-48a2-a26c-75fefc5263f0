/**
 * Global error handling middleware
 */

const errorHandler = (error, req, res, next) => {
  console.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    userAddress: req.userAddress,
    timestamp: new Date().toISOString()
  });

  // Default error response
  let statusCode = 500;
  let errorResponse = {
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: 'An internal server error occurred'
    }
  };

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400;
    errorResponse.error = {
      code: 'VALIDATION_ERROR',
      message: error.message,
      details: error.details || {}
    };
  } else if (error.name === 'CastError') {
    statusCode = 400;
    errorResponse.error = {
      code: 'VALIDATION_ERROR',
      message: 'Invalid data format'
    };
  } else if (error.code === 11000) {
    // Duplicate key error (if using MongoDB)
    statusCode = 409;
    errorResponse.error = {
      code: 'CONFLICT',
      message: 'Resource already exists'
    };
  } else if (error.message.includes('User denied')) {
    statusCode = 401;
    errorResponse.error = {
      code: 'AUTHENTICATION_ERROR',
      message: 'User denied transaction'
    };
  } else if (error.message.includes('insufficient funds')) {
    statusCode = 400;
    errorResponse.error = {
      code: 'BLOCKCHAIN_ERROR',
      message: 'Insufficient funds for transaction'
    };
  } else if (error.message.includes('gas')) {
    statusCode = 400;
    errorResponse.error = {
      code: 'BLOCKCHAIN_ERROR',
      message: 'Transaction failed due to gas issues'
    };
  } else if (error.message.includes('revert')) {
    statusCode = 400;
    errorResponse.error = {
      code: 'BLOCKCHAIN_ERROR',
      message: 'Smart contract execution failed'
    };
  } else if (error.message.includes('IPFS')) {
    statusCode = 503;
    errorResponse.error = {
      code: 'IPFS_ERROR',
      message: 'IPFS service unavailable'
    };
  } else if (error.message.includes('network')) {
    statusCode = 503;
    errorResponse.error = {
      code: 'NETWORK_ERROR',
      message: 'Network connection failed'
    };
  } else if (error.code === 'ECONNREFUSED') {
    statusCode = 503;
    errorResponse.error = {
      code: 'SERVICE_UNAVAILABLE',
      message: 'External service unavailable'
    };
  } else if (error.code === 'ENOTFOUND') {
    statusCode = 503;
    errorResponse.error = {
      code: 'SERVICE_UNAVAILABLE',
      message: 'Service endpoint not found'
    };
  } else if (error.code === 'ETIMEDOUT') {
    statusCode = 408;
    errorResponse.error = {
      code: 'TIMEOUT_ERROR',
      message: 'Request timeout'
    };
  }

  // Handle custom application errors
  if (error.statusCode) {
    statusCode = error.statusCode;
  }

  if (error.code && error.message) {
    errorResponse.error = {
      code: error.code,
      message: error.message,
      details: error.details || {}
    };
  }

  // In development, include stack trace
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.stack = error.stack;
  }

  // Log critical errors
  if (statusCode >= 500) {
    console.error('Critical error:', {
      error: error.message,
      stack: error.stack,
      request: {
        method: req.method,
        url: req.url,
        headers: req.headers,
        body: req.body
      }
    });
  }

  res.status(statusCode).json(errorResponse);
};

// Async error wrapper
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Custom error classes
class AppError extends Error {
  constructor(message, statusCode, code = 'APPLICATION_ERROR', details = {}) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.name = 'AppError';
    
    Error.captureStackTrace(this, this.constructor);
  }
}

class ValidationError extends AppError {
  constructor(message, details = {}) {
    super(message, 400, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

class AuthorizationError extends AppError {
  constructor(message = 'Access denied') {
    super(message, 403, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

class NotFoundError extends AppError {
  constructor(message = 'Resource not found') {
    super(message, 404, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

class ConflictError extends AppError {
  constructor(message = 'Resource conflict') {
    super(message, 409, 'CONFLICT');
    this.name = 'ConflictError';
  }
}

class BlockchainError extends AppError {
  constructor(message = 'Blockchain operation failed') {
    super(message, 400, 'BLOCKCHAIN_ERROR');
    this.name = 'BlockchainError';
  }
}

class IPFSError extends AppError {
  constructor(message = 'IPFS operation failed') {
    super(message, 503, 'IPFS_ERROR');
    this.name = 'IPFSError';
  }
}

// 404 handler for undefined routes
const notFoundHandler = (req, res, next) => {
  const error = new NotFoundError(`Route ${req.method} ${req.originalUrl} not found`);
  next(error);
};

module.exports = {
  errorHandler,
  asyncHandler,
  notFoundHandler,
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  BlockchainError,
  IPFSError
};
