const { Web3 } = require('web3');
const fs = require('fs');
const path = require('path');

class BlockchainService {
  constructor() {
    this.web3 = null;
    this.contracts = {};
    this.isInitialized = false;
    this.contractAddresses = {};
  }

  async initialize() {
    try {
      // Initialize Web3
      const ganacheUrl = process.env.GANACHE_URL || 'http://127.0.0.1:7545';
      this.web3 = new Web3(ganacheUrl);
      
      // Test connection
      await this.web3.eth.net.isListening();
      console.log('✅ Web3 connected to Ganache');
      
      // Load contract addresses
      await this.loadContractAddresses();
      
      // Load and initialize contracts
      await this.loadContracts();
      
      this.isInitialized = true;
      console.log('✅ Blockchain service initialized');
      
    } catch (error) {
      console.error('❌ Failed to initialize blockchain service:', error);
      throw error;
    }
  }

  async loadContractAddresses() {
    try {
      const addressesPath = path.join(__dirname, '../../contract-addresses.json');
      if (fs.existsSync(addressesPath)) {
        const addressesData = fs.readFileSync(addressesPath, 'utf8');
        this.contractAddresses = JSON.parse(addressesData);
        console.log('📄 Contract addresses loaded');
      } else {
        console.warn('⚠️ Contract addresses file not found. Please deploy contracts first.');
        this.contractAddresses = {};
      }
    } catch (error) {
      console.error('❌ Failed to load contract addresses:', error);
      this.contractAddresses = {};
    }
  }

  async loadContracts() {
    try {
      const contractNames = [
        'UserManagement',
        'PatentRegistry', 
        'TransactionManager',
        'ProtectionManager',
        'NotificationSystem'
      ];

      for (const contractName of contractNames) {
        await this.loadContract(contractName);
      }
      
      console.log('📄 All contracts loaded');
    } catch (error) {
      console.error('❌ Failed to load contracts:', error);
      throw error;
    }
  }

  async loadContract(contractName) {
    try {
      // Load ABI
      const abiPath = path.join(__dirname, `../../build/contracts/${contractName}.json`);
      if (!fs.existsSync(abiPath)) {
        console.warn(`⚠️ ABI file not found for ${contractName}. Please compile contracts first.`);
        return;
      }

      const contractData = JSON.parse(fs.readFileSync(abiPath, 'utf8'));
      const abi = contractData.abi;
      
      // Get contract address
      const address = this.contractAddresses[contractName];
      if (!address) {
        console.warn(`⚠️ Address not found for ${contractName}. Please deploy contracts first.`);
        return;
      }

      // Create contract instance
      this.contracts[contractName] = new this.web3.eth.Contract(abi, address);
      console.log(`✅ ${contractName} contract loaded at ${address}`);
      
    } catch (error) {
      console.error(`❌ Failed to load ${contractName} contract:`, error);
    }
  }

  isConnected() {
    return this.isInitialized && this.web3 !== null;
  }

  getContract(contractName) {
    if (!this.contracts[contractName]) {
      throw new Error(`Contract ${contractName} not loaded`);
    }
    return this.contracts[contractName];
  }

  async getAccounts() {
    if (!this.web3) throw new Error('Web3 not initialized');
    return await this.web3.eth.getAccounts();
  }

  async getBalance(address) {
    if (!this.web3) throw new Error('Web3 not initialized');
    const balance = await this.web3.eth.getBalance(address);
    return this.web3.utils.fromWei(balance, 'ether');
  }

  async sendTransaction(from, to, value, data = '0x') {
    if (!this.web3) throw new Error('Web3 not initialized');
    
    const gasPrice = await this.web3.eth.getGasPrice();
    const gasEstimate = await this.web3.eth.estimateGas({
      from,
      to,
      value,
      data
    });

    return await this.web3.eth.sendTransaction({
      from,
      to,
      value,
      data,
      gas: gasEstimate,
      gasPrice
    });
  }

  async callContractMethod(contractName, methodName, params = [], options = {}) {
    try {
      const contract = this.getContract(contractName);
      const method = contract.methods[methodName](...params);
      
      if (options.send) {
        // Send transaction (state-changing)
        const accounts = await this.getAccounts();
        const from = options.from || accounts[0];
        
        const gasEstimate = await method.estimateGas({ from });
        const gasPrice = await this.web3.eth.getGasPrice();
        
        return await method.send({
          from,
          gas: gasEstimate,
          gasPrice,
          ...options
        });
      } else {
        // Call method (read-only)
        return await method.call(options);
      }
    } catch (error) {
      console.error(`❌ Contract method call failed: ${contractName}.${methodName}`, error);
      throw error;
    }
  }

  async getTransactionReceipt(txHash) {
    if (!this.web3) throw new Error('Web3 not initialized');
    return await this.web3.eth.getTransactionReceipt(txHash);
  }

  async getCurrentBlock() {
    if (!this.web3) throw new Error('Web3 not initialized');
    return await this.web3.eth.getBlockNumber();
  }

  async getBlock(blockNumber) {
    if (!this.web3) throw new Error('Web3 not initialized');
    return await this.web3.eth.getBlock(blockNumber);
  }

  // Utility methods for address validation
  isValidAddress(address) {
    if (!this.web3) return false;
    return this.web3.utils.isAddress(address);
  }

  toChecksumAddress(address) {
    if (!this.web3) throw new Error('Web3 not initialized');
    return this.web3.utils.toChecksumAddress(address);
  }

  // Event listening
  async subscribeToEvents(contractName, eventName, callback, fromBlock = 'latest') {
    try {
      const contract = this.getContract(contractName);
      const subscription = contract.events[eventName]({
        fromBlock
      });
      
      subscription.on('data', callback);
      subscription.on('error', (error) => {
        console.error(`❌ Event subscription error: ${contractName}.${eventName}`, error);
      });
      
      return subscription;
    } catch (error) {
      console.error(`❌ Failed to subscribe to events: ${contractName}.${eventName}`, error);
      throw error;
    }
  }

  // Get past events
  async getPastEvents(contractName, eventName, options = {}) {
    try {
      const contract = this.getContract(contractName);
      return await contract.getPastEvents(eventName, {
        fromBlock: 0,
        toBlock: 'latest',
        ...options
      });
    } catch (error) {
      console.error(`❌ Failed to get past events: ${contractName}.${eventName}`, error);
      throw error;
    }
  }

  /**
   * Auto-register all Ganache accounts to the blockchain
   * This is called during server startup to ensure all accounts are available for testing
   */
  async autoRegisterGanacheAccounts() {
    try {
      console.log('🔄 Starting auto-registration of Ganache accounts...');

      // Load configuration
      let config;
      try {
        const configPath = path.join(__dirname, '../../ganache-accounts-config.json');
        if (fs.existsSync(configPath)) {
          const configData = fs.readFileSync(configPath, 'utf8');
          config = JSON.parse(configData);
        } else {
          console.warn('⚠️ Ganache accounts config file not found, using defaults');
          config = { autoRegister: true, defaultAccounts: [], defaultRoleForOthers: 'user' };
        }
      } catch (error) {
        console.error('❌ Failed to load Ganache accounts config:', error);
        config = { autoRegister: true, defaultAccounts: [], defaultRoleForOthers: 'user' };
      }

      // Check if auto-registration is enabled
      if (!config.autoRegister) {
        console.log('ℹ️ Auto-registration is disabled in config');
        return;
      }
      
      // Get all accounts from Ganache
      const accounts = await this.getAccounts();
      
      if (!accounts || accounts.length === 0) {
        console.warn('⚠️ No accounts found in Ganache');
        return;
      }

      console.log(`📊 Found ${accounts.length} accounts in Ganache`);

      // Check if UserManagement contract is available
      if (!this.contracts.UserManagement) {
        console.warn('⚠️ UserManagement contract not available, skipping auto-registration');
        return;
      }

      // Quick check: see if all accounts are already registered
      console.log('🔍 Checking if accounts are already registered...');
      let alreadyRegisteredCount = 0;
      let unregisteredAccounts = [];
      
      for (let i = 0; i < accounts.length; i++) {
        const account = accounts[i];
        try {
          const isRegistered = await this.callContractMethod(
            'UserManagement',
            'registeredUsers',
            [account]
          );

          if (isRegistered) {
            alreadyRegisteredCount++;
          } else {
            unregisteredAccounts.push({ index: i, address: account });
          }
        } catch (error) {
          console.warn(`⚠️ Could not check registration status for account ${i}:`, error.message);
          // Assume unregistered if we can't check
          unregisteredAccounts.push({ index: i, address: account });
        }
      }

      // If all accounts are registered, skip the registration process
      if (unregisteredAccounts.length === 0) {
        console.log(`✅ All ${accounts.length} accounts are already registered, skipping auto-registration`);
        console.log(`📈 Registration status: ${alreadyRegisteredCount}/${accounts.length} registered`);
        return;
      }

      console.log(`📈 Found ${alreadyRegisteredCount} already registered, ${unregisteredAccounts.length} need registration`);

      let newlyRegisteredCount = 0;
      
      // Register only unregistered accounts
      for (const { index: i, address: account } of unregisteredAccounts) {
        try {
          // Get user data and role from config
          const configAccount = config.defaultAccounts?.find(acc => acc.index === i);
          let userData, role;

          if (configAccount) {
            // Use configured data
            userData = configAccount.userData;
            const roleMap = { 'user': 0, 'reviewer': 1, 'admin': 2 };
            role = roleMap[configAccount.role] || 0;
          } else {
            // Use template for other accounts
            const template = config.userDataTemplate || {};
            userData = {
              name: `${template.namePrefix || 'Ganache User'} ${i}`,
              phone: `${template.phonePrefix || '**********'}${i.toString().padStart(2, '0')}`,
              idNumber: `${template.idPrefix || 'ID'}${Date.now()}${i.toString().padStart(3, '0')}`
            };
            const roleMap = { 'user': 0, 'reviewer': 1, 'admin': 2 };
            role = roleMap[config.defaultRoleForOthers] || 0;
          }

          // Register the user
          await this.callContractMethod(
            'UserManagement',
            'registerUser',
            [account, userData.name, userData.phone, userData.idNumber],
            { send: true, from: account }
          );

          // Set role if not regular user
          if (role > 0) {
            await this.callContractMethod(
              'UserManagement',
              'changeUserRole',
              [account, role],
              { send: true, from: accounts[0] } // Use first account (admin) to set roles
            );
          }

          const roleNames = ['用户', '审查员', '管理员'];
          console.log(`✅ Account ${i}: ${account} registered as ${roleNames[role]} (${userData.name})`);
          newlyRegisteredCount++;

        } catch (error) {
          console.error(`❌ Failed to register account ${i} (${account}):`, error.message);
          // Continue with next account even if one fails
        }

        // Add small delay to avoid overwhelming the network
        if (unregisteredAccounts.indexOf(unregisteredAccounts.find(item => item.address === account)) < unregisteredAccounts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      console.log(`🎉 Auto-registration completed:`);
      console.log(`   - Newly registered: ${newlyRegisteredCount}`);
      console.log(`   - Already registered: ${alreadyRegisteredCount}`);
      console.log(`   - Total accounts: ${accounts.length}`);

    } catch (error) {
      console.error('❌ Failed to auto-register Ganache accounts:', error);
      // Don't throw error to prevent server startup failure
    }
  }
}

// Create singleton instance
const blockchainService = new BlockchainService();

module.exports = blockchainService;
