const express = require('express');
const router = express.Router();

// Import middleware
const { 
  extractUserAddress, 
  verifyActiveUser, 
  getUserRole, 
  requireReviewerOrAdmin
} = require('../middleware/authMiddleware');
const { validationRules, handleValidationErrors } = require('../middleware/validationMiddleware');
const { asyncHandler } = require('../middleware/errorHandler');

// Import controllers
const reviewController = require('../controllers/reviewController');

/**
 * @route GET /api/review/uploads/pending
 * @desc Get pending patent uploads for review (reviewer/admin only)
 * @access Reviewer/Admin
 */
router.get('/uploads/pending',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireReviewerOrAdmin,
  validationRules.pagination(),
  handleValidationErrors,
  asyncHandler(reviewController.getPendingUploads)
);

/**
 * @route PUT /api/review/uploads/:id/approve
 * @desc Approve a patent upload (reviewer/admin only)
 * @access Reviewer/Admin
 */
router.put('/uploads/:id/approve',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireReviewerOrAdmin,
  validationRules.reviewAction(),
  handleValidationErrors,
  asyncHandler(reviewController.approveUpload)
);

/**
 * @route PUT /api/review/uploads/:id/reject
 * @desc Reject a patent upload (reviewer/admin only)
 * @access Reviewer/Admin
 */
router.put('/uploads/:id/reject',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireReviewerOrAdmin,
  validationRules.reviewReject(),
  handleValidationErrors,
  asyncHandler(reviewController.rejectUpload)
);

/**
 * @route GET /api/review/uploads/:id
 * @desc Get upload details for review
 * @access Reviewer/Admin
 */
router.get('/uploads/:id',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireReviewerOrAdmin,
  asyncHandler(reviewController.getUploadDetails)
);

/**
 * @route GET /api/review/statistics
 * @desc Get review statistics (reviewer/admin only)
 * @access Reviewer/Admin
 */
router.get('/statistics',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireReviewerOrAdmin,
  asyncHandler(reviewController.getReviewStatistics)
);

module.exports = router;
