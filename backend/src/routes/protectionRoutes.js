const express = require('express');
const router = express.Router();

// Import middleware
const { 
  extractUserAddress, 
  verifyActiveUser, 
  getUserRole, 
  requireReviewerOrAdmin
} = require('../middleware/authMiddleware');
const { validationRules, handleValidationErrors } = require('../middleware/validationMiddleware');
const { asyncHandler } = require('../middleware/errorHandler');

// Import controllers
const protectionController = require('../controllers/protectionController');

/**
 * @route POST /api/protection/request
 * @desc Submit a rights protection request
 * @access Authenticated users
 */
router.post('/request',
  extractUserAddress,
  verifyActiveUser,
  validationRules.protectionRequest(),
  handleValidationErrors,
  asyncHandler(protectionController.submitProtectionRequest)
);

/**
 * @route GET /api/protection/pending
 * @desc Get pending protection requests (reviewer/admin only)
 * @access Reviewer/Admin
 */
router.get('/pending',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireReviewerOrAdmin,
  validationRules.pagination(),
  handleValidationErrors,
  asyncHandler(protectionController.getPendingProtectionRequests)
);

/**
 * @route PUT /api/protection/:id/approve
 * @desc Approve a protection request (reviewer/admin only)
 * @access Reviewer/Admin
 */
router.put('/:id/approve',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireReviewerOrAdmin,
  validationRules.protectionApprove(),
  handleValidationErrors,
  asyncHandler(protectionController.approveProtectionRequest)
);

/**
 * @route PUT /api/protection/:id/reject
 * @desc Reject a protection request (reviewer/admin only)
 * @access Reviewer/Admin
 */
router.put('/:id/reject',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireReviewerOrAdmin,
  validationRules.reviewReject(),
  handleValidationErrors,
  asyncHandler(protectionController.rejectProtectionRequest)
);

/**
 * @route GET /api/protection/cases/pending
 * @desc Get pending protection cases for review
 * @access Reviewer/Admin
 */
router.get('/cases/pending',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireReviewerOrAdmin,
  validationRules.pagination(),
  handleValidationErrors,
  asyncHandler(protectionController.getPendingProtectionCases)
);

/**
 * @route GET /api/protection/my-cases
 * @desc Get user's protection cases
 * @access Authenticated users
 */
router.get('/my-cases',
  extractUserAddress,
  verifyActiveUser,
  validationRules.pagination(),
  handleValidationErrors,
  asyncHandler(protectionController.getUserProtectionCases)
);

/**
 * @route GET /api/protection/:id
 * @desc Get protection case details
 * @access Claimant, Current Owner, or Reviewer/Admin
 */
router.get('/:id',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  asyncHandler(protectionController.getProtectionCaseDetails)
);

module.exports = router;
