const express = require('express');
const router = express.Router();

// Import middleware
const { 
  extractUserAddress, 
  verifyActiveUser, 
  getUserRole, 
  requireAdmin,
  validateAddressParam,
  updateLastLogin
} = require('../middleware/authMiddleware');
const { validationRules, handleValidationErrors } = require('../middleware/validationMiddleware');
const { asyncHandler } = require('../middleware/errorHandler');

// Import controllers
const userController = require('../controllers/userController');

/**
 * @route GET /api/user/role/:address
 * @desc Get user role by blockchain address
 * @access Public
 */
router.get('/role/:address', 
  validateAddressParam('address'),
  async<PERSON>and<PERSON>(userController.getUserRole)
);

/**
 * @route POST /api/user/role
 * @desc Update user role (admin only)
 * @access Admin
 */
router.post('/role',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireAdmin,
  validationRules.userRole(),
  handleValidation<PERSON>rrors,
  as<PERSON><PERSON><PERSON><PERSON>(userController.updateUserRole)
);

/**
 * @route GET /api/user/profile/:address
 * @desc Get user profile information
 * @access Public
 */
router.get('/profile/:address',
  validateAddressParam('address'),
  asyncHandler(userController.getUserProfile)
);

/**
 * @route PUT /api/user/profile/:address
 * @desc Update user profile
 * @access User (own profile) or Admin
 */
router.put('/profile/:address',
  validateAddressParam('address'),
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  validationRules.userProfile(),
  handleValidationErrors,
  asyncHandler(userController.updateUserProfile)
);

/**
 * @route POST /api/user/register
 * @desc Register a new user
 * @access Public
 */
router.post('/register',
  extractUserAddress,
  validationRules.userProfile(),
  handleValidationErrors,
  asyncHandler(userController.registerUser)
);

/**
 * @route POST /api/user/login
 * @desc Update last login time
 * @access User
 */
router.post('/login',
  extractUserAddress,
  verifyActiveUser,
  updateLastLogin,
  asyncHandler(userController.updateLastLogin)
);

/**
 * @route GET /api/user/permissions/:address
 * @desc Get user permissions
 * @access Public
 */
router.get('/permissions/:address',
  validateAddressParam('address'),
  asyncHandler(userController.getUserPermissions)
);

/**
 * @route PUT /api/user/status/:address
 * @desc Activate or deactivate a user (admin only)
 * @access Admin
 */
router.put('/status/:address',
  validateAddressParam('address'),
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireAdmin,
  asyncHandler(userController.updateUserStatus)
);

/**
 * @route POST /api/user/switch/:address
 * @desc Switch user context (for MetaMask user switching)
 * @access Public
 */
router.post('/switch/:address',
  validateAddressParam('address'),
  asyncHandler(userController.switchUser)
);

/**
 * @route GET /api/user/permission/:address/:permission
 * @desc Check if user has specific permission
 * @access Public
 */
router.get('/permission/:address/:permission',
  validateAddressParam('address'),
  asyncHandler(userController.checkPermission)
);

module.exports = router;
