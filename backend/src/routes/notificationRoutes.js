const express = require('express');
const router = express.Router();

// Import middleware
const { 
  extractUserAddress, 
  verifyActiveUser, 
  getUserRole, 
  requireAdmin,
  validateAddressParam
} = require('../middleware/authMiddleware');
const { validationRules, handleValidationErrors } = require('../middleware/validationMiddleware');
const { asyncHandler } = require('../middleware/errorHandler');

// Import controllers
const notificationController = require('../controllers/notificationController');

/**
 * @route GET /api/notifications/:address
 * @desc Get user's notifications
 * @access User (own notifications) or Admin
 */
router.get('/:address',
  validateAddressParam('address'),
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  validationRules.pagination(),
  handleValidationErrors,
  asyncHandler(notificationController.getUserNotifications)
);

/**
 * @route PUT /api/notifications/:id/read
 * @desc Mark notification as read
 * @access Notification recipient
 */
router.put('/:id/read',
  extractUserAddress,
  verifyActiveUser,
  as<PERSON><PERSON><PERSON><PERSON>(notificationController.markNotificationAsRead)
);

/**
 * @route PUT /api/notifications/:address/read-all
 * @desc Mark all notifications as read for a user
 * @access User (own notifications) or Admin
 */
router.put('/:address/read-all',
  validateAddressParam('address'),
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  asyncHandler(notificationController.markAllNotificationsAsRead)
);

/**
 * @route POST /api/notifications/send
 * @desc Send notification to user (system use)
 * @access Admin
 */
router.post('/send',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireAdmin,
  validationRules.notification(),
  handleValidationErrors,
  asyncHandler(notificationController.sendNotification)
);

/**
 * @route GET /api/notifications/:address/unread-count
 * @desc Get unread notification count for user
 * @access User (own notifications) or Admin
 */
router.get('/:address/unread-count',
  validateAddressParam('address'),
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  asyncHandler(notificationController.getUnreadCount)
);

module.exports = router;
