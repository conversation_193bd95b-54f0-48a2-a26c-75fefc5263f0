const express = require('express');
const multer = require('multer');
const router = express.Router();

// Import middleware
const { 
  extractUserAddress, 
  verifyActiveUser, 
  optionalAuth
} = require('../middleware/authMiddleware');
const { asyncHandler } = require('../middleware/errorHandler');

// Import controllers
const ipfsController = require('../controllers/ipfsController');

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB default
    files: 10 // Maximum 10 files per request
  },
  fileFilter: (req, file, cb) => {
    // Check file type
    const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'pdf,doc,docx,jpg,jpeg,png').split(',');
    const fileExtension = file.originalname.split('.').pop().toLowerCase();
    
    if (allowedTypes.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error(`File type .${fileExtension} is not allowed. Allowed types: ${allowedTypes.join(', ')}`), false);
    }
  }
});

/**
 * @route POST /api/ipfs/upload
 * @desc Upload files to IPFS
 * @access Authenticated users
 */
router.post('/upload',
  extractUserAddress,
  verifyActiveUser,
  upload.array('files', 10), // Allow up to 10 files
  asyncHandler(ipfsController.uploadFiles)
);

/**
 * @route POST /api/ipfs/upload-single
 * @desc Upload a single file to IPFS
 * @access Authenticated users
 */
router.post('/upload-single',
  extractUserAddress,
  verifyActiveUser,
  upload.single('file'),
  asyncHandler(ipfsController.uploadSingleFile)
);

/**
 * @route GET /api/ipfs/download/:hash
 * @desc Download file from IPFS
 * @access Public (with optional auth for logging)
 */
router.get('/download/:hash',
  optionalAuth,
  asyncHandler(ipfsController.downloadFile)
);

/**
 * @route GET /api/ipfs/info/:hash
 * @desc Get file information from IPFS
 * @access Public
 */
router.get('/info/:hash',
  asyncHandler(ipfsController.getFileInfo)
);

/**
 * @route POST /api/ipfs/pin/:hash
 * @desc Pin file to IPFS (prevent garbage collection)
 * @access Authenticated users
 */
router.post('/pin/:hash',
  extractUserAddress,
  verifyActiveUser,
  asyncHandler(ipfsController.pinFile)
);

/**
 * @route DELETE /api/ipfs/pin/:hash
 * @desc Unpin file from IPFS
 * @access Authenticated users
 */
router.delete('/pin/:hash',
  extractUserAddress,
  verifyActiveUser,
  asyncHandler(ipfsController.unpinFile)
);

/**
 * @route GET /api/ipfs/pinned
 * @desc List pinned files
 * @access Authenticated users
 */
router.get('/pinned',
  extractUserAddress,
  verifyActiveUser,
  asyncHandler(ipfsController.listPinnedFiles)
);

/**
 * @route POST /api/ipfs/upload-json
 * @desc Upload JSON data to IPFS
 * @access Authenticated users
 */
router.post('/upload-json',
  extractUserAddress,
  verifyActiveUser,
  asyncHandler(ipfsController.uploadJSON)
);

/**
 * @route GET /api/ipfs/download-json/:hash
 * @desc Download and parse JSON from IPFS
 * @access Public (with optional auth for logging)
 */
router.get('/download-json/:hash',
  optionalAuth,
  asyncHandler(ipfsController.downloadJSON)
);

module.exports = router;
