const { Web3 } = require('web3');
const fs = require('fs');
const path = require('path');

// Initialize Web3
const web3 = new Web3('http://127.0.0.1:7545');

async function quickRegisterUser(userAddress = null, userName = null) {
  try {
    console.log('⚡ Quick User Registration Tool\n');

    // Get user address from command line or prompt
    if (!userAddress) {
      const args = process.argv.slice(2);
      if (args.length > 0) {
        userAddress = args[0];
        userName = args[1] || `Quick User ${Date.now()}`;
      } else {
        console.log('Usage: node quick-register-user.js <userAddress> [userName]');
        console.log('Example: node quick-register-user.js 0x1234... "Test User"');
        return;
      }
    }

    if (!web3.utils.isAddress(userAddress)) {
      console.error('❌ Invalid Ethereum address format');
      return;
    }

    // Load contract addresses and ABI
    const contractAddresses = JSON.parse(fs.readFileSync(path.join(__dirname, 'contract-addresses.json'), 'utf8'));
    const userManagementABI = JSON.parse(fs.readFileSync(path.join(__dirname, 'build/contracts/UserManagement.json'), 'utf8')).abi;
    const userManagement = new web3.eth.Contract(userManagementABI, contractAddresses.UserManagement);

    // Check if already registered
    const isRegistered = await userManagement.methods.registeredUsers(userAddress).call();
    if (isRegistered) {
      console.log('✅ User is already registered!');
      
      // Show user info
      const profile = await userManagement.methods.getUserProfile(userAddress).call();
      const role = await userManagement.methods.getUserRole(userAddress).call();
      const roleNames = ['User', 'Reviewer', 'Admin'];
      
      console.log(`📋 User Info:`);
      console.log(`   Address: ${userAddress}`);
      console.log(`   Name: ${profile.name}`);
      console.log(`   Role: ${roleNames[role] || 'Unknown'}`);
      console.log(`   Active: ${profile.isActive}`);
      console.log(`   Phone: ${profile.phone}`);
      console.log(`   ID: ${profile.idNumber}`);
      return;
    }

    console.log(`👤 Registering user: ${userAddress}`);
    console.log(`📝 Name: ${userName}`);

    // Register the user
    const tx = await userManagement.methods.registerUser(
      userAddress,
      userName,
      `138000000${Math.floor(Math.random() * 100).toString().padStart(2, '0')}`, // Random phone
      `ID${Date.now()}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}` // Random ID
    ).send({ 
      from: userAddress, 
      gas: 1000000,
      gasPrice: await web3.eth.getGasPrice()
    });

    console.log(`✅ User registered successfully!`);
    console.log(`📋 Transaction Hash: ${tx.transactionHash}`);
    console.log(`⛽ Gas Used: ${tx.gasUsed}`);

    // Verify registration
    const isNowRegistered = await userManagement.methods.registeredUsers(userAddress).call();
    console.log(`✅ Registration verified: ${isNowRegistered}`);

  } catch (error) {
    console.error('❌ Registration failed:', error.message);
    
    if (error.message.includes('insufficient funds')) {
      console.log('💡 Try ensuring the account has enough ETH for gas fees');
    } else if (error.message.includes('User already registered')) {
      console.log('💡 User is already registered');
    }
  }
}

// Run if called directly
if (require.main === module) {
  quickRegisterUser();
}

module.exports = quickRegisterUser; 