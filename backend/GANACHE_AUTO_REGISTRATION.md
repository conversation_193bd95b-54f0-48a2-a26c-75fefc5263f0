# Ganache账户自动注册功能

## 概述

在开发和测试环境中，为了方便使用Ganache提供的测试账户，系统实现了自动注册功能。当后端服务启动时，会自动将所有Ganache账户注册到区块链上，并根据配置分配相应的角色。

## 功能特点

- ✅ **自动检测**：启动时自动检测Ganache中的所有账户
- ✅ **智能注册**：只注册未注册的账户，避免重复注册
- ✅ **角色分配**：根据配置自动分配用户角色（管理员、审查员、普通用户）
- ✅ **配置灵活**：通过配置文件自定义账户信息和角色分配
- ✅ **错误容忍**：单个账户注册失败不影响其他账户和服务启动
- ✅ **详细日志**：提供详细的注册过程日志

## 配置文件

配置文件位置：`backend/ganache-accounts-config.json`

### 配置项说明

```json
{
  "autoRegister": true,                 // 是否启用自动注册
  "defaultAccounts": [                  // 特定账户的配置
    {
      "index": 0,                       // Ganache账户索引
      "role": "admin",                  // 账户角色：admin/reviewer/user
      "userData": {
        "name": "系统管理员",
        "phone": "*********00",
        "idNumber": "ADMIN001"
      }
    }
  ],
  "defaultRoleForOthers": "user",       // 其他账户的默认角色
  "userDataTemplate": {                 // 其他账户的信息模板
    "namePrefix": "测试用户",
    "phonePrefix": "*********",
    "idPrefix": "TEST"
  }
}
```

### 角色类型

- **admin**: 管理员 - 拥有所有权限
- **reviewer**: 审查员 - 可以审查和批准专利
- **user**: 普通用户 - 基础用户权限

## 使用方法

### 1. 启动Ganache

确保Ganache在指定端口（默认7545）运行，并有可用的测试账户。

### 2. 配置账户信息

编辑 `ganache-accounts-config.json` 文件，设置所需的账户角色和信息：

```json
{
  "autoRegister": true,
  "defaultAccounts": [
    {
      "index": 0,
      "role": "admin",
      "userData": {
        "name": "张三",
        "phone": "***********",
        "idNumber": "ADMIN001"
      }
    },
    {
      "index": 1,
      "role": "reviewer",
      "userData": {
        "name": "李四",
        "phone": "***********",
        "idNumber": "REV001"
      }
    }
  ],
  "defaultRoleForOthers": "user",
  "userDataTemplate": {
    "namePrefix": "用户",
    "phonePrefix": "*********",
    "idPrefix": "USER"
  }
}
```

### 3. 启动后端服务

运行后端服务，自动注册功能会在初始化时执行：

```bash
cd backend
npm start
```

### 4. 查看注册日志

服务启动时会显示详细的注册日志：

```
🔄 Starting auto-registration of Ganache accounts...
📊 Found 10 accounts in Ganache
✅ Account 0: 0x... registered as 管理员 (张三)
✅ Account 1: 0x... registered as 审查员 (李四)
✅ Account 2: 0x... registered as 用户 (用户 2)
...
🎉 Auto-registration completed:
   - Newly registered: 8
   - Already registered: 2
   - Total accounts: 10
```

## 禁用自动注册

如果不需要自动注册功能，可以在配置文件中设置：

```json
{
  "autoRegister": false
}
```

或者删除配置文件，系统会跳过自动注册。

## 注意事项

1. **合约部署**：确保UserManagement合约已正确部署且地址已配置
2. **账户权限**：第一个账户（index 0）需要有管理员权限来设置其他账户的角色
3. **网络延迟**：注册过程中有小延迟避免网络拥堵
4. **错误处理**：单个账户注册失败不会影响服务启动
5. **重复注册**：已注册的账户会被跳过，不会重复注册

## 故障排除

### 问题1：UserManagement合约不可用
```
⚠️ UserManagement contract not available, skipping auto-registration
```
**解决方案**：确保合约已部署且地址已正确配置在 `contract-addresses.json` 文件中。

### 问题2：账户注册失败
```
❌ Failed to register account 1 (0x...): execution reverted
```
**解决方案**：检查账户是否有足够的gas费用，或者合约方法调用是否正确。

### 问题3：无法获取Ganache账户
```
⚠️ No accounts found in Ganache
```
**解决方案**：确保Ganache正在运行且网络连接正常。

## 开发建议

- 在开发环境中使用自动注册功能可以快速获得可用的测试账户
- 生产环境中建议禁用此功能，使用正式的用户注册流程
- 定期清理测试数据和重新部署合约时，自动注册功能可以快速恢复测试环境 