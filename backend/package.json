{"name": "patent-exchange-backend", "version": "1.0.0", "description": "Blockchain-based patent exchange platform backend", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:ganache-registration": "node scripts/test-ganache-registration.js", "compile-contracts": "truffle compile", "migrate": "truffle migrate", "migrate:reset": "truffle migrate --reset", "ganache": "ganache-cli -p 7545 -h 0.0.0.0 --deterministic --accounts 10 --defaultBalanceEther 100", "deploy": "npm run compile-contracts && npm run migrate"}, "keywords": ["blockchain", "patent", "exchange", "ethereum", "ipfs", "smart-contracts"], "author": "Patent Exchange Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "form-data": "^4.0.2", "helmet": "^7.1.0", "ipfs-http-client": "^60.0.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "web3": "^4.3.0"}, "devDependencies": {"@truffle/hdwallet-provider": "^2.1.15", "ganache-cli": "^6.12.2", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "truffle": "^5.11.5"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}