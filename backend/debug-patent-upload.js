const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

// Test patent upload validation
async function testPatentUpload() {
  try {
    console.log('🔍 Testing patent upload validation...');
    
    // Create test form data
    const formData = new FormData();
    
    // Add patent information (using sample data that should pass validation)
    formData.append('patentName', '测试专利名称 - 这是一个用于测试的专利名称');
    formData.append('patentNumber', `CN${Date.now().toString().slice(-12)}.5`);
    formData.append('patentCategory', 'invention');
    formData.append('transferPrice', '1.5');
    formData.append('patentAbstract', '这是一个测试专利的摘要。这个摘要包含了足够的字符数来满足最少50个字符的要求。专利摘要应该详细描述专利的技术内容和创新点，以便审核人员能够理解专利的核心价值。');
    formData.append('applicationDate', '2023-01-15');
    formData.append('expirationDate', '2043-01-15');
    formData.append('ownerName', '测试用户');
    formData.append('ownerIdNumber', '110101199001011234');
    formData.append('isAgentSale', 'false');
    formData.append('uploaderAddress', '******************************************');
    
    // Create dummy files for testing
    const testFileContent = Buffer.from('This is a test file content for patent document');
    formData.append('patentDocument', testFileContent, {
      filename: 'test-patent.pdf',
      contentType: 'application/pdf'
    });
    
    formData.append('ownershipDocument', testFileContent, {
      filename: 'test-ownership.pdf',
      contentType: 'application/pdf'
    });
    
    console.log('📋 Form data prepared:');
    console.log('- patentName:', '测试专利名称 - 这是一个用于测试的专利名称');
    console.log('- patentNumber:', `CN${Date.now().toString().slice(-12)}.5`);
    console.log('- patentCategory:', 'invention');
    console.log('- transferPrice:', '1.5');
    console.log('- applicationDate:', '2023-01-15');
    console.log('- expirationDate:', '2043-01-15');
    console.log('- ownerName:', '测试用户');
    console.log('- ownerIdNumber:', '110101199001011234');
    console.log('- isAgentSale:', 'false');
    console.log('- uploaderAddress:', '******************************************');
    
    // Test the upload endpoint
    const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
      headers: {
        ...formData.getHeaders(),
        'X-User-Address': '******************************************'
      },
      timeout: 30000
    });
    
    console.log('✅ Upload successful:', response.data);
    
  } catch (error) {
    console.error('❌ Upload failed:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
      
      // Check for validation errors
      if (error.response.data?.error?.details?.errors) {
        console.error('\n🔍 Validation errors:');
        error.response.data.error.details.errors.forEach((err, index) => {
          console.error(`${index + 1}. Field: ${err.field}`);
          console.error(`   Message: ${err.message}`);
          console.error(`   Value: ${err.value}`);
        });
      }
    } else if (error.request) {
      console.error('No response received:', error.message);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Test individual validation rules
async function testValidationRules() {
  console.log('\n🧪 Testing individual validation rules...');
  
  const testCases = [
    {
      name: 'Empty patent name',
      data: { patentName: '' },
      expectedError: 'Patent name is required'
    },
    {
      name: 'Short patent name',
      data: { patentName: 'Test' },
      expectedError: 'Patent name must be between 5 and 200 characters'
    },
    {
      name: 'Invalid patent number format',
      data: { patentNumber: 'INVALID123' },
      expectedError: 'Invalid patent number format'
    },
    {
      name: 'Invalid transfer price',
      data: { transferPrice: '0' },
      expectedError: 'Transfer price must be greater than 0'
    },
    {
      name: 'Short patent abstract',
      data: { patentAbstract: 'Too short' },
      expectedError: 'Patent abstract must be between 50 and 2000 characters'
    },
    {
      name: 'Invalid application date format',
      data: { applicationDate: 'invalid-date' },
      expectedError: 'Invalid application date format'
    },
    {
      name: 'Future application date',
      data: { applicationDate: '2030-01-01' },
      expectedError: 'Application date cannot be in the future'
    },
    {
      name: 'Past expiration date',
      data: { expirationDate: '2020-01-01' },
      expectedError: 'Expiration date must be in the future'
    },
    {
      name: 'Invalid owner ID number',
      data: { ownerIdNumber: '123456' },
      expectedError: 'Invalid owner ID number format'
    },
    {
      name: 'Invalid uploader address',
      data: { uploaderAddress: 'invalid-address' },
      expectedError: 'Invalid uploader address format'
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n📝 Testing: ${testCase.name}`);
    
    try {
      const formData = new FormData();
      
      // Add default valid data
      const defaultData = {
        patentName: '测试专利名称 - 这是一个用于测试的专利名称',
        patentNumber: `CN${Date.now().toString().slice(-12)}.5`,
        patentCategory: 'invention',
        transferPrice: '1.5',
        patentAbstract: '这是一个测试专利的摘要。这个摘要包含了足够的字符数来满足最少50个字符的要求。专利摘要应该详细描述专利的技术内容和创新点。',
        applicationDate: '2023-01-15',
        expirationDate: '2043-01-15',
        ownerName: '测试用户',
        ownerIdNumber: '110101199001011234',
        isAgentSale: 'false',
        uploaderAddress: '******************************************'
      };
      
      // Override with test data
      const testData = { ...defaultData, ...testCase.data };
      
      // Add all data to form
      Object.entries(testData).forEach(([key, value]) => {
        formData.append(key, value);
      });
      
      // Add dummy files
      const testFileContent = Buffer.from('Test file content');
      formData.append('patentDocument', testFileContent, {
        filename: 'test-patent.pdf',
        contentType: 'application/pdf'
      });
      formData.append('ownershipDocument', testFileContent, {
        filename: 'test-ownership.pdf',
        contentType: 'application/pdf'
      });
      
      const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
        headers: {
          ...formData.getHeaders(),
          'X-User-Address': '******************************************'
        },
        timeout: 10000
      });
      
      console.log(`   ❌ Expected validation error but got success`);
      
    } catch (error) {
      if (error.response?.data?.error?.details?.errors) {
        const validationErrors = error.response.data.error.details.errors;
        const hasExpectedError = validationErrors.some(err => 
          err.message.includes(testCase.expectedError) || 
          err.message.toLowerCase().includes(testCase.expectedError.toLowerCase())
        );
        
        if (hasExpectedError) {
          console.log(`   ✅ Got expected validation error`);
        } else {
          console.log(`   ⚠️  Got different validation error:`);
          validationErrors.forEach(err => {
            console.log(`      - ${err.field}: ${err.message}`);
          });
        }
      } else {
        console.log(`   ⚠️  Got unexpected error: ${error.response?.data?.error?.message || error.message}`);
      }
    }
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting patent upload validation test...\n');
  
  // Wait for backend to be ready
  console.log('⏳ Waiting for backend to be ready...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  try {
    // Test basic connectivity
    const healthCheck = await axios.get('http://localhost:3000/api/user/role/******************************************');
    console.log('✅ Backend is responding\n');
  } catch (error) {
    console.error('❌ Backend is not responding:', error.message);
    console.log('Please make sure the backend server is running on port 3000');
    return;
  }
  
  // Run tests
  await testPatentUpload();
  await testValidationRules();
  
  console.log('\n✅ Test completed');
}

main().catch(console.error); 