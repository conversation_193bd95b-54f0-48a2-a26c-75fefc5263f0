#!/usr/bin/env node

/**
 * Test script to check reviewer access and role verification
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// Test addresses from Ganache
const TEST_ADDRESSES = [
  '******************************************', // Account 0
  '******************************************', // Account 1
  '******************************************', // Account 2
  '******************************************', // Account 3
  '******************************************'  // Account 4
];

// Helper function to make API calls
async function apiCall(method, endpoint, data = null, userAddress = null) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (userAddress) {
      config.headers['X-User-Address'] = userAddress;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    return {
      error: true,
      status: error.response?.status,
      message: error.response?.data?.error?.message || error.message,
      code: error.response?.data?.error?.code
    };
  }
}

async function checkUserRole(address) {
  console.log(`\n🔍 Checking role for address: ${address}`);
  
  const result = await apiCall('GET', `/user/role/${address}`, null, address);
  
  if (result.error) {
    console.log(`❌ Role check failed: ${result.message}`);
    return null;
  } else {
    const role = result.data?.role || 'unknown';
    console.log(`✅ Role: ${role}`);
    return role;
  }
}

async function testPendingTransactionsAccess(address, role) {
  console.log(`\n🔍 Testing pending transactions access for ${role}: ${address}`);
  
  const result = await apiCall('GET', '/transactions/pending', null, address);
  
  if (result.error) {
    console.log(`❌ Access denied: ${result.message} (Status: ${result.status})`);
    return false;
  } else {
    const transactions = result.data?.data || result.data || [];
    console.log(`✅ Access granted! Found ${Array.isArray(transactions) ? transactions.length : 'non-array'} transactions`);
    return true;
  }
}

async function runReviewerAccessTest() {
  console.log('🧪 Reviewer Access Test');
  console.log('=======================');
  
  const reviewers = [];
  const users = [];
  
  // Check roles for all test addresses
  for (const address of TEST_ADDRESSES) {
    const role = await checkUserRole(address);
    if (role === 'reviewer' || role === 'admin') {
      reviewers.push({ address, role });
    } else {
      users.push({ address, role });
    }
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`   Reviewers/Admins: ${reviewers.length}`);
  console.log(`   Regular Users: ${users.length}`);
  
  // Test access for reviewers
  if (reviewers.length > 0) {
    console.log(`\n✅ Testing reviewer access:`);
    for (const reviewer of reviewers) {
      await testPendingTransactionsAccess(reviewer.address, reviewer.role);
    }
  } else {
    console.log(`\n⚠️ No reviewers found! This explains why ReviewTradingView shows no transactions.`);
  }
  
  // Test access for regular users (should be denied)
  if (users.length > 0) {
    console.log(`\n❌ Testing user access (should be denied):`);
    const testUser = users[0];
    await testPendingTransactionsAccess(testUser.address, testUser.role);
  }
  
  // Check if there are any pending transactions at all
  console.log(`\n🔍 Checking if there are any pending transactions in the system...`);
  if (reviewers.length > 0) {
    const reviewer = reviewers[0];
    const result = await apiCall('GET', '/transactions/pending', null, reviewer.address);
    
    if (!result.error) {
      const transactions = result.data?.data || result.data || [];
      if (Array.isArray(transactions)) {
        console.log(`✅ Found ${transactions.length} pending transactions in the system`);
        if (transactions.length > 0) {
          console.log(`   First transaction:`, transactions[0]);
        }
      }
    }
  }
  
  console.log('\n🎯 Recommendations:');
  if (reviewers.length === 0) {
    console.log('   1. ❌ No reviewer accounts found - need to assign reviewer role to at least one account');
    console.log('   2. 💡 Use the admin interface or backend script to assign reviewer roles');
  } else {
    console.log('   1. ✅ Reviewer accounts exist');
    console.log('   2. 💡 Make sure the frontend is connected with a reviewer account');
    console.log('   3. 💡 Check the frontend auth store to ensure role detection is working');
  }
}

// Run the test
if (require.main === module) {
  runReviewerAccessTest();
}

module.exports = {
  runReviewerAccessTest,
  checkUserRole,
  testPendingTransactionsAccess
};
