const axios = require('axios');
const FormData = require('form-data');

/**
 * Test script to verify the frontend fix for patent upload
 * This tests the exact scenario that was failing before
 */

async function testFrontendFixedUpload() {
  console.log('🔍 Testing frontend upload with the fix applied...');
  
  try {
    // Create FormData exactly like the fixed frontend does
    const formData = new FormData();
    
    // Add patent information with valid data
    formData.append('patentName', '测试专利名称 - 验证前端修复');
    formData.append('patentNumber', `CN${Date.now().toString().slice(-12)}.5`);
    formData.append('patentCategory', 'invention');
    formData.append('transferPrice', '1.5');
    formData.append('patentAbstract', '这是一个测试专利的摘要，用于验证前端修复是否成功。这个摘要包含了足够的字符数来满足最少50个字符的要求。专利摘要应该详细描述专利的技术内容和创新点。');
    formData.append('applicationDate', '2023-01-15');
    formData.append('expirationDate', '2043-01-15');
    formData.append('ownerName', '测试用户');
    formData.append('ownerIdNumber', '110101199001011234');
    
    // Test the fix: convert boolean to string explicitly
    const isAgentSale = false;
    formData.append('isAgentSale', isAgentSale.toString()); // This is the fix
    
    formData.append('uploaderAddress', '******************************************');
    
    // Add dummy files
    const testFileContent = Buffer.from('Test file content for frontend fix verification');
    formData.append('patentDocument', testFileContent, {
      filename: 'test-patent-frontend-fix.pdf',
      contentType: 'application/pdf'
    });
    formData.append('ownershipDocument', testFileContent, {
      filename: 'test-ownership-frontend-fix.pdf',
      contentType: 'application/pdf'
    });
    
    console.log('📋 Form data prepared with boolean fix applied');
    console.log('   isAgentSale value:', isAgentSale.toString(), '(type: string)');
    
    // Make request exactly like frontend API client
    const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
      headers: {
        ...formData.getHeaders(),
        'X-User-Address': '******************************************'
      },
      timeout: 30000
    });
    
    console.log('✅ Upload successful with frontend fix:', response.data);
    return true;
    
  } catch (error) {
    if (error.response) {
      console.log('❌ Upload failed even with fix:');
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
      
      // Check if it's a validation error
      if (error.response.status === 400 && error.response.data.error?.code === 'VALIDATION_ERROR') {
        console.log('Validation errors:', error.response.data.error.details?.errors);
      }
      return false;
    } else {
      console.error('❌ Network or other error:', error.message);
      return false;
    }
  }
}

async function testBooleanHandling() {
  console.log('\n🔍 Testing different boolean value formats...');
  
  const testCases = [
    { value: 'true', description: 'String "true"' },
    { value: 'false', description: 'String "false"' },
    { value: true, description: 'Boolean true' },
    { value: false, description: 'Boolean false' }
  ];
  
  for (const testCase of testCases) {
    try {
      const formData = new FormData();
      
      // Add minimal valid data
      formData.append('patentName', '测试专利名称 - 布尔值测试');
      formData.append('patentNumber', `CN${Date.now().toString().slice(-12)}.5`);
      formData.append('patentCategory', 'invention');
      formData.append('transferPrice', '1.5');
      formData.append('patentAbstract', '这是一个测试专利的摘要，用于测试不同的布尔值格式。这个摘要包含了足够的字符数来满足最少50个字符的要求。');
      formData.append('applicationDate', '2023-01-15');
      formData.append('expirationDate', '2043-01-15');
      formData.append('ownerName', '测试用户');
      formData.append('ownerIdNumber', '110101199001011234');
      formData.append('isAgentSale', testCase.value); // Test different boolean formats
      formData.append('uploaderAddress', '******************************************');
      
      // Add dummy files
      const testFileContent = Buffer.from('Test file content');
      formData.append('patentDocument', testFileContent, {
        filename: 'test-patent.pdf',
        contentType: 'application/pdf'
      });
      formData.append('ownershipDocument', testFileContent, {
        filename: 'test-ownership.pdf',
        contentType: 'application/pdf'
      });
      
      const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
        headers: {
          ...formData.getHeaders(),
          'X-User-Address': '******************************************'
        },
        timeout: 30000
      });
      
      console.log(`   ✅ ${testCase.description}: SUCCESS`);
      
    } catch (error) {
      if (error.response?.status === 400) {
        console.log(`   ❌ ${testCase.description}: VALIDATION ERROR`);
        if (error.response.data.error?.details?.errors) {
          const relevantErrors = error.response.data.error.details.errors.filter(err => 
            err.path === 'isAgentSale'
          );
          if (relevantErrors.length > 0) {
            console.log(`      Error: ${relevantErrors[0].msg}`);
          }
        }
      } else {
        console.log(`   ❌ ${testCase.description}: OTHER ERROR - ${error.message}`);
      }
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}

// Run the tests
async function runTests() {
  console.log('🚀 Starting frontend fix verification tests...\n');
  
  const success = await testFrontendFixedUpload();
  await testBooleanHandling();
  
  console.log('\n📊 Test Summary:');
  if (success) {
    console.log('✅ Frontend fix appears to be working correctly');
    console.log('✅ Patent upload should now work in the frontend application');
  } else {
    console.log('❌ Frontend fix may need additional adjustments');
  }
  
  console.log('\n✅ Tests completed');
}

runTests().catch(console.error);
