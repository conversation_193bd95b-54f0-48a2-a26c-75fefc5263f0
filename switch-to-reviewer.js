#!/usr/bin/env node

/**
 * Helper script to show reviewer account information
 * This helps users know which accounts to switch to in MetaMask
 */

console.log('🔍 Reviewer Account Information');
console.log('==============================');
console.log('');
console.log('To access the ReviewTradingView, you need to connect with one of these reviewer accounts:');
console.log('');

const reviewerAccounts = [
  {
    address: '0x164D435789d02dbE8317f48017D61663C1CE369B',
    role: 'admin',
    name: 'Account 0 (Admin)'
  },
  {
    address: '0xa19515E50EA5c913e9ddB5476E4a59E097606727',
    role: 'reviewer',
    name: 'Account 1 (Reviewer)'
  },
  {
    address: '0x4ECd8c0fD3B1BebFd355C766664e2f5CE69CD6Ce',
    role: 'reviewer',
    name: 'Account 2 (Reviewer)'
  },
  {
    address: '0x234D1d5522F9E502a0239740E40c72d7c4FbBe9f',
    role: 'reviewer',
    name: 'Account 3 (Reviewer)'
  }
];

reviewerAccounts.forEach((account, index) => {
  console.log(`${index + 1}. ${account.name}`);
  console.log(`   Address: ${account.address}`);
  console.log(`   Role: ${account.role}`);
  console.log('');
});

console.log('📋 Instructions:');
console.log('1. Open MetaMask in your browser');
console.log('2. Click on the account selector (top right)');
console.log('3. Switch to one of the reviewer accounts listed above');
console.log('4. Refresh the frontend application');
console.log('5. Navigate to Review → Trading Review');
console.log('');
console.log('💡 If you don\'t see these accounts in MetaMask:');
console.log('1. Make sure you\'re connected to the correct network (Ganache)');
console.log('2. Import the accounts using their private keys from Ganache');
console.log('3. Or use the account switching feature in the frontend');
console.log('');
console.log('🎯 Expected Result:');
console.log('- You should see 7 pending transactions for review');
console.log('- You should be able to approve/reject transactions');
console.log('- The transactions should show proper status progression');
