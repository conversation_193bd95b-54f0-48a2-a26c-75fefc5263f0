#!/usr/bin/env node

/**
 * Simple API test to verify the transaction approval workflow fix
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// Test addresses
const REVIEWER_ADDRESS = '******************************************';

// Helper function to make API calls
async function apiCall(method, endpoint, data = null, userAddress = null) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (userAddress) {
      config.headers['X-User-Address'] = userAddress;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ API call failed: ${method} ${endpoint}`);
    console.error('Error:', error.response?.data || error.message);
    throw error;
  }
}

async function testPendingTransactions() {
  console.log('🔍 Testing pending transactions endpoint...');
  
  try {
    const result = await apiCall('GET', '/transactions/pending', null, REVIEWER_ADDRESS);
    console.log('✅ Pending transactions endpoint works');
    console.log('Response structure:', {
      success: result.success,
      dataType: typeof result.data,
      hasData: !!result.data,
      dataKeys: result.data ? Object.keys(result.data) : 'no data'
    });
    
    const transactions = result.data?.data || result.data || [];
    console.log(`Found ${Array.isArray(transactions) ? transactions.length : 'non-array'} transactions`);
    
    if (Array.isArray(transactions) && transactions.length > 0) {
      console.log('First transaction:', transactions[0]);
      return transactions[0].id;
    }
    
    return null;
  } catch (error) {
    console.error('❌ Failed to get pending transactions');
    return null;
  }
}

async function testApproveTransaction(transactionId) {
  if (!transactionId) {
    console.log('⚠️ No transaction ID to test approval');
    return false;
  }
  
  console.log(`✅ Testing transaction approval for ID: ${transactionId}...`);
  
  try {
    const result = await apiCall('PUT', `/transactions/${transactionId}/approve`, {
      reviewerAddress: REVIEWER_ADDRESS,
      comments: 'Test approval'
    }, REVIEWER_ADDRESS);
    
    console.log('✅ Transaction approval endpoint works');
    console.log('Approval result:', result);
    return true;
  } catch (error) {
    console.error('❌ Failed to approve transaction');
    return false;
  }
}

async function testCompleteTransaction(transactionId) {
  if (!transactionId) {
    console.log('⚠️ No transaction ID to test completion');
    return false;
  }
  
  console.log(`🏁 Testing transaction completion for ID: ${transactionId}...`);
  
  try {
    const result = await apiCall('PUT', `/transactions/${transactionId}/complete`, {}, REVIEWER_ADDRESS);
    
    console.log('✅ Transaction completion endpoint works');
    console.log('Completion result:', result);
    return true;
  } catch (error) {
    console.error('❌ Failed to complete transaction');
    console.error('This might be expected if transaction is not in approved status');
    return false;
  }
}

async function runSimpleAPITest() {
  console.log('🧪 Simple API Test for Transaction Workflow');
  console.log('==========================================');
  
  try {
    // Test 1: Check pending transactions
    const transactionId = await testPendingTransactions();
    
    // Test 2: Test approval endpoint (if we have a transaction)
    if (transactionId) {
      const approved = await testApproveTransaction(transactionId);
      
      // Test 3: Test completion endpoint (if approval worked)
      if (approved) {
        await testCompleteTransaction(transactionId);
      }
    }
    
    console.log('\n🎉 API tests completed!');
    console.log('==========================================');
    console.log('✅ The transaction workflow endpoints are working');
    console.log('✅ Approval and completion are now separate steps');
    console.log('✅ This should fix the frontend issue where transactions');
    console.log('   were showing as completed immediately after approval');
    
  } catch (error) {
    console.error('\n❌ API test failed:', error.message);
    console.error('==========================================');
  }
}

// Run the test
if (require.main === module) {
  runSimpleAPITest();
}

module.exports = {
  runSimpleAPITest,
  testPendingTransactions,
  testApproveTransaction,
  testCompleteTransaction
};
