# Dynamic Role-Based View Switching Guide

This guide explains how to use and test the dynamic role-based view switching system that responds to MetaMask account changes.

## Overview

The system automatically detects when users switch accounts in MetaMask and updates the UI to display the appropriate interface based on the new account's role. It provides seamless transitions between different role interfaces without requiring page refresh or manual role selection.

## Features

### ✅ Implemented Features

1. **Automatic Account Detection**: Monitors MetaMask account changes in real-time
2. **Role-Based Access Control**: Three distinct roles with different permissions
3. **Smooth UI Transitions**: Visual feedback during role changes with loading states
4. **Error Handling**: Retry mechanism for role detection failures
5. **Notification System**: User-friendly notifications for account and role changes
6. **Multiple Detection Strategies**: Support for address-based, smart contract, and API-based role detection

### 🎯 Role Definitions

#### Regular Users (`user`)
- Patent upload functionality
- Patent search interface  
- Patent trading/marketplace
- "My Patents" dashboard
- Patent transfer capabilities
- Rights protection features

#### Patent Reviewers (`reviewer`)
- All regular user features
- Patent review interface/dashboard
- Patent maintenance tools
- Rights protection review system

#### Administrators (`admin`)
- System management interface
- User management
- Transaction monitoring
- System statistics

## How to Test

### 1. Start the Development Server

```bash
cd frontend/patent-exchange
npm run dev
```

### 2. Connect MetaMask

1. Open the application in your browser
2. Click "连接钱包" (Connect Wallet) button
3. Approve the connection in MetaMask

### 3. Use the Role Manager (Development Tool)

Once connected, you'll see a "角色管理 (开发测试)" section on the home page. This tool allows you to:

- View current account and role status
- Add demo addresses for different roles
- Switch between role detection strategies
- Manually refresh role detection
- View system configuration

### 4. Test Role Switching

#### Method 1: Using Demo Addresses
1. In the Role Manager, add your current MetaMask address to a specific role (admin/reviewer/user)
2. Click "刷新角色" (Refresh Role) to update your role
3. Observe the navigation menu and available features change

#### Method 2: Switch MetaMask Accounts
1. Add different addresses to different roles using the Role Manager
2. Switch accounts in MetaMask
3. The system will automatically detect the change and update the role
4. Watch for notifications and UI updates

## Technical Implementation

### Core Components

#### 1. Enhanced Auth Store (`src/stores/auth.js`)
- Manages authentication state and role detection
- Handles MetaMask account change events
- Provides role change notifications
- Implements retry logic for failed role detection

#### 2. Role Service (`src/services/roleService.js`)
- Configurable role detection strategies
- Support for smart contract, API, and address-based detection
- Demo address management for testing

#### 3. Enhanced Router Guards (`src/router/index.js`)
- Role-based navigation protection
- Automatic redirection based on role changes
- Handles edge cases during role transitions

#### 4. Enhanced UI Components (`src/components/AppLayout.vue`)
- Dynamic navigation menu based on roles
- Visual feedback during role loading
- Role change notifications

#### 5. Role Manager Component (`src/components/RoleManager.vue`)
- Development and testing tool
- Address management for demo mode
- Configuration viewing and strategy switching

### Role Detection Strategies

#### 1. Address-Based (Default/Demo)
```javascript
// Configure demo addresses
roleService.addDemoAddresses('admin', ['0x123...'])
roleService.addDemoAddresses('reviewer', ['0x456...'])
```

#### 2. Smart Contract-Based
```javascript
// Set contract strategy
roleService.setStrategy('contract', {
  contractAddress: '0xYourContractAddress'
})
```

#### 3. API-Based
```javascript
// Set API strategy  
roleService.setStrategy('api', {
  apiEndpoint: 'https://your-api.com'
})
```

## Configuration

### Environment Variables
Create a `.env` file in the frontend directory:

```env
# Role detection strategy: 'address', 'contract', 'api'
VITE_ROLE_STRATEGY=address

# Smart contract address (if using contract strategy)
VITE_CONTRACT_ADDRESS=0x...

# API endpoint (if using API strategy)
VITE_API_ENDPOINT=https://your-api.com
```

### Customizing Demo Addresses

Edit `src/services/roleService.js` to modify default demo addresses:

```javascript
this.demoRoles = {
  admin: [
    '0x8ba1f109551bd432803012645hac136c5c1e5f1e',
    // Add your admin addresses here
  ],
  reviewer: [
    '0x14723a09acff6d2a60dcdf7aa4aff308fddc160c',
    // Add your reviewer addresses here
  ]
}
```

## Troubleshooting

### Common Issues

1. **Role not updating after account switch**
   - Check if the new address is configured in the demo addresses
   - Use the "刷新角色" button in Role Manager
   - Check browser console for error messages

2. **Navigation not working**
   - Ensure the route has proper role permissions in `src/router/index.js`
   - Check if user has the required role for the route

3. **MetaMask not detecting changes**
   - Refresh the page and reconnect MetaMask
   - Check if MetaMask is properly connected
   - Ensure you're on the correct network

### Debug Information

Enable debug logging by opening browser console. The system logs:
- Account changes
- Role detection attempts
- Navigation decisions
- Error messages

## Future Enhancements

1. **Smart Contract Integration**: Replace demo addresses with actual smart contract role queries
2. **Backend API Integration**: Connect to a backend service for role management
3. **Role Caching**: Implement role caching to reduce detection calls
4. **Advanced Permissions**: Implement fine-grained permissions within roles
5. **Role History**: Track role change history for audit purposes

## API Reference

### Auth Store Methods

```javascript
// Connect wallet
await authStore.connectWallet()

// Disconnect wallet
authStore.disconnectWallet()

// Manually refresh role
await authStore.determineUserRole()

// Clear notifications
authStore.clearRoleNotification()
```

### Role Service Methods

```javascript
// Get user role
const role = await roleService.getUserRole(address, web3)

// Set detection strategy
roleService.setStrategy('contract', { contractAddress: '0x...' })

// Add demo addresses
roleService.addDemoAddresses('admin', ['0x...'])

// Check if user has specific role
const hasRole = await roleService.hasRole(address, 'admin', web3)
```

## Support

For issues or questions about the role switching system:

1. Check the browser console for error messages
2. Use the Role Manager tool to debug configuration
3. Verify MetaMask connection and network settings
4. Review the implementation in the source files mentioned above
