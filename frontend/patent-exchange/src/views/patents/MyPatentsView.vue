<template>
  <div class="my-patents-view">
    <div class="container-fluid py-4">
      <!-- <PERSON> Header -->
      <div class="d-flex align-items-center mb-4">
        <i class="bi bi-folder text-primary me-3" style="font-size: 2rem;"></i>
        <div>
          <h2 class="mb-1">我的专利</h2>
          <p class="text-muted mb-0">管理您的专利资产</p>
        </div>
      </div>

      <!-- Tabs -->
      <ul class="nav nav-tabs mb-4" id="patentTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button
            class="nav-link active"
            id="uploaded-tab"
            data-bs-toggle="tab"
            data-bs-target="#uploaded"
            type="button"
            role="tab"
          >
            <i class="bi bi-upload me-2"></i>
            已上传专利 <span class="badge bg-primary ms-1">{{ uploadedPatents.length }}</span>
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            id="purchased-tab"
            data-bs-toggle="tab"
            data-bs-target="#purchased"
            type="button"
            role="tab"
          >
            <i class="bi bi-cart-check me-2"></i>
            已购买专利 <span class="badge bg-success ms-1">{{ purchasedPatents.length }}</span>
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            id="sold-tab"
            data-bs-toggle="tab"
            data-bs-target="#sold"
            type="button"
            role="tab"
          >
            <i class="bi bi-cash-coin me-2"></i>
            已出售专利 <span class="badge bg-warning ms-1">{{ soldPatents.length }}</span>
          </button>
        </li>
      </ul>

      <!-- Tab Content -->
      <div class="tab-content" id="patentTabsContent">
        <!-- Uploaded Patents -->
        <div class="tab-pane fade show active" id="uploaded" role="tabpanel">
          <div class="row g-4">
            <div
              v-for="patent in uploadedPatents"
              :key="patent.id"
              class="col-lg-6 col-xl-4"
            >
              <div class="card h-100 shadow-sm">
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-start mb-3">
                    <h6 class="card-title text-truncate-2 mb-0">{{ patent.name }}</h6>
                    <span :class="getStatusBadgeClass(patent.status)" class="badge ms-2">
                      {{ getStatusText(patent.status) }}
                    </span>
                  </div>

                  <p class="text-muted small mb-2">
                    <i class="bi bi-hash me-1"></i>
                    {{ patent.number }}
                  </p>

                  <p class="card-text text-truncate-3 mb-3">{{ patent.abstract }}</p>

                  <div class="row text-center mb-3">
                    <div class="col-6">
                      <div class="border-end">
                        <div class="fw-bold text-primary">{{ formatPrice(patent.price) }} ETH</div>
                        <small class="text-muted">转让价格</small>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="fw-bold text-success">{{ formatDate(patent.uploadDate) }}</div>
                      <small class="text-muted">上传日期</small>
                    </div>
                  </div>

                  <div class="d-flex flex-column gap-2">
                    <button
                      class="btn btn-outline-primary btn-sm"
                      @click="viewPatentDetails(patent)"
                    >
                      <i class="bi bi-eye me-1"></i>
                      查看详情
                    </button>

                    <!-- Action Buttons for Uploaded Patents -->
                    <div class="btn-group btn-group-sm" role="group">
                      <button
                        class="btn btn-outline-warning"
                        @click="withdrawPatent(patent)"
                        :disabled="!canWithdrawPatent(patent) || isActionLoading"
                        :title="getActionTooltip(patent, 'withdraw')"
                      >
                        <i class="bi bi-arrow-left-circle me-1"></i>
                        撤回
                      </button>

                      <button
                        v-if="patent.status !== 'frozen' && patent.status !== 'withdrawn'"
                        class="btn btn-outline-secondary"
                        @click="freezePatent(patent)"
                        :disabled="!canFreezePatent(patent) || isActionLoading"
                        :title="getActionTooltip(patent, 'freeze')"
                      >
                        <i class="bi bi-pause-circle me-1"></i>
                        冻结
                      </button>

                      <button
                        v-if="patent.status === 'withdrawn'"
                        class="btn btn-outline-success"
                        @click="restorePatent(patent)"
                        :disabled="isActionLoading"
                        title="恢复专利"
                      >
                        <i class="bi bi-play-circle me-1"></i>
                        恢复
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="uploadedPatents.length === 0" class="text-center py-5">
            <i class="bi bi-upload text-muted" style="font-size: 3rem;"></i>
            <h5 class="text-muted mt-3">暂无上传的专利</h5>
            <p class="text-muted">
              <router-link to="/patents/upload" class="text-decoration-none">
                点击这里上传您的第一个专利
              </router-link>
            </p>
          </div>
        </div>

        <!-- Purchased Patents -->
        <div class="tab-pane fade" id="purchased" role="tabpanel">
          <div class="row g-4">
            <div
              v-for="patent in purchasedPatents"
              :key="patent.id"
              class="col-lg-6 col-xl-4"
            >
              <div class="card h-100 shadow-sm">
                <div class="card-body">
                  <h6 class="card-title text-truncate-2 mb-3">{{ patent.name }}</h6>

                  <p class="text-muted small mb-2">
                    <i class="bi bi-hash me-1"></i>
                    {{ patent.number }}
                  </p>

                  <p class="card-text text-truncate-3 mb-3">{{ patent.abstract }}</p>

                  <div class="row text-center mb-3">
                    <div class="col-6">
                      <div class="border-end">
                        <div class="fw-bold text-success">{{ formatPrice(patent.purchasePrice) }} ETH</div>
                        <small class="text-muted">购买价格</small>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="fw-bold text-info">{{ formatDate(patent.purchaseDate) }}</div>
                      <small class="text-muted">购买日期</small>
                    </div>
                  </div>

                  <!-- Transaction Details for Purchased Patents -->
                  <div class="mb-3">
                    <small class="text-muted d-block">
                      <i class="bi bi-person me-1"></i>
                      卖方: {{ patent.sellerName || formatAddress(patent.sellerAddress) }}
                    </small>
                    <small class="text-muted d-block">
                      <i class="bi bi-link-45deg me-1"></i>
                      交易哈希: {{ formatAddress(patent.transactionHash) }}
                    </small>
                  </div>

                  <div class="d-flex gap-2">
                    <button
                      class="btn btn-outline-primary btn-sm flex-fill"
                      @click="viewPatentDetails(patent)"
                    >
                      <i class="bi bi-eye me-1"></i>
                      查看详情
                    </button>
                    <button
                      class="btn btn-outline-success btn-sm flex-fill"
                      @click="downloadPatentDocument(patent)"
                    >
                      <i class="bi bi-download me-1"></i>
                      下载文档
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="purchasedPatents.length === 0" class="text-center py-5">
            <i class="bi bi-cart text-muted" style="font-size: 3rem;"></i>
            <h5 class="text-muted mt-3">暂无购买的专利</h5>
            <p class="text-muted">
              <router-link to="/patents/search" class="text-decoration-none">
                去搜索专利进行购买
              </router-link>
            </p>
          </div>
        </div>

        <!-- Sold Patents -->
        <div class="tab-pane fade" id="sold" role="tabpanel">
          <div class="row g-4">
            <div
              v-for="patent in soldPatents"
              :key="patent.id"
              class="col-lg-6 col-xl-4"
            >
              <div class="card h-100 shadow-sm">
                <div class="card-body">
                  <h6 class="card-title text-truncate-2 mb-3">{{ patent.name }}</h6>

                  <p class="text-muted small mb-2">
                    <i class="bi bi-hash me-1"></i>
                    {{ patent.number }}
                  </p>

                  <p class="card-text text-truncate-3 mb-3">{{ patent.abstract }}</p>

                  <div class="row text-center mb-3">
                    <div class="col-6">
                      <div class="border-end">
                        <div class="fw-bold text-warning">{{ formatPrice(patent.salePrice) }} ETH</div>
                        <small class="text-muted">出售价格</small>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="fw-bold text-secondary">{{ formatDate(patent.saleDate) }}</div>
                      <small class="text-muted">出售日期</small>
                    </div>
                  </div>

                  <!-- Transaction Details for Sold Patents -->
                  <div class="mb-3">
                    <small class="text-muted d-block">
                      <i class="bi bi-person me-1"></i>
                      买方: {{ patent.buyerName || formatAddress(patent.buyerAddress) }}
                    </small>
                    <small class="text-muted d-block">
                      <i class="bi bi-link-45deg me-1"></i>
                      交易哈希: {{ formatAddress(patent.transactionHash) }}
                    </small>
                  </div>

                  <div class="d-flex gap-2">
                    <button
                      class="btn btn-outline-primary btn-sm flex-fill"
                      @click="viewPatentDetails(patent)"
                    >
                      <i class="bi bi-eye me-1"></i>
                      查看详情
                    </button>
                    <button
                      class="btn btn-outline-info btn-sm flex-fill"
                      @click="viewTransactionHistory(patent)"
                    >
                      <i class="bi bi-receipt me-1"></i>
                      交易记录
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="soldPatents.length === 0" class="text-center py-5">
            <i class="bi bi-cash-coin text-muted" style="font-size: 3rem;"></i>
            <h5 class="text-muted mt-3">暂无出售的专利</h5>
            <p class="text-muted">当您的专利被购买后，会在这里显示</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import { patentService } from '@/services/patentService'
import { notificationService } from '@/services/notificationService'

export default {
  name: 'MyPatentsView',
  setup() {
    const authStore = useAuthStore()
    const router = useRouter()

    const uploadedPatents = ref([])
    const purchasedPatents = ref([])
    const soldPatents = ref([])
    const isActionLoading = ref(false)
    const isLoadingData = ref(false)

    const getStatusBadgeClass = (status) => {
      const classes = {
        pending: 'bg-warning',
        approved: 'bg-success',
        rejected: 'bg-danger',
        normal: 'bg-success',
        withdrawn: 'bg-secondary'
      }
      return classes[status] || 'bg-secondary'
    }

    const getStatusText = (status) => {
      const texts = {
        pending: '待审核',
        approved: '已通过',
        rejected: '已拒绝',
        normal: '正常',
        withdrawn: '已撤回'
      }
      return texts[status] || '未知状态'
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const formatAddress = (address) => {
      if (!address || typeof address !== 'string') {
        return 'N/A'
      }
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    const formatPrice = (price) => {
      return parseFloat(price).toFixed(2)
    }

    // Status validation helpers
    const canWithdrawPatent = (patent) => {
      // Can only withdraw approved or normal patents
      return patent.status === 'approved' || patent.status === 'normal'
    }

    const canFreezePatent = (patent) => {
      // Can only freeze approved or normal patents
      return patent.status === 'approved' || patent.status === 'normal'
    }

    const getActionTooltip = (patent, action) => {
      if (action === 'withdraw') {
        if (patent.status === 'pending') {
          return '待审核的专利无法撤回，请等待审核完成'
        } else if (patent.status === 'rejected') {
          return '已拒绝的专利无法撤回'
        } else if (patent.status === 'withdrawn') {
          return '专利已撤回'
        } else if (patent.status === 'traded') {
          return '已交易的专利无法撤回'
        }
        return '撤回专利'
      } else if (action === 'freeze') {
        if (patent.status === 'pending') {
          return '待审核的专利无法冻结，请等待审核完成'
        } else if (patent.status === 'rejected') {
          return '已拒绝的专利无法冻结'
        } else if (patent.status === 'withdrawn') {
          return '已撤回的专利无法冻结'
        } else if (patent.status === 'traded') {
          return '已交易的专利无法冻结'
        }
        return '冻结专利'
      }
      return ''
    }

    // Navigation methods
    const viewPatentDetails = (patent) => {
      router.push(`/patent/${patent.id}`)
    }

    const viewTransactionHistory = (patent) => {
      // Navigate to transaction history page or show modal
      console.log('View transaction history for patent:', patent.id)
    }

    const downloadPatentDocument = async (patent) => {
      try {
        await patentService.downloadPatentDocument(patent.id, 'patent')
        notificationService.addNotification({
          type: 'success',
          title: '下载成功',
          message: `专利文档 "${patent.name}" 下载完成`,
          severity: 'success'
        })
      } catch (error) {
        notificationService.addNotification({
          type: 'error',
          title: '下载失败',
          message: error.message,
          severity: 'error'
        })
      }
    }

    // Patent action methods
    const withdrawPatent = async (patent) => {
      if (!confirm(`确定要撤回专利 "${patent.name}" 吗？撤回后将无法进行交易。`)) {
        return
      }

      try {
        isActionLoading.value = true
        await patentService.withdrawPatent(patent.id)

        // Update local data
        const index = uploadedPatents.value.findIndex(p => p.id === patent.id)
        if (index !== -1) {
          uploadedPatents.value[index].status = 'withdrawn'
        }

        notificationService.addNotification({
          type: 'success',
          title: '撤回成功',
          message: `专利 "${patent.name}" 已成功撤回`,
          severity: 'success'
        })
      } catch (error) {
        notificationService.addNotification({
          type: 'error',
          title: '撤回失败',
          message: error.message,
          severity: 'error'
        })
      } finally {
        isActionLoading.value = false
      }
    }

    const freezePatent = async (patent) => {
      if (!confirm(`确定要冻结专利 "${patent.name}" 吗？冻结后将暂时无法进行交易。`)) {
        return
      }

      try {
        isActionLoading.value = true
        await patentService.freezePatent(patent.id)

        // Update local data
        const index = uploadedPatents.value.findIndex(p => p.id === patent.id)
        if (index !== -1) {
          uploadedPatents.value[index].status = 'frozen'
        }

        notificationService.addNotification({
          type: 'success',
          title: '冻结成功',
          message: `专利 "${patent.name}" 已成功冻结`,
          severity: 'success'
        })
      } catch (error) {
        notificationService.addNotification({
          type: 'error',
          title: '冻结失败',
          message: error.message,
          severity: 'error'
        })
      } finally {
        isActionLoading.value = false
      }
    }

    const restorePatent = async (patent) => {
      if (!confirm(`确定要恢复专利 "${patent.name}" 吗？恢复后将可以重新进行交易。`)) {
        return
      }

      try {
        isActionLoading.value = true
        await patentService.restorePatent(patent.id)

        // Update local data
        const index = uploadedPatents.value.findIndex(p => p.id === patent.id)
        if (index !== -1) {
          uploadedPatents.value[index].status = 'approved'
        }

        notificationService.addNotification({
          type: 'success',
          title: '恢复成功',
          message: `专利 "${patent.name}" 已成功恢复`,
          severity: 'success'
        })
      } catch (error) {
        notificationService.addNotification({
          type: 'error',
          title: '恢复失败',
          message: error.message,
          severity: 'error'
        })
      } finally {
        isActionLoading.value = false
      }
    }

    // Load user's patent data from backend
    const loadUserPatents = async () => {
      if (!authStore.account) {
        console.warn('No user address available')
        return
      }

      try {
        isLoadingData.value = true

        // Load uploaded patents
        const uploadedData = await patentService.getUserUploadedPatents(authStore.account)
        uploadedPatents.value = uploadedData || []

        // Load purchased patents
        const purchasedData = await patentService.getUserPurchasedPatents(authStore.account)
        purchasedPatents.value = purchasedData.map(transaction => ({
          id: transaction.patentId,
          name: transaction.patentName,
          number: transaction.patentNumber,
          abstract: transaction.patentAbstract || 'No abstract available',
          purchasePrice: parseFloat(transaction.price),
          purchaseDate: transaction.submitDate,
          sellerName: transaction.sellerName,
          sellerAddress: transaction.sellerAddress,
          transactionHash: transaction.transactionHash || 'N/A'
        })) || []

        // Load sold patents (from user's transactions where they are the seller)
        const allTransactions = await patentService.getUserPurchasedPatents(authStore.account)
        // Filter for transactions where current user is the seller
        soldPatents.value = allTransactions.filter(transaction =>
          transaction.sellerAddress === authStore.account
        ).map(transaction => ({
          id: transaction.patentId,
          name: transaction.patentName,
          number: transaction.patentNumber,
          abstract: transaction.patentAbstract || 'No abstract available',
          salePrice: parseFloat(transaction.price),
          saleDate: transaction.submitDate,
          buyerName: transaction.buyerName,
          buyerAddress: transaction.buyerAddress,
          transactionHash: transaction.transactionHash || 'N/A'
        })) || []

      } catch (error) {
        console.error('Failed to load user patents:', error)
        notificationService.addNotification({
          type: 'error',
          title: '加载失败',
          message: '无法加载专利数据，请刷新页面重试',
          severity: 'error'
        })
      } finally {
        isLoadingData.value = false
      }
    }

    onMounted(() => {
      // Load user's patents from backend API
      if (authStore.isConnected && authStore.account) {
        loadUserPatents()
      }
    })

    return {
      authStore,
      uploadedPatents,
      purchasedPatents,
      soldPatents,
      isActionLoading,
      isLoadingData,
      getStatusBadgeClass,
      getStatusText,
      formatDate,
      formatAddress,
      formatPrice,
      canWithdrawPatent,
      canFreezePatent,
      getActionTooltip,
      viewPatentDetails,
      viewTransactionHistory,
      downloadPatentDocument,
      withdrawPatent,
      freezePatent,
      restorePatent,
      loadUserPatents
    }
  }
}
</script>

<style scoped>
.my-patents-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.nav-tabs .nav-link {
  border: none;
  color: #6c757d;
}

.nav-tabs .nav-link.active {
  background-color: #fff;
  border-bottom: 2px solid #0d6efd;
  color: #0d6efd;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card {
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
}
</style>
