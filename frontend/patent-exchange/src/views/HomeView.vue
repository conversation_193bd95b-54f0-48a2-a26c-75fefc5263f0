<template>
  <div class="home-view">
    <!-- Hero Section -->
    <section class="hero-section bg-primary text-white py-5">
      <div class="container-fluid">
        <div class="row align-items-center">
          <div class="col-lg-6">
            <h1 class="display-4 fw-bold mb-4">
              基于区块链的专利交易系统
            </h1>
            <p class="lead mb-4">
              安全、透明、高效的专利交易平台，使用区块链技术确保专利权的真实性和交易的可追溯性。
            </p>
            <div class="d-flex gap-3">
              <button
                v-if="!authStore.isConnected"
                class="btn btn-light btn-lg"
                @click="connectWallet"
                :disabled="authStore.isLoading"
              >
                <span v-if="authStore.isLoading" class="spinner-border spinner-border-sm me-2"></span>
                <i class="bi bi-wallet2 me-2"></i>
                连接钱包开始使用
              </button>
              <router-link
                v-else-if="authStore.isUser"
                to="/patents/search"
                class="btn btn-light btn-lg"
              >
                <i class="bi bi-search me-2"></i>
                搜索专利
              </router-link>
              <router-link
                v-else-if="authStore.isReviewer"
                to="/review/pending"
                class="btn btn-light btn-lg"
              >
                <i class="bi bi-clipboard-check me-2"></i>
                审核管理
              </router-link>
              <router-link
                v-else-if="authStore.isAdmin"
                to="/admin/users"
                class="btn btn-light btn-lg"
              >
                <i class="bi bi-gear me-2"></i>
                系统管理
              </router-link>
            </div>
          </div>
          <div class="col-lg-6 text-center">
            <i class="bi bi-shield-check display-1 text-white-50"></i>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section py-5">
      <div class="container-fluid">
        <div class="row text-center mb-5">
          <div class="col-12">
            <h2 class="fw-bold mb-3">系统特性</h2>
            <p class="text-muted">基于区块链技术的专利交易平台核心功能</p>
          </div>
        </div>

        <div class="row g-4">
          <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center p-4">
                <i class="bi bi-upload text-primary display-6 mb-3"></i>
                <h5 class="card-title">专利上传</h5>
                <p class="card-text text-muted">
                  支持专利文档上传到IPFS，确保文档的永久存储和防篡改
                </p>
              </div>
            </div>
          </div>

          <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center p-4">
                <i class="bi bi-search text-success display-6 mb-3"></i>
                <h5 class="card-title">智能搜索</h5>
                <p class="card-text text-muted">
                  根据专利名称、编号、类别进行模糊搜索，快速找到目标专利
                </p>
              </div>
            </div>
          </div>

          <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center p-4">
                <i class="bi bi-arrow-left-right text-warning display-6 mb-3"></i>
                <h5 class="card-title">安全交易</h5>
                <p class="card-text text-muted">
                  基于智能合约的专利交易，确保交易的安全性和透明度
                </p>
              </div>
            </div>
          </div>

          <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center p-4">
                <i class="bi bi-shield-check text-info display-6 mb-3"></i>
                <h5 class="card-title">专利维权</h5>
                <p class="card-text text-muted">
                  支持专利维权申请，通过区块链记录维权过程
                </p>
              </div>
            </div>
          </div>

          <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center p-4">
                <i class="bi bi-clipboard-check text-danger display-6 mb-3"></i>
                <h5 class="card-title">专业审核</h5>
                <p class="card-text text-muted">
                  专业审核方对专利上传、交易、维权进行严格审核
                </p>
              </div>
            </div>
          </div>

          <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center p-4">
                <i class="bi bi-graph-up text-secondary display-6 mb-3"></i>
                <h5 class="card-title">数据统计</h5>
                <p class="card-text text-muted">
                  完整的交易记录和统计分析，便于管理和监控
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Statistics Section -->
    <section v-if="authStore.isConnected" class="stats-section bg-light py-5">
      <div class="container-fluid">
        <div class="row text-center">
          <div class="col-md-3">
            <div class="stat-item">
              <h3 class="display-6 fw-bold text-primary">{{ stats.totalPatents }}</h3>
              <p class="text-muted">总专利数</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-item">
              <h3 class="display-6 fw-bold text-success">{{ stats.totalTransactions }}</h3>
              <p class="text-muted">总交易数</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-item">
              <h3 class="display-6 fw-bold text-warning">{{ stats.pendingReviews }}</h3>
              <p class="text-muted">待审核</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-item">
              <h3 class="display-6 fw-bold text-info">{{ stats.activeUsers }}</h3>
              <p class="text-muted">活跃用户</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <section v-if="!authStore.isConnected" class="cta-section py-5">
      <div class="container-fluid text-center">
        <h2 class="fw-bold mb-3">开始使用专利交易系统</h2>
        <p class="lead text-muted mb-4">
          连接您的MetaMask钱包，立即开始专利交易之旅
        </p>
        <button
          class="btn btn-primary btn-lg"
          @click="connectWallet"
          :disabled="authStore.isLoading"
        >
          <span v-if="authStore.isLoading" class="spinner-border spinner-border-sm me-2"></span>
          <i class="bi bi-wallet2 me-2"></i>
          连接MetaMask钱包
        </button>
      </div>
    </section>

    <!-- Role Manager (Development Tool) -->
    <section v-if="authStore.isConnected" class="role-manager-section py-5 bg-light">
      <div class="container-fluid">
        <RoleManager />
      </div>
    </section>
  </div>
</template>

<script>
import { useAuthStore } from '@/stores/auth'
import { ref, onMounted } from 'vue'
import RoleManager from '@/components/RoleManager.vue'
import api from '@/services/apiClient'

export default {
  name: 'HomeView',
  components: {
    RoleManager
  },
  setup() {
    const authStore = useAuthStore()

    // Real statistics data from backend
    const stats = ref({
      totalPatents: 0,
      totalTransactions: 0,
      pendingReviews: 0,
      activeUsers: 0
    })

    const isLoadingStats = ref(false)

    const connectWallet = async () => {
      await authStore.connectWallet()
    }

    const loadStatistics = async () => {
      try {
        isLoadingStats.value = true

        // Only load statistics if user is admin
        if (!authStore.isAdmin) {
          console.log('User is not admin, skipping statistics load')
          return
        }

        const response = await api.admin.getOverviewStats()

        if (response.data.success) {
          const data = response.data.data
          stats.value = {
            totalPatents: data.patents?.total || 0,
            totalTransactions: data.transactions?.total || 0,
            pendingReviews: (data.patents?.pending || 0) + (data.transactions?.pending || 0) + (data.protectionCases?.pending || 0),
            activeUsers: data.users?.active || 0
          }
        }
      } catch (error) {
        console.error('Failed to load statistics:', error)
        // For non-admin users or API errors, show placeholder values
        stats.value = {
          totalPatents: '---',
          totalTransactions: '---',
          pendingReviews: '---',
          activeUsers: '---'
        }
      } finally {
        isLoadingStats.value = false
      }
    }

    onMounted(() => {
      // Load real statistics from backend API
      if (authStore.isConnected) {
        loadStatistics()
      }
    })

    return {
      authStore,
      stats,
      isLoadingStats,
      connectWallet
    }
  }
}
</script>

<style scoped>
.hero-section {
  background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
}

.features-section .card {
  transition: transform 0.2s ease-in-out;
}

.features-section .card:hover {
  transform: translateY(-5px);
}

.stat-item {
  padding: 1rem;
}

.cta-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
</style>
