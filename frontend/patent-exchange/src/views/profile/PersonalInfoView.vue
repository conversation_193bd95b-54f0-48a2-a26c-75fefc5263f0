<template>
  <div class="personal-info-view">
    <div class="container-fluid py-4">
      <div class="d-flex align-items-center mb-4">
        <i class="bi bi-person-circle text-primary me-3" style="font-size: 2rem;"></i>
        <div>
          <h2 class="mb-1">个人信息</h2>
          <p class="text-muted mb-0">管理您的个人资料信息</p>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="text-muted mt-3">正在加载个人信息...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="alert alert-danger" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>
        {{ error }}
      </div>

      <!-- Personal Information Form -->
      <div v-else class="row">
        <div class="col-lg-8">
          <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
              <h5 class="mb-0">
                <i class="bi bi-person me-2"></i>
                基本信息
              </h5>
            </div>
            <div class="card-body">
              <form @submit.prevent="updateProfile">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="name" class="form-label">姓名 <span class="text-danger">*</span></label>
                    <input
                      type="text"
                      class="form-control"
                      id="name"
                      v-model="profileForm.name"
                      :class="{ 'is-invalid': validationErrors.name }"
                      required
                    >
                    <div v-if="validationErrors.name" class="invalid-feedback">
                      {{ validationErrors.name }}
                    </div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">手机号码 <span class="text-danger">*</span></label>
                    <input
                      type="tel"
                      class="form-control"
                      id="phone"
                      v-model="profileForm.phone"
                      :class="{ 'is-invalid': validationErrors.phone }"
                      required
                    >
                    <div v-if="validationErrors.phone" class="invalid-feedback">
                      {{ validationErrors.phone }}
                    </div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label for="idNumber" class="form-label">身份证号码 <span class="text-danger">*</span></label>
                    <input
                      type="text"
                      class="form-control"
                      id="idNumber"
                      v-model="profileForm.idNumber"
                      :class="{ 'is-invalid': validationErrors.idNumber }"
                      required
                    >
                    <div v-if="validationErrors.idNumber" class="invalid-feedback">
                      {{ validationErrors.idNumber }}
                    </div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label for="address" class="form-label">区块链地址</label>
                    <input
                      type="text"
                      class="form-control"
                      id="address"
                      :value="authStore.account"
                      readonly
                      style="background-color: #f8f9fa;"
                    >
                    <div class="form-text">区块链地址为只读，无法修改</div>
                  </div>
                </div>

                <div class="d-flex justify-content-between">
                  <button
                    type="button"
                    class="btn btn-outline-secondary"
                    @click="resetForm"
                    :disabled="isUpdating"
                  >
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    重置
                  </button>
                  <button
                    type="submit"
                    class="btn btn-primary"
                    :disabled="isUpdating"
                  >
                    <span v-if="isUpdating" class="spinner-border spinner-border-sm me-2"></span>
                    <i v-else class="bi bi-check-lg me-2"></i>
                    {{ isUpdating ? '更新中...' : '保存更改' }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div class="col-lg-4">
          <div class="card shadow-sm">
            <div class="card-header bg-info text-white">
              <h5 class="mb-0">
                <i class="bi bi-info-circle me-2"></i>
                账户统计
              </h5>
            </div>
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <span class="text-muted">注册时间</span>
                <span class="fw-bold">{{ formatDate(userProfile.registrationDate) }}</span>
              </div>
              <div class="d-flex justify-content-between align-items-center mb-3">
                <span class="text-muted">最后登录</span>
                <span class="fw-bold">{{ formatDate(userProfile.lastLoginDate) }}</span>
              </div>
              <div class="d-flex justify-content-between align-items-center">
                <span class="text-muted">用户角色</span>
                <span class="badge bg-success">{{ roleText }}</span>
              </div>
            </div>
          </div>

          <div class="card shadow-sm mt-3">
            <div class="card-header bg-warning text-dark">
              <h5 class="mb-0">
                <i class="bi bi-shield-exclamation me-2"></i>
                安全提示
              </h5>
            </div>
            <div class="card-body">
              <ul class="list-unstyled mb-0">
                <li class="mb-2">
                  <i class="bi bi-check-circle text-success me-2"></i>
                  请确保个人信息真实有效
                </li>
                <li class="mb-2">
                  <i class="bi bi-check-circle text-success me-2"></i>
                  区块链地址无法修改，请妥善保管
                </li>
                <li class="mb-0">
                  <i class="bi bi-check-circle text-success me-2"></i>
                  个人信息将用于专利交易验证
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Success Message -->
      <div v-if="successMessage" class="alert alert-success alert-dismissible fade show mt-3" role="alert">
        <i class="bi bi-check-circle me-2"></i>
        {{ successMessage }}
        <button type="button" class="btn-close" @click="successMessage = null"></button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { userService } from '@/services/userService'

export default {
  name: 'PersonalInfoView',
  setup() {
    const authStore = useAuthStore()
    
    const isLoading = ref(false)
    const isUpdating = ref(false)
    const error = ref(null)
    const successMessage = ref(null)
    
    const userProfile = ref({})
    const profileForm = reactive({
      name: '',
      phone: '',
      idNumber: ''
    })
    
    const validationErrors = ref({})

    const roleText = computed(() => {
      switch (authStore.userRole) {
        case 'admin': return '管理员'
        case 'reviewer': return '审核方'
        case 'user': return '用户'
        default: return '未知'
      }
    })

    const loadUserProfile = async () => {
      try {
        isLoading.value = true
        error.value = null
        
        const profile = await userService.getUserProfile(authStore.account)
        userProfile.value = profile
        
        // Populate form
        profileForm.name = profile.name
        profileForm.phone = profile.phone
        profileForm.idNumber = profile.idNumber
        
      } catch (err) {
        error.value = err.message
      } finally {
        isLoading.value = false
      }
    }

    const updateProfile = async () => {
      try {
        // Validate form
        const validation = userService.validateProfile(profileForm)
        if (!validation.isValid) {
          validationErrors.value = validation.errors.reduce((acc, error) => {
            if (error.includes('姓名')) acc.name = error
            if (error.includes('手机')) acc.phone = error
            if (error.includes('身份证')) acc.idNumber = error
            return acc
          }, {})
          return
        }
        
        validationErrors.value = {}
        isUpdating.value = true
        
        await userService.updateUserProfile(authStore.account, profileForm)
        
        successMessage.value = '个人信息更新成功'
        
        // Reload profile
        await loadUserProfile()
        
      } catch (err) {
        error.value = err.message
      } finally {
        isUpdating.value = false
      }
    }

    const resetForm = () => {
      profileForm.name = userProfile.value.name
      profileForm.phone = userProfile.value.phone
      profileForm.idNumber = userProfile.value.idNumber
      validationErrors.value = {}
    }

    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    onMounted(() => {
      if (authStore.isConnected) {
        loadUserProfile()
      }
    })

    return {
      authStore,
      isLoading,
      isUpdating,
      error,
      successMessage,
      userProfile,
      profileForm,
      validationErrors,
      roleText,
      updateProfile,
      resetForm,
      formatDate
    }
  }
}
</script>

<style scoped>
.personal-info-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 10px;
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
}

.form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn {
  border-radius: 6px;
}

.alert {
  border-radius: 8px;
}
</style>
