<template>
  <div class="notifications-view">
    <div class="container-fluid py-4">
      <!-- Header -->
      <div class="d-flex align-items-center justify-content-between mb-4">
        <div class="d-flex align-items-center">
          <i class="bi bi-bell text-primary me-3" style="font-size: 2rem;"></i>
          <div>
            <h2 class="mb-1">通知中心</h2>
            <p class="text-muted mb-0">查看所有系统通知</p>
          </div>
        </div>
        <div class="d-flex gap-2">
          <button class="btn btn-outline-secondary" @click="markAllAsRead" :disabled="unreadCount === 0">
            <i class="bi bi-check-all me-2"></i>
            全部已读
          </button>
          <button class="btn btn-outline-danger" @click="clearAll" :disabled="notifications.length === 0">
            <i class="bi bi-trash me-2"></i>
            清空全部
          </button>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="row g-4 mb-4">
        <div class="col-md-3">
          <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
              <div class="bg-primary bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                <i class="bi bi-bell text-primary" style="font-size: 1.5rem;"></i>
              </div>
              <h3 class="mb-1">{{ notifications.length }}</h3>
              <small class="text-muted">总通知数</small>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
              <div class="bg-warning bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                <i class="bi bi-bell-fill text-warning" style="font-size: 1.5rem;"></i>
              </div>
              <h3 class="mb-1">{{ unreadCount }}</h3>
              <small class="text-muted">未读通知</small>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
              <div class="bg-success bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                <i class="bi bi-check-circle text-success" style="font-size: 1.5rem;"></i>
              </div>
              <h3 class="mb-1">{{ readCount }}</h3>
              <small class="text-muted">已读通知</small>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
              <div class="bg-info bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                <i class="bi bi-calendar-week text-info" style="font-size: 1.5rem;"></i>
              </div>
              <h3 class="mb-1">{{ todayCount }}</h3>
              <small class="text-muted">今日通知</small>
            </div>
          </div>
        </div>
      </div>

      <!-- Filter Tabs -->
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <ul class="nav nav-tabs card-header-tabs" id="notificationTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button
                class="nav-link active"
                id="all-tab"
                data-bs-toggle="tab"
                data-bs-target="#all"
                type="button"
                role="tab"
              >
                <i class="bi bi-list-ul me-2"></i>
                全部 <span class="badge bg-primary ms-1">{{ notifications.length }}</span>
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                id="unread-tab"
                data-bs-toggle="tab"
                data-bs-target="#unread"
                type="button"
                role="tab"
              >
                <i class="bi bi-bell me-2"></i>
                未读 <span class="badge bg-warning ms-1">{{ unreadCount }}</span>
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                id="patent-tab"
                data-bs-toggle="tab"
                data-bs-target="#patent"
                type="button"
                role="tab"
              >
                <i class="bi bi-file-earmark-text me-2"></i>
                专利相关 <span class="badge bg-info ms-1">{{ patentNotifications.length }}</span>
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                id="transaction-tab"
                data-bs-toggle="tab"
                data-bs-target="#transaction"
                type="button"
                role="tab"
              >
                <i class="bi bi-currency-exchange me-2"></i>
                交易相关 <span class="badge bg-success ms-1">{{ transactionNotifications.length }}</span>
              </button>
            </li>
          </ul>
        </div>

        <div class="card-body p-0">
          <div class="tab-content" id="notificationTabsContent">
            <!-- All Notifications -->
            <div class="tab-pane fade show active" id="all" role="tabpanel">
              <NotificationList :notifications="notifications" @mark-read="markAsRead" @remove="removeNotification" />
            </div>

            <!-- Unread Notifications -->
            <div class="tab-pane fade" id="unread" role="tabpanel">
              <NotificationList :notifications="unreadNotifications" @mark-read="markAsRead" @remove="removeNotification" />
            </div>

            <!-- Patent Notifications -->
            <div class="tab-pane fade" id="patent" role="tabpanel">
              <NotificationList :notifications="patentNotifications" @mark-read="markAsRead" @remove="removeNotification" />
            </div>

            <!-- Transaction Notifications -->
            <div class="tab-pane fade" id="transaction" role="tabpanel">
              <NotificationList :notifications="transactionNotifications" @mark-read="markAsRead" @remove="removeNotification" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { notificationService } from '@/services/notificationService'
import NotificationList from '@/components/NotificationList.vue'

export default {
  name: 'NotificationsView',
  components: {
    NotificationList
  },
  setup() {
    const notifications = computed(() => notificationService.notifications.value)
    const unreadCount = computed(() => notificationService.unreadCount.value)
    
    const readCount = computed(() => 
      notifications.value.filter(n => n.read).length
    )
    
    const todayCount = computed(() => {
      const today = new Date().toDateString()
      return notifications.value.filter(n => 
        new Date(n.timestamp).toDateString() === today
      ).length
    })
    
    const unreadNotifications = computed(() => 
      notifications.value.filter(n => !n.read)
    )
    
    const patentNotifications = computed(() => 
      notifications.value.filter(n => 
        ['patent_approved', 'patent_rejected', 'rights_protection_initiated', 'rights_protection_resolved'].includes(n.type)
      )
    )
    
    const transactionNotifications = computed(() => 
      notifications.value.filter(n => 
        ['patent_sold', 'patent_purchased', 'transaction_completed'].includes(n.type)
      )
    )
    
    const markAllAsRead = () => {
      notificationService.markAllAsRead()
    }
    
    const clearAll = () => {
      if (confirm('确定要清空所有通知吗？此操作不可撤销。')) {
        notificationService.clearAll()
      }
    }
    
    const markAsRead = (notificationId) => {
      notificationService.markAsRead(notificationId)
    }
    
    const removeNotification = (notificationId) => {
      notificationService.removeNotification(notificationId)
    }
    
    return {
      notifications,
      unreadCount,
      readCount,
      todayCount,
      unreadNotifications,
      patentNotifications,
      transactionNotifications,
      markAllAsRead,
      clearAll,
      markAsRead,
      removeNotification
    }
  }
}
</script>

<style scoped>
.notifications-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 12px;
}

.card-header {
  border-radius: 12px 12px 0 0 !important;
}

.nav-tabs .nav-link {
  border: none;
  color: #6c757d;
}

.nav-tabs .nav-link.active {
  background-color: transparent;
  border-bottom: 2px solid #0d6efd;
  color: #0d6efd;
}

.badge {
  font-size: 0.7rem;
}
</style>
