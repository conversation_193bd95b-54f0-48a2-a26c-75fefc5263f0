<template>
  <div class="review-pending-view">
    <div class="container-fluid py-4">
      <!-- Header -->
      <div class="d-flex align-items-center justify-content-between mb-4">
        <div class="d-flex align-items-center">
          <i class="bi bi-clipboard-check text-primary me-3" style="font-size: 2rem;"></i>
          <div>
            <h2 class="mb-1">待审核列表</h2>
            <p class="text-muted mb-0">审核待处理的专利申请</p>
          </div>
        </div>
        <div class="d-flex gap-2">
          <button
            class="btn btn-outline-primary btn-sm"
            @click="loadPendingUploads"
            :disabled="isLoading"
          >
            <i class="bi bi-arrow-clockwise me-1"></i>
            刷新
          </button>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card bg-warning text-dark">
            <div class="card-body text-center">
              <i class="bi bi-clock-history" style="font-size: 2rem;"></i>
              <h4 class="mt-2 mb-0">{{ pendingUploads.length }}</h4>
              <small>待审核专利</small>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-success text-white">
            <div class="card-body text-center">
              <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
              <h4 class="mt-2 mb-0">{{ approvedCount }}</h4>
              <small>已批准</small>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-danger text-white">
            <div class="card-body text-center">
              <i class="bi bi-x-circle" style="font-size: 2rem;"></i>
              <h4 class="mt-2 mb-0">{{ rejectedCount }}</h4>
              <small>已拒绝</small>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-info text-white">
            <div class="card-body text-center">
              <i class="bi bi-file-earmark-text" style="font-size: 2rem;"></i>
              <h4 class="mt-2 mb-0">{{ totalCount }}</h4>
              <small>总计</small>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="text-muted mt-3">正在加载待审核专利...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="alert alert-danger" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>
        {{ error }}
      </div>

      <!-- Success Message -->
      <div v-if="successMessage" class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle me-2"></i>
        {{ successMessage }}
        <button type="button" class="btn-close" @click="successMessage = null"></button>
      </div>

      <!-- Empty State -->
      <div v-else-if="!isLoading && pendingUploads.length === 0" class="text-center py-5">
        <i class="bi bi-clipboard-check text-muted" style="font-size: 4rem;"></i>
        <h4 class="text-muted mt-3">暂无待审核专利</h4>
        <p class="text-muted">所有专利申请都已处理完毕</p>
      </div>

      <!-- Pending Uploads List -->
      <div v-else class="row">
        <div v-for="upload in pendingUploads" :key="upload.id" class="col-lg-6 mb-4">
          <div class="card shadow-sm h-100">
            <div class="card-header bg-warning text-dark">
              <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                  <i class="bi bi-file-earmark-text me-2"></i>
                  {{ upload.patentName }}
                </h6>
                <span class="badge bg-dark">待审核</span>
              </div>
            </div>
            <div class="card-body">
              <!-- Patent Information -->
              <div class="row g-2 mb-3">
                <div class="col-6">
                  <small class="text-muted">专利号</small>
                  <div class="fw-bold small">{{ upload.patentNumber }}</div>
                </div>
                <div class="col-6">
                  <small class="text-muted">分类</small>
                  <div class="fw-bold small">{{ upload.category }}</div>
                </div>
                <div class="col-6">
                  <small class="text-muted">转让价格</small>
                  <div class="fw-bold small text-success">{{ upload.price }} ETH</div>
                </div>
                <div class="col-6">
                  <small class="text-muted">提交时间</small>
                  <div class="fw-bold small">{{ upload.submitDate }}</div>
                </div>
              </div>

              <!-- Owner Information -->
              <div class="mb-3">
                <small class="text-muted d-block">专利权人</small>
                <div class="fw-bold">{{ upload.ownerName }}</div>
                <small class="text-muted">身份证号: {{ upload.ownerIdNumber }}</small>
              </div>

              <!-- Uploader Information -->
              <div class="mb-3">
                <small class="text-muted d-block">上传用户</small>
                <div class="d-flex align-items-center">
                  <span class="fw-bold me-2">{{ upload.uploaderName }}</span>
                  <button
                    class="btn btn-link btn-sm p-0 text-decoration-none"
                    @click="showUserDetails(upload.uploaderAddress)"
                  >
                    {{ upload.uploaderAddressShort }}
                  </button>
                </div>
                <small class="text-muted">电话: {{ upload.uploaderPhone }}</small>
              </div>

              <!-- Agent Sale Badge -->
              <div v-if="upload.isAgentSale" class="mb-3">
                <span class="badge bg-secondary">
                  <i class="bi bi-person-badge me-1"></i>
                  代理出售
                </span>
              </div>

              <!-- Abstract -->
              <div v-if="upload.abstract" class="mb-3">
                <small class="text-muted d-block">专利摘要</small>
                <p class="small text-truncate-3">{{ upload.abstract }}</p>
              </div>

              <!-- Documents -->
              <div class="mb-3">
                <small class="text-muted d-block mb-2">相关文档</small>
                <div class="d-flex gap-2">
                  <button
                    class="btn btn-outline-info btn-sm"
                    @click="viewDocument(upload.id, 'patent')"
                  >
                    <i class="bi bi-file-earmark-pdf me-1"></i>
                    专利文档
                  </button>
                  <button
                    class="btn btn-outline-info btn-sm"
                    @click="viewDocument(upload.id, 'ownership')"
                  >
                    <i class="bi bi-file-earmark-check me-1"></i>
                    权属证明
                  </button>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="d-flex gap-2">
                <button
                  class="btn btn-outline-primary btn-sm flex-fill"
                  @click="viewPatentDetails(upload)"
                >
                  <i class="bi bi-eye me-1"></i>
                  查看详情
                </button>
                <button
                  class="btn btn-success btn-sm flex-fill"
                  @click="showApproveModal(upload)"
                  :disabled="isProcessing && processingId === upload.id"
                >
                  <i class="bi bi-check-lg me-1"></i>
                  批准
                </button>
                <button
                  class="btn btn-danger btn-sm flex-fill"
                  @click="showRejectModal(upload)"
                  :disabled="isProcessing && processingId === upload.id"
                >
                  <i class="bi bi-x-lg me-1"></i>
                  拒绝
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Approve Modal -->
    <div class="modal fade" id="approveModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="bi bi-check-circle text-success me-2"></i>
              批准专利申请
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div v-if="selectedUpload">
              <div class="alert alert-info">
                <strong>{{ selectedUpload.patentName }}</strong><br>
                <small>专利号: {{ selectedUpload.patentNumber }}</small>
              </div>

              <div class="mb-3">
                <label class="form-label">审核意见 (可选)</label>
                <textarea
                  v-model="approveComments"
                  class="form-control"
                  rows="3"
                  placeholder="请输入审核意见..."
                ></textarea>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button
              type="button"
              class="btn btn-success"
              @click="confirmApprove"
              :disabled="isProcessing"
            >
              <span v-if="isProcessing" class="spinner-border spinner-border-sm me-2"></span>
              确认批准
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Reject Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="bi bi-x-circle text-danger me-2"></i>
              拒绝专利申请
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div v-if="selectedUpload">
              <div class="alert alert-warning">
                <strong>{{ selectedUpload.patentName }}</strong><br>
                <small>专利号: {{ selectedUpload.patentNumber }}</small>
              </div>

              <div class="mb-3">
                <label class="form-label">拒绝原因 <span class="text-danger">*</span></label>
                <select v-model="rejectReason" class="form-select mb-2">
                  <option value="">请选择拒绝原因</option>
                  <option v-for="template in rejectTemplates" :key="template" :value="template">
                    {{ template }}
                  </option>
                </select>
                <textarea
                  v-model="rejectReason"
                  class="form-control"
                  rows="3"
                  placeholder="请详细说明拒绝原因..."
                  :class="{ 'is-invalid': rejectReasonError }"
                ></textarea>
                <div v-if="rejectReasonError" class="invalid-feedback">
                  {{ rejectReasonError }}
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button
              type="button"
              class="btn btn-danger"
              @click="confirmReject"
              :disabled="isProcessing || !rejectReason"
            >
              <span v-if="isProcessing" class="spinner-border spinner-border-sm me-2"></span>
              确认拒绝
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- User Detail Modal -->
    <UserDetailModal
      modal-id="userDetailModal"
      :user-address="selectedUserAddress"
    />
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { notificationService } from '@/services/notificationService'
import { api } from '@/services/apiClient'
import UserDetailModal from '@/components/UserDetailModal.vue'
import { Modal } from 'bootstrap'

export default {
  name: 'ReviewPendingView',
  components: {
    UserDetailModal
  },
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()

    // Reactive data
    const isLoading = ref(false)
    const isProcessing = ref(false)
    const processingId = ref(null)
    const error = ref(null)
    const successMessage = ref(null)
    const pendingUploads = ref([])
    const selectedUpload = ref(null)
    const selectedUserAddress = ref('')
    const approveComments = ref('')
    const rejectReason = ref('')
    const rejectReasonError = ref('')

    // Statistics from backend API
    const approvedCount = ref(0)
    const rejectedCount = ref(0)

    // Computed
    const totalCount = computed(() => {
      return approvedCount.value + rejectedCount.value + pendingUploads.value.length
    })

    // Reject reason templates
    const rejectTemplates = [
      '专利文档不完整或不清晰',
      '权属证明文档无效',
      '专利信息填写错误',
      '专利已过期或无效',
      '重复申请',
      '不符合平台要求',
      '涉嫌侵权或争议',
      '其他技术或法律问题'
    ]

    // Load pending uploads
    const loadPendingUploads = async () => {
      try {
        isLoading.value = true
        error.value = null

        // Load both pending uploads and statistics
        const [uploadsResponse, statsResponse] = await Promise.all([
          api.review.getPendingUploads(),
          api.review.getStatistics()
        ])

        // Handle the uploads data structure
        pendingUploads.value = uploadsResponse.data.data.data || uploadsResponse.data.data || []

        // Handle the statistics data
        if (statsResponse.data.success && statsResponse.data.data) {
          const stats = statsResponse.data.data
          approvedCount.value = stats.patents.approved || 0
          rejectedCount.value = stats.patents.rejected || 0
        }

        console.log(`✅ Loaded ${pendingUploads.value.length} pending uploads`)
        console.log(`📊 Statistics: ${approvedCount.value} approved, ${rejectedCount.value} rejected`)

      } catch (err) {
        error.value = err.message || '加载待审核专利失败'
        console.error('Failed to load pending uploads:', err)
        
        // Fallback to empty data on error
        pendingUploads.value = []
        approvedCount.value = 0
        rejectedCount.value = 0
      } finally {
        isLoading.value = false
      }
    }

    // View patent details
    const viewPatentDetails = (upload) => {
      router.push(`/patent/${upload.id}`)
    }

    // Show user details modal
    const showUserDetails = (address) => {
      selectedUserAddress.value = address
      const modal = new Modal(document.getElementById('userDetailModal'))
      modal.show()
    }

    // View document
    const viewDocument = (patentId, documentType) => {
      // Open document in new tab
      const url = `/api/patents/${patentId}/download/${documentType}`
      window.open(url, '_blank')
    }

    // Show approve modal
    const showApproveModal = (upload) => {
      selectedUpload.value = upload
      approveComments.value = ''
      const modal = new Modal(document.getElementById('approveModal'))
      modal.show()
    }

    // Show reject modal
    const showRejectModal = (upload) => {
      selectedUpload.value = upload
      rejectReason.value = ''
      rejectReasonError.value = ''
      const modal = new Modal(document.getElementById('rejectModal'))
      modal.show()
    }

    // Confirm approve
    const confirmApprove = async () => {
      if (!selectedUpload.value) return

      try {
        isProcessing.value = true
        processingId.value = selectedUpload.value.id

        const response = await api.review.approveUpload(selectedUpload.value.id, {
          reviewerAddress: authStore.account,
          comments: approveComments.value || 'Patent documentation verified and approved'
        })

        if (response.data.success) {
          successMessage.value = response.data.message || '专利申请已批准'

          // Remove from pending list
          pendingUploads.value = pendingUploads.value.filter(
            upload => upload.id !== selectedUpload.value.id
          )

          // Reload statistics to get updated counts
          try {
            const statsResponse = await api.review.getStatistics()
            if (statsResponse.data.success && statsResponse.data.data) {
              const stats = statsResponse.data.data
              approvedCount.value = stats.patents.approved || 0
              rejectedCount.value = stats.patents.rejected || 0
            }
          } catch (statsError) {
            console.warn('Failed to reload statistics:', statsError)
            // Fallback: just increment the approved count
            approvedCount.value++
          }

          // Close modal
          const modal = Modal.getInstance(document.getElementById('approveModal'))
          modal.hide()

          // Clear form
          selectedUpload.value = null
          approveComments.value = ''
        }

      } catch (err) {
        error.value = err.message || '批准专利申请失败'
        console.error('Failed to approve upload:', err)
      } finally {
        isProcessing.value = false
        processingId.value = null
      }
    }

    // Confirm reject
    const confirmReject = async () => {
      if (!selectedUpload.value) return

      // Validate reject reason
      if (!rejectReason.value.trim()) {
        rejectReasonError.value = '请填写拒绝原因'
        return
      }

      try {
        isProcessing.value = true
        processingId.value = selectedUpload.value.id
        rejectReasonError.value = ''

        const response = await api.review.rejectUpload(selectedUpload.value.id, {
          reviewerAddress: authStore.account,
          reason: rejectReason.value.trim()
        })

        if (response.data.success) {
          successMessage.value = response.data.message || '专利申请已拒绝'

          // Remove from pending list
          pendingUploads.value = pendingUploads.value.filter(
            upload => upload.id !== selectedUpload.value.id
          )

          // Reload statistics to get updated counts
          try {
            const statsResponse = await api.review.getStatistics()
            if (statsResponse.data.success && statsResponse.data.data) {
              const stats = statsResponse.data.data
              approvedCount.value = stats.patents.approved || 0
              rejectedCount.value = stats.patents.rejected || 0
            }
          } catch (statsError) {
            console.warn('Failed to reload statistics:', statsError)
            // Fallback: just increment the rejected count
            rejectedCount.value++
          }

          // Close modal
          const modal = Modal.getInstance(document.getElementById('rejectModal'))
          modal.hide()

          // Clear form
          selectedUpload.value = null
          rejectReason.value = ''
        }

      } catch (err) {
        error.value = err.message || '拒绝专利申请失败'
        console.error('Failed to reject upload:', err)
      } finally {
        isProcessing.value = false
        processingId.value = null
      }
    }

    // Initialize
    onMounted(() => {
      if (authStore.isConnected && (authStore.userRole === 'admin' || authStore.userRole === 'reviewer')) {
        loadPendingUploads()
      } else {
        error.value = '您没有权限访问此页面'
      }
    })

    return {
      authStore,
      isLoading,
      isProcessing,
      processingId,
      error,
      successMessage,
      pendingUploads,
      selectedUpload,
      selectedUserAddress,
      approveComments,
      rejectReason,
      rejectReasonError,
      approvedCount,
      rejectedCount,
      totalCount,
      rejectTemplates,
      loadPendingUploads,
      viewPatentDetails,
      showUserDetails,
      viewDocument,
      showApproveModal,
      showRejectModal,
      confirmApprove,
      confirmReject
    }
  }
}
</script>

<style scoped>
.review-pending-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 10px;
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.btn-sm {
  font-size: 0.875rem;
}

.modal-content {
  border-radius: 10px;
}

.modal-header {
  border-bottom: 1px solid #dee2e6;
  border-radius: 10px 10px 0 0;
}

.modal-footer {
  border-top: 1px solid #dee2e6;
  border-radius: 0 0 10px 10px;
}
</style>