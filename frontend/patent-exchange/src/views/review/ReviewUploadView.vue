<template>
  <div class="review-upload-view">
    <div class="container-fluid py-4">
      <!-- Header -->
      <div class="d-flex align-items-center mb-4">
        <i class="bi bi-upload text-primary me-3" style="font-size: 2rem;"></i>
        <div>
          <h2 class="mb-1">上传审核</h2>
          <p class="text-muted mb-0">审核专利上传申请</p>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card border-0 bg-primary text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-1">待审核</h6>
                  <h3 class="mb-0">{{ pendingUploads.length }}</h3>
                </div>
                <i class="bi bi-clock-history fs-1 opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 bg-success text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-1">已批准</h6>
                  <h3 class="mb-0">{{ approvedCount }}</h3>
                </div>
                <i class="bi bi-check-circle fs-1 opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 bg-danger text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-1">已拒绝</h6>
                  <h3 class="mb-0">{{ rejectedCount }}</h3>
                </div>
                <i class="bi bi-x-circle fs-1 opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 bg-info text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-1">总计</h6>
                  <h3 class="mb-0">{{ totalCount }}</h3>
                </div>
                <i class="bi bi-bar-chart fs-1 opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Alert -->
      <div v-if="error" class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>
        {{ error }}
        <button type="button" class="btn-close" @click="error = null"></button>
      </div>

      <!-- Success Alert -->
      <div v-if="successMessage" class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle me-2"></i>
        {{ successMessage }}
        <button type="button" class="btn-close" @click="successMessage = null"></button>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="text-muted mt-3">正在加载待审核上传...</p>
      </div>

      <!-- Pending Uploads List -->
      <div v-else-if="pendingUploads.length > 0" class="row">
        <div v-for="upload in pendingUploads" :key="upload.id" class="col-12 mb-4">
          <div class="card shadow-sm">
            <div class="card-header bg-light">
              <div class="row align-items-center">
                <div class="col">
                  <h5 class="mb-1">
                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                    {{ upload.patentName }}
                  </h5>
                  <small class="text-muted">专利号: {{ upload.patentNumber }}</small>
                </div>
                <div class="col-auto">
                  <span class="badge bg-warning">待审核</span>
                </div>
              </div>
            </div>
            <div class="card-body">
              <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6">
                  <h6 class="text-primary mb-3">
                    <i class="bi bi-info-circle me-2"></i>
                    基本信息
                  </h6>
                  <div class="mb-2">
                    <strong>上传者:</strong>
                    <a
                      href="#"
                      class="text-decoration-none ms-2"
                      @click.prevent="showUserDetails(upload.uploaderAddress)"
                    >
                      {{ upload.uploaderName }}
                      <small class="text-muted">({{ formatAddress(upload.uploaderAddress) }})</small>
                    </a>
                  </div>
                  <div class="mb-2">
                    <strong>提交时间:</strong>
                    <span class="ms-2">{{ formatDate(upload.submitDate) }}</span>
                  </div>
                  <div class="mb-2">
                    <strong>转让价格:</strong>
                    <span class="ms-2 text-success fw-bold">¥{{ formatPrice(upload.transferPrice) }}</span>
                  </div>
                  <div class="mb-2">
                    <strong>代理销售:</strong>
                    <span class="ms-2">
                      <span v-if="upload.isAgentSale" class="badge bg-success">是</span>
                      <span v-else class="badge bg-secondary">否</span>
                    </span>
                  </div>
                </div>

                <!-- Patent Details -->
                <div class="col-md-6">
                  <h6 class="text-primary mb-3">
                    <i class="bi bi-file-text me-2"></i>
                    专利详情
                  </h6>
                  <div class="mb-2">
                    <strong>申请日期:</strong>
                    <span class="ms-2">{{ formatDate(upload.applicationDate) }}</span>
                  </div>
                  <div class="mb-2">
                    <strong>到期日期:</strong>
                    <span class="ms-2">{{ formatDate(upload.expirationDate) }}</span>
                  </div>
                  <div class="mb-2">
                    <strong>专利权人:</strong>
                    <span class="ms-2">{{ upload.ownerName }}</span>
                  </div>
                  <div class="mb-2">
                    <strong>身份证号:</strong>
                    <span class="ms-2 font-monospace">{{ maskIdNumber(upload.ownerIdNumber) }}</span>
                  </div>
                </div>
              </div>

              <!-- Patent Abstract -->
              <div class="mt-3">
                <h6 class="text-primary mb-2">
                  <i class="bi bi-card-text me-2"></i>
                  专利摘要
                </h6>
                <div class="bg-light p-3 rounded">
                  <p class="mb-0 text-muted">{{ upload.patentAbstract }}</p>
                </div>
              </div>

              <!-- Documents -->
              <div class="mt-3">
                <h6 class="text-primary mb-2">
                  <i class="bi bi-paperclip me-2"></i>
                  相关文档
                </h6>
                <div class="d-flex gap-2 flex-wrap">
                  <a
                    :href="upload.documentUrl"
                    target="_blank"
                    class="btn btn-outline-primary btn-sm"
                  >
                    <i class="bi bi-file-earmark-pdf me-2"></i>
                    专利文档
                  </a>
                  <a
                    v-if="upload.proxyDocumentUrl"
                    :href="upload.proxyDocumentUrl"
                    target="_blank"
                    class="btn btn-outline-secondary btn-sm"
                  >
                    <i class="bi bi-file-earmark-text me-2"></i>
                    代理证书
                  </a>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="mt-4 d-flex gap-2">
                <button
                  class="btn btn-success flex-fill"
                  @click="approveUpload(upload)"
                  :disabled="isProcessing"
                >
                  <span v-if="isProcessing && processingId === upload.id" class="spinner-border spinner-border-sm me-2"></span>
                  <i v-else class="bi bi-check-lg me-2"></i>
                  批准
                </button>
                <button
                  class="btn btn-danger flex-fill"
                  @click="showRejectModal(upload)"
                  :disabled="isProcessing"
                >
                  <i class="bi bi-x-lg me-2"></i>
                  拒绝
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-5">
        <i class="bi bi-check-circle text-success" style="font-size: 4rem;"></i>
        <h4 class="text-muted mt-3">暂无待审核上传</h4>
        <p class="text-muted">所有专利上传申请都已处理完毕</p>
      </div>
    </div>

    <!-- Reject Modal -->
    <div
      class="modal fade"
      id="rejectModal"
      tabindex="-1"
      aria-labelledby="rejectModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title" id="rejectModalLabel">
              <i class="bi bi-x-circle me-2"></i>
              拒绝上传申请
            </h5>
            <button
              type="button"
              class="btn-close btn-close-white"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <div v-if="selectedUpload" class="mb-3">
              <h6 class="text-muted">专利信息</h6>
              <p class="mb-1"><strong>{{ selectedUpload.patentName }}</strong></p>
              <small class="text-muted">{{ selectedUpload.patentNumber }}</small>
            </div>

            <div class="mb-3">
              <label for="rejectReason" class="form-label">
                拒绝原因 <span class="text-danger">*</span>
              </label>
              <textarea
                id="rejectReason"
                class="form-control"
                rows="4"
                v-model="rejectReason"
                placeholder="请详细说明拒绝原因..."
                :class="{ 'is-invalid': rejectReasonError }"
              ></textarea>
              <div v-if="rejectReasonError" class="invalid-feedback">
                {{ rejectReasonError }}
              </div>
            </div>

            <!-- Quick Reason Templates -->
            <div class="mb-3">
              <label class="form-label">常用拒绝原因</label>
              <div class="d-flex flex-wrap gap-2">
                <button
                  v-for="template in rejectTemplates"
                  :key="template"
                  type="button"
                  class="btn btn-outline-secondary btn-sm"
                  @click="rejectReason = template"
                >
                  {{ template }}
                </button>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="bi bi-x-lg me-2"></i>
              取消
            </button>
            <button
              type="button"
              class="btn btn-danger"
              @click="confirmReject"
              :disabled="isProcessing"
            >
              <span v-if="isProcessing" class="spinner-border spinner-border-sm me-2"></span>
              <i v-else class="bi bi-check-lg me-2"></i>
              确认拒绝
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- User Detail Modal -->
    <UserDetailModal
      modal-id="userDetailModal"
      :user-address="selectedUserAddress"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { transactionService } from '@/services/transactionService'
import UserDetailModal from '@/components/UserDetailModal.vue'

export default {
  name: 'ReviewUploadView',
  components: {
    UserDetailModal
  },
  setup() {
    const authStore = useAuthStore()

    // State
    const isLoading = ref(false)
    const isProcessing = ref(false)
    const processingId = ref(null)
    const error = ref(null)
    const successMessage = ref(null)
    const pendingUploads = ref([])
    const selectedUpload = ref(null)
    const selectedUserAddress = ref(null)

    // Reject modal state
    const rejectReason = ref('')
    const rejectReasonError = ref(null)

    // Statistics
    const approvedCount = ref(0)
    const rejectedCount = ref(0)
    const totalCount = computed(() =>
      pendingUploads.value.length + approvedCount.value + rejectedCount.value
    )

    // Reject reason templates
    const rejectTemplates = [
      '专利信息不完整或有误',
      '专利文档缺失或无效',
      '专利权归属存在争议',
      '转让价格不合理',
      '代理证书无效或缺失',
      '申请材料不符合要求',
      '专利已过期或无效',
      '违反平台上传规则'
    ]

    // Load pending uploads
    const loadPendingUploads = async () => {
      try {
        isLoading.value = true
        error.value = null

        const uploads = await transactionService.getPendingUploads()
        pendingUploads.value = uploads

        // Load real statistics from backend
        try {
          const stats = await transactionService.getReviewStatistics()
          approvedCount.value = stats.patents?.approved || 0
          rejectedCount.value = stats.patents?.rejected || 0
        } catch (statsError) {
          console.warn('Failed to load statistics:', statsError)
          // Fallback to basic counts if statistics fail
          approvedCount.value = 0
          rejectedCount.value = 0
        }

      } catch (err) {
        error.value = err.message
      } finally {
        isLoading.value = false
      }
    }

    // Approve upload
    const approveUpload = async (upload) => {
      try {
        isProcessing.value = true
        processingId.value = upload.id

        const result = await transactionService.approveUpload(
          upload.id,
          authStore.account
        )

        if (result.success) {
          successMessage.value = `专利上传 ${upload.patentName} 已批准`
          // Remove from pending list
          pendingUploads.value = pendingUploads.value.filter(
            u => u.id !== upload.id
          )
          
          // Reload statistics to get updated counts
          try {
            const stats = await transactionService.getReviewStatistics()
            approvedCount.value = stats.patents?.approved || 0
            rejectedCount.value = stats.patents?.rejected || 0
          } catch (statsError) {
            console.warn('Failed to reload statistics:', statsError)
            // Fallback: just increment the approved count
            approvedCount.value++
          }
        }

      } catch (err) {
        error.value = err.message
      } finally {
        isProcessing.value = false
        processingId.value = null
      }
    }

    // Show reject modal
    const showRejectModal = (upload) => {
      selectedUpload.value = upload
      rejectReason.value = ''
      rejectReasonError.value = null
      const modal = new bootstrap.Modal(document.getElementById('rejectModal'))
      modal.show()
    }

    // Confirm reject
    const confirmReject = async () => {
      try {
        if (!rejectReason.value.trim()) {
          rejectReasonError.value = '请输入拒绝原因'
          return
        }

        rejectReasonError.value = null
        isProcessing.value = true

        const result = await transactionService.rejectUpload(
          selectedUpload.value.id,
          authStore.account,
          rejectReason.value
        )

        if (result.success) {
          successMessage.value = `专利上传 ${selectedUpload.value.patentName} 已拒绝`

          // Remove from pending list
          pendingUploads.value = pendingUploads.value.filter(
            u => u.id !== selectedUpload.value.id
          )
          
          // Reload statistics to get updated counts
          try {
            const stats = await transactionService.getReviewStatistics()
            approvedCount.value = stats.patents?.approved || 0
            rejectedCount.value = stats.patents?.rejected || 0
          } catch (statsError) {
            console.warn('Failed to reload statistics:', statsError)
            // Fallback: just increment the rejected count
            rejectedCount.value++
          }

          // Close modal
          const modal = bootstrap.Modal.getInstance(document.getElementById('rejectModal'))
          modal.hide()
        }

      } catch (err) {
        error.value = err.message
      } finally {
        isProcessing.value = false
      }
    }

    // Show user details modal
    const showUserDetails = (address) => {
      selectedUserAddress.value = address
      const modal = new bootstrap.Modal(document.getElementById('userDetailModal'))
      modal.show()
    }

    // Utility functions
    const formatPrice = (price) => {
      return new Intl.NumberFormat('zh-CN').format(price)
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const formatAddress = (address) => {
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    const maskIdNumber = (idNumber) => {
      if (!idNumber || idNumber.length < 8) return idNumber
      return idNumber.slice(0, 4) + '****' + idNumber.slice(-4)
    }

    // Initialize
    onMounted(() => {
      if (authStore.isConnected) {
        loadPendingUploads()
      }
    })

    return {
      isLoading,
      isProcessing,
      processingId,
      error,
      successMessage,
      pendingUploads,
      selectedUpload,
      selectedUserAddress,
      rejectReason,
      rejectReasonError,
      rejectTemplates,
      approvedCount,
      rejectedCount,
      totalCount,
      approveUpload,
      showRejectModal,
      confirmReject,
      showUserDetails,
      formatPrice,
      formatDate,
      formatAddress,
      maskIdNumber
    }
  }
}
</script>

<style scoped>
.review-upload-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 10px;
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
}

.btn {
  border-radius: 6px;
}

.alert {
  border-radius: 8px;
}

.modal-content {
  border-radius: 12px;
}

.modal-header {
  border-radius: 12px 12px 0 0;
}

.badge {
  font-size: 0.75rem;
}

.text-primary {
  color: #0d6efd !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

.font-monospace {
  font-family: 'Courier New', monospace;
}

.opacity-75 {
  opacity: 0.75;
}

.fs-1 {
  font-size: 2.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.flex-fill {
  flex: 1 1 auto;
}

.flex-wrap {
  flex-wrap: wrap;
}
</style>
