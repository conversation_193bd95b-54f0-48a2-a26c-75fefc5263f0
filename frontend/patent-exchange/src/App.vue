<script>
import AppLayout from './components/AppLayout.vue'
import { useAuthStore } from './stores/auth'
import { onMounted } from 'vue'

export default {
  name: 'App',
  components: {
    AppLayout
  },
  setup() {
    const authStore = useAuthStore()

    onMounted(async () => {
      // Check if wallet is already connected on app load
      await authStore.checkConnection()
    })

    return {
      authStore
    }
  }
}
</script>

<template>
  <AppLayout />
</template>

<style>
/* Global styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Ensure the root app element takes full width */
#app {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure full-width containers don't cause horizontal scroll */
.container-fluid {
  padding-left: 1rem;
  padding-right: 1rem;
  max-width: 100vw;
  width: 100%;
}

/* Responsive adjustments for better spacing on large screens */
@media (min-width: 1200px) {
  .container-fluid {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1600px) {
  .container-fluid {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

.btn {
  border-radius: 0.375rem;
}

.card {
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.form-control:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.table th {
  border-top: none;
  font-weight: 600;
}

.badge {
  font-size: 0.75em;
}

/* Custom utility classes */
.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Loading spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Custom colors */
.bg-patent {
  background-color: #f8f9fa;
}

.text-patent {
  color: #495057;
}

.border-patent {
  border-color: #dee2e6;
}

/* Ensure proper spacing for full-width layout */
.row {
  margin-left: 0;
  margin-right: 0;
}

.row > * {
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  padding-right: calc(var(--bs-gutter-x) * 0.5);
}

/* Fix any potential Bootstrap conflicts */
.container,
.container-sm,
.container-md,
.container-lg,
.container-xl,
.container-xxl {
  max-width: none !important;
  width: 100% !important;
}
</style>
