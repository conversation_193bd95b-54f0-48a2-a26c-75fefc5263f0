import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { roleService } from '@/services/roleService'

export const useAuthStore = defineStore('auth', () => {
  // State
  const account = ref(null)
  const isConnected = ref(false)
  const userRole = ref(null) // 'user', 'reviewer', 'admin'
  const previousRole = ref(null) // Track previous role for transitions
  const web3 = ref(null)
  const isLoading = ref(false)
  const isRoleLoading = ref(false) // Separate loading state for role detection
  const error = ref(null)
  const roleChangeNotification = ref(null) // For role change notifications

  // Getters
  const isUser = computed(() => userRole.value === 'user')
  const isReviewer = computed(() => userRole.value === 'reviewer')
  const isAdmin = computed(() => userRole.value === 'admin')
  const hasRoleChanged = computed(() => previousRole.value && previousRole.value !== userRole.value)
  const isAnyLoading = computed(() => isLoading.value || isRoleLoading.value)
  const shortAddress = computed(() => {
    if (!account.value) return ''
    return `${account.value.slice(0, 6)}...${account.value.slice(-4)}`
  })

  // Actions
  const connectWallet = async () => {
    try {
      isLoading.value = true
      error.value = null

      // Check if MetaMask is installed
      if (typeof window.ethereum === 'undefined') {
        throw new Error('请安装 MetaMask 钱包')
      }

      // Request account access
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      })

      if (accounts.length === 0) {
        throw new Error('未找到钱包账户')
      }

      account.value = accounts[0]
      isConnected.value = true

      // Sync to localStorage for API client
      localStorage.setItem('userAddress', accounts[0])

      // Initialize Web3
      const Web3 = (await import('web3')).default
      web3.value = new Web3(window.ethereum)

      // Determine user role (this would typically come from smart contract)
      await determineUserRole()

      // Listen for account changes
      window.ethereum.on('accountsChanged', handleAccountsChanged)
      window.ethereum.on('chainChanged', handleChainChanged)

      console.log('钱包连接成功:', account.value, '角色:', userRole.value)

    } catch (err) {
      error.value = err.message
      console.error('钱包连接失败:', err)
    } finally {
      isLoading.value = false
    }
  }

  const disconnectWallet = () => {
    account.value = null
    isConnected.value = false
    previousRole.value = userRole.value
    userRole.value = null
    web3.value = null
    error.value = null
    roleChangeNotification.value = null
    isRoleLoading.value = false

    // Clear localStorage
    localStorage.removeItem('userAddress')

    // Remove event listeners
    if (window.ethereum) {
      window.ethereum.removeListener('accountsChanged', handleAccountsChanged)
      window.ethereum.removeListener('chainChanged', handleChainChanged)
    }

    console.log('钱包已断开连接')
  }

  const determineUserRole = async (retryCount = 0) => {
    if (!account.value) return

    try {
      isRoleLoading.value = true
      error.value = null

      // Store previous role for comparison
      previousRole.value = userRole.value

      // Use role service for role detection
      const newRole = await roleService.getUserRole(account.value, web3.value)

      // Check if role has changed
      if (previousRole.value && previousRole.value !== newRole) {
        const roleNames = {
          'admin': '管理员',
          'reviewer': '审核方',
          'user': '用户'
        }

        roleChangeNotification.value = {
          type: 'success',
          message: `角色已更新: ${roleNames[previousRole.value] || '未知'} → ${roleNames[newRole] || '未知'}`,
          timestamp: Date.now()
        }

        console.log('角色变更:', previousRole.value, '→', newRole)
      }

      userRole.value = newRole

    } catch (err) {
      console.error('角色检测失败:', err)

      // Retry logic for role detection failures
      if (retryCount < 3) {
        console.log(`重试角色检测 (${retryCount + 1}/3)...`)
        setTimeout(() => {
          determineUserRole(retryCount + 1)
        }, 1000 * (retryCount + 1)) // Exponential backoff
        return
      }

      // If all retries failed, set default role and show error
      userRole.value = 'user' // Default to user role
      error.value = '角色检测失败，已设置为默认用户角色'

      roleChangeNotification.value = {
        type: 'warning',
        message: '角色检测失败，已设置为默认用户角色',
        timestamp: Date.now()
      }
    } finally {
      isRoleLoading.value = false
    }
  }

  const handleAccountsChanged = async (accounts) => {
    console.log('MetaMask 账户变更:', accounts)

    if (accounts.length === 0) {
      disconnectWallet()
    } else {
      const newAccount = accounts[0]

      // Only update if account actually changed
      if (newAccount !== account.value) {
        account.value = newAccount

        // Sync to localStorage for API client
        localStorage.setItem('userAddress', newAccount)

        // Clear any existing notifications
        roleChangeNotification.value = null

        // Show account change notification
        roleChangeNotification.value = {
          type: 'info',
          message: `账户已切换至: ${newAccount.slice(0, 6)}...${newAccount.slice(-4)}`,
          timestamp: Date.now()
        }

        // Determine role for new account
        await determineUserRole()
      }
    }
  }

  const handleChainChanged = () => {
    console.log('区块链网络已变更')

    roleChangeNotification.value = {
      type: 'warning',
      message: '检测到网络变更，页面将重新加载...',
      timestamp: Date.now()
    }

    // Small delay to show notification before reload
    setTimeout(() => {
      window.location.reload()
    }, 1500)
  }

  // Clear role change notification
  const clearRoleNotification = () => {
    roleChangeNotification.value = null
  }

  const checkConnection = async () => {
    try {
      if (typeof window.ethereum !== 'undefined') {
        const accounts = await window.ethereum.request({
          method: 'eth_accounts'
        })

        if (accounts.length > 0) {
          account.value = accounts[0]
          isConnected.value = true

          // Sync to localStorage for API client
          localStorage.setItem('userAddress', accounts[0])

          const Web3 = (await import('web3')).default
          web3.value = new Web3(window.ethereum)

          await determineUserRole()

          // Set up event listeners
          window.ethereum.on('accountsChanged', handleAccountsChanged)
          window.ethereum.on('chainChanged', handleChainChanged)
        }
      }
    } catch (err) {
      console.error('检查连接状态失败:', err)
    }
  }

  return {
    // State
    account,
    isConnected,
    userRole,
    previousRole,
    web3,
    isLoading,
    isRoleLoading,
    error,
    roleChangeNotification,

    // Getters
    isUser,
    isReviewer,
    isAdmin,
    hasRoleChanged,
    isAnyLoading,
    shortAddress,

    // Actions
    connectWallet,
    disconnectWallet,
    checkConnection,
    clearRoleNotification,
    determineUserRole
  }
})
