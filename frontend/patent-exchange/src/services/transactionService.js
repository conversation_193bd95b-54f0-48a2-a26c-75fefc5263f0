import api from './apiClient.js'

// Transaction service for managing patent transactions and rights protection
export const transactionService = {
  // Get pending transactions for review (reviewer view)
  async getPendingTransactions() {
    try {
      const response = await api.transactions.getPending()
      console.log('🔍 getPendingTransactions response:', response.data)
      return response.data.data.data || response.data.data || []
    } catch (error) {
      console.error('获取待审核交易失败:', error)
      throw new Error(error.message || '获取待审核交易失败')
    }
  },

  // Get user's transactions
  async getUserTransactions(userAddress, params = {}) {
    try {
      const response = await api.transactions.getUserTransactions(userAddress, params)
      return response.data.data.transactions || []
    } catch (error) {
      console.error('获取用户交易记录失败:', error)
      throw new Error(error.message || '获取用户交易记录失败')
    }
  },

  // Initiate patent transaction
  async initiateTransaction(transactionData) {
    try {
      const response = await api.transactions.initiate(transactionData)
      return response.data
    } catch (error) {
      console.error('发起交易失败:', error)
      throw new Error(error.message || '发起交易失败')
    }
  },

  // Approve transaction (reviewer action)
  async approveTransaction(transactionId, reviewerAddress, comments = '') {
    try {
      const response = await api.transactions.approve(transactionId, {
        reviewerAddress,
        comments
      })
      return response.data
    } catch (error) {
      console.error('批准交易失败:', error)
      throw new Error(error.message || '批准交易失败')
    }
  },

  // Reject transaction (reviewer action)
  async rejectTransaction(transactionId, reviewerAddress, reason, comments = '') {
    try {
      const response = await api.transactions.reject(transactionId, {
        reviewerAddress,
        reason,
        comments
      })
      return response.data
    } catch (error) {
      console.error('拒绝交易失败:', error)
      throw new Error(error.message || '拒绝交易失败')
    }
  },

  // Complete transaction (reviewer action)
  async completeTransaction(transactionId) {
    try {
      const response = await api.transactions.complete(transactionId)
      return response.data
    } catch (error) {
      console.error('完成交易失败:', error)
      throw new Error(error.message || '完成交易失败')
    }
  },

  // Get pending patent uploads for review (reviewer view)
  async getPendingUploads() {
    try {
      const response = await api.review.getPendingUploads()
      console.log('🔍 getPendingUploads response:', response.data)
      return response.data.data?.data || response.data.data || []
    } catch (error) {
      console.error('获取待审核上传失败:', error)
      throw new Error(error.message || '获取待审核上传失败')
    }
  },

  // Approve patent upload (reviewer action)
  async approveUpload(uploadId, reviewerAddress, comments = '') {
    try {
      const response = await api.review.approveUpload(uploadId, {
        reviewerAddress,
        comments
      })
      return response.data
    } catch (error) {
      console.error('批准上传失败:', error)
      throw new Error(error.message || '批准上传失败')
    }
  },

  // Reject patent upload (reviewer action)
  async rejectUpload(uploadId, reviewerAddress, reason, comments = '') {
    try {
      const response = await api.review.rejectUpload(uploadId, {
        reviewerAddress,
        reason,
        comments
      })
      return response.data
    } catch (error) {
      console.error('拒绝上传失败:', error)
      throw new Error(error.message || '拒绝上传失败')
    }
  },

  // Get pending rights protection requests for review (reviewer view)
  async getPendingProtectionRequests() {
    try {
      const response = await api.protection.getPending()
      console.log('🔍 getPendingProtectionRequests response:', response.data)
      return response.data.data?.data || response.data.data || []
    } catch (error) {
      console.error('获取待审核维权申请失败:', error)
      throw new Error(error.message || '获取待审核维权申请失败')
    }
  },

  // Approve rights protection request (reviewer action)
  async approveProtectionRequest(requestId, reviewerAddress, resolution = '') {
    try {
      console.log('🔍 Approving protection request:', { requestId, reviewerAddress, resolution })
      const response = await api.protection.approve(requestId, {
        reviewerAddress,
        resolution
      })
      console.log('✅ Protection request approved:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ 批准维权申请失败:', error)

      // Extract meaningful error message from backend response format
      let errorMessage = '批准维权申请失败'
      if (error.response?.data?.error?.message) {
        errorMessage = error.response.data.error.message
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error.response?.data?.error) {
        errorMessage = typeof error.response.data.error === 'string' ? error.response.data.error : error.response.data.error.message || '批准维权申请失败'
      } else if (error.message) {
        errorMessage = error.message
      }

      throw new Error(errorMessage)
    }
  },

  // Reject rights protection request (reviewer action)
  async rejectProtectionRequest(requestId, reviewerAddress, reason) {
    try {
      console.log('🔍 Rejecting protection request:', { requestId, reviewerAddress, reason })
      const response = await api.protection.reject(requestId, {
        reviewerAddress,
        reason
      })
      console.log('✅ Protection request rejected:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ 拒绝维权申请失败:', error)

      // Extract meaningful error message from backend response format
      let errorMessage = '拒绝维权申请失败'
      if (error.response?.data?.error?.message) {
        errorMessage = error.response.data.error.message
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error.response?.data?.error) {
        errorMessage = typeof error.response.data.error === 'string' ? error.response.data.error : error.response.data.error.message || '拒绝维权申请失败'
      } else if (error.message) {
        errorMessage = error.message
      }

      throw new Error(errorMessage)
    }
  },

  // Get pending rights protection cases
  async getPendingProtectionCases() {
    try {
      const response = await api.protection.getCases()
      console.log('🔍 getPendingProtectionCases response:', response.data)
      return response.data.data?.data || response.data.data || []
    } catch (error) {
      console.error('获取待审核维权案例失败:', error)
      throw new Error(error.message || '获取待审核维权案例失败')
    }
  },

  // Initiate rights protection
  async initiateRightsProtection(protectionData) {
    try {
      const response = await api.protection.request(protectionData)
      return response.data
    } catch (error) {
      console.error('发起维权失败:', error)
      throw new Error(error.message || '发起维权失败')
    }
  },

  // Approve rights protection (reviewer action)
  async approveRightsProtection(caseId, reviewerAddress, resolution = '') {
    try {
      const response = await api.protection.approve(caseId, {
        reviewerAddress,
        resolution
      })
      return response.data
    } catch (error) {
      console.error('批准维权失败:', error)
      throw new Error(error.message || '批准维权失败')
    }
  },

  // Reject rights protection (reviewer action)
  async rejectRightsProtection(caseId, reviewerAddress, reason) {
    try {
      const response = await api.protection.reject(caseId, {
        reviewerAddress,
        reason
      })
      return response.data
    } catch (error) {
      console.error('拒绝维权失败:', error)
      throw new Error(error.message || '拒绝维权失败')
    }
  },

  // Get review statistics
  async getReviewStatistics() {
    try {
      const response = await api.review.getStatistics()
      return response.data
    } catch (error) {
      console.error('获取审核统计失败:', error)
      throw new Error(error.message || '获取审核统计失败')
    }
  },

  // Get user's protection cases
  async getUserProtectionCases() {
    try {
      const response = await api.protection.getMyCases()
      console.log('🔍 getUserProtectionCases response:', response.data)
      return response.data.data?.data || response.data.data || []
    } catch (error) {
      console.error('获取用户维权案例失败:', error)
      throw new Error(error.message || '获取用户维权案例失败')
    }
  }
}
