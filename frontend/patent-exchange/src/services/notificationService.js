// Notification service for managing real-time notifications
import { ref, reactive } from 'vue'
import api from './apiClient.js'

class NotificationService {
  constructor() {
    // In-app notifications store
    this.notifications = ref([])
    this.unreadCount = ref(0)

    // Notification types
    this.TYPES = {
      PATENT_APPROVED: 'patent_approved',
      PATENT_REJECTED: 'patent_rejected',
      PATENT_SOLD: 'patent_sold',
      PATENT_PURCHASED: 'patent_purchased',
      RIGHTS_PROTECTION_INITIATED: 'rights_protection_initiated',
      RIGHTS_PROTECTION_RESOLVED: 'rights_protection_resolved',
      TRANSACTION_COMPLETED: 'transaction_completed',
      REVIEW_ASSIGNED: 'review_assigned'
    }

    // Request browser notification permission on initialization
    this.requestPermission()
  }

  // Request browser notification permission
  async requestPermission() {
    if ('Notification' in window && Notification.permission === 'default') {
      try {
        await Notification.requestPermission()
      } catch (error) {
        console.warn('Failed to request notification permission:', error)
      }
    }
  }

  // Add notification to in-app store
  addNotification(notification) {
    const newNotification = {
      id: Date.now() + Math.random(),
      timestamp: new Date(),
      read: false,
      ...notification
    }

    this.notifications.value.unshift(newNotification)
    this.unreadCount.value++

    // Show browser notification if permission granted
    this.showBrowserNotification(newNotification)

    return newNotification.id
  }

  // Show browser notification
  showBrowserNotification(notification) {
    if ('Notification' in window && Notification.permission === 'granted') {
      try {
        const browserNotification = new Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico',
          tag: notification.type,
          requireInteraction: false
        })

        // Auto close after 5 seconds
        setTimeout(() => {
          browserNotification.close()
        }, 5000)

        // Handle click events
        browserNotification.onclick = () => {
          window.focus()
          if (notification.action) {
            notification.action()
          }
          browserNotification.close()
        }
      } catch (error) {
        console.warn('Failed to show browser notification:', error)
      }
    }
  }

  // Mark notification as read
  markAsRead(notificationId) {
    const notification = this.notifications.value.find(n => n.id === notificationId)
    if (notification && !notification.read) {
      notification.read = true
      this.unreadCount.value = Math.max(0, this.unreadCount.value - 1)
    }
  }

  // Mark all notifications as read
  markAllAsRead() {
    this.notifications.value.forEach(notification => {
      notification.read = true
    })
    this.unreadCount.value = 0
  }

  // Remove notification
  removeNotification(notificationId) {
    const index = this.notifications.value.findIndex(n => n.id === notificationId)
    if (index !== -1) {
      const notification = this.notifications.value[index]
      if (!notification.read) {
        this.unreadCount.value = Math.max(0, this.unreadCount.value - 1)
      }
      this.notifications.value.splice(index, 1)
    }
  }

  // Clear all notifications
  clearAll() {
    this.notifications.value = []
    this.unreadCount.value = 0
  }

  // Get notifications (with optional filtering)
  getNotifications(filter = {}) {
    let filtered = this.notifications.value

    if (filter.type) {
      filtered = filtered.filter(n => n.type === filter.type)
    }

    if (filter.unreadOnly) {
      filtered = filtered.filter(n => !n.read)
    }

    return filtered
  }

  // Load notifications from backend
  async loadNotifications(userAddress, params = {}) {
    try {
      const response = await api.notifications.getByAddress(userAddress, params)
      const serverNotifications = response.data.data.notifications || []

      // Convert server notifications to local format
      const localNotifications = serverNotifications.map(notification => ({
        id: notification.id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        severity: notification.severity || 'info',
        read: notification.read,
        timestamp: new Date(notification.timestamp),
        relatedId: notification.relatedId
      }))

      // Update local store
      this.notifications.value = localNotifications
      this.unreadCount.value = response.data.data.unreadCount || 0

      return localNotifications
    } catch (error) {
      console.error('Failed to load notifications:', error)
      throw new Error(error.message || '加载通知失败')
    }
  }

  // Mark notification as read on server
  async markAsReadOnServer(notificationId) {
    try {
      await api.notifications.markAsRead(notificationId)
      this.markAsRead(notificationId)
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
      throw new Error(error.message || '标记通知已读失败')
    }
  }

  // Mark all notifications as read on server
  async markAllAsReadOnServer(userAddress) {
    try {
      await api.notifications.markAllAsRead(userAddress)
      this.markAllAsRead()
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
      throw new Error(error.message || '标记所有通知已读失败')
    }
  }

  // Send notification via backend
  async sendNotification(notificationData) {
    try {
      const response = await api.notifications.send(notificationData)
      return response.data
    } catch (error) {
      console.error('Failed to send notification:', error)
      throw new Error(error.message || '发送通知失败')
    }
  }

  // Predefined notification creators for common events
  notifyPatentApproved(patentName, patentId) {
    return this.addNotification({
      type: this.TYPES.PATENT_APPROVED,
      title: '专利审核通过',
      message: `您的专利"${patentName}"已通过审核`,
      severity: 'success',
      action: () => {
        // Navigate to patent details
        window.location.href = `/patent/${patentId}`
      }
    })
  }

  notifyPatentRejected(patentName, reason) {
    return this.addNotification({
      type: this.TYPES.PATENT_REJECTED,
      title: '专利审核未通过',
      message: `您的专利"${patentName}"审核未通过。原因：${reason}`,
      severity: 'error'
    })
  }

  notifyPatentSold(patentName, buyerName, price) {
    return this.addNotification({
      type: this.TYPES.PATENT_SOLD,
      title: '专利交易完成',
      message: `您的专利"${patentName}"已成功出售给${buyerName}，交易金额：${price} ETH`,
      severity: 'success'
    })
  }

  notifyPatentPurchased(patentName, sellerName, price) {
    return this.addNotification({
      type: this.TYPES.PATENT_PURCHASED,
      title: '专利购买成功',
      message: `您已成功购买专利"${patentName}"，卖方：${sellerName}，交易金额：${price} ETH`,
      severity: 'success'
    })
  }

  notifyRightsProtectionInitiated(patentName, caseId) {
    return this.addNotification({
      type: this.TYPES.RIGHTS_PROTECTION_INITIATED,
      title: '专利维权申请已提交',
      message: `您对专利"${patentName}"的维权申请已提交，案例编号：${caseId}`,
      severity: 'info'
    })
  }

  notifyRightsProtectionResolved(patentName, result, reason) {
    return this.addNotification({
      type: this.TYPES.RIGHTS_PROTECTION_RESOLVED,
      title: '专利维权结果',
      message: `您的专利"${patentName}"维权申请已${result}。${reason ? '原因：' + reason : ''}`,
      severity: result === '通过' ? 'success' : 'warning'
    })
  }

  notifyTransactionCompleted(patentName, transactionId) {
    return this.addNotification({
      type: this.TYPES.TRANSACTION_COMPLETED,
      title: '交易完成',
      message: `专利"${patentName}"的交易已完成，交易ID：${transactionId}`,
      severity: 'success'
    })
  }

  notifyReviewAssigned(reviewType, itemName) {
    return this.addNotification({
      type: this.TYPES.REVIEW_ASSIGNED,
      title: '新的审核任务',
      message: `您有新的${reviewType}审核任务：${itemName}`,
      severity: 'info',
      action: () => {
        // Navigate to review page
        window.location.href = '/review/pending'
      }
    })
  }

  // Simulate receiving notifications (for demo purposes)
  simulateNotifications() {
    setTimeout(() => {
      this.notifyPatentApproved('智能手机快速充电技术', '1')
    }, 2000)

    setTimeout(() => {
      this.notifyPatentSold('环保汽车发动机设计', '李四', '2.3')
    }, 5000)

    setTimeout(() => {
      this.notifyReviewAssigned('专利上传', '新型智能家居控制系统')
    }, 8000)
  }
}

// Create and export singleton instance
export const notificationService = new NotificationService()

// Export for use in Vue components
export default notificationService
