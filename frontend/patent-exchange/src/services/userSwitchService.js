import { apiClient } from './apiClient';

/**
 * User Switch Service
 * Handles user switching functionality for MetaMask integration
 */

class UserSwitchService {
  constructor() {
    this.currentUser = null;
    this.switchListeners = [];
  }

  /**
   * Switch to a new user address
   * @param {string} address - The user's Ethereum address
   * @returns {Promise<Object>} User information and permissions
   */
  async switchUser(address) {
    try {
      console.log(`🔄 Switching to user: ${address}`);
      
      const response = await apiClient.post(`/user/switch/${address}`);
      
      if (response.data.success) {
        const userData = response.data.data;
        this.currentUser = userData;
        
        // Store current user in localStorage
        localStorage.setItem('currentUser', JSON.stringify(userData));
        localStorage.setItem('userAddress', address);
        
        // Notify listeners about user switch
        this.notifyListeners(userData);
        
        console.log(`✅ Successfully switched to user: ${userData.profile?.name || address} (${userData.role})`);
        return userData;
      } else {
        throw new Error(response.data.error?.message || 'Failed to switch user');
      }
    } catch (error) {
      console.error('❌ Failed to switch user:', error);
      throw error;
    }
  }

  /**
   * Get current user information
   * @returns {Object|null} Current user data or null if no user
   */
  getCurrentUser() {
    if (!this.currentUser) {
      // Try to load from localStorage
      const storedUser = localStorage.getItem('currentUser');
      if (storedUser) {
        try {
          this.currentUser = JSON.parse(storedUser);
        } catch (error) {
          console.warn('Failed to parse stored user data:', error);
          localStorage.removeItem('currentUser');
        }
      }
    }
    return this.currentUser;
  }

  /**
   * Check if user has specific permission
   * @param {string} address - User address
   * @param {string} permission - Permission to check
   * @returns {Promise<boolean>} Whether user has permission
   */
  async checkPermission(address, permission) {
    try {
      const response = await apiClient.get(`/user/permission/${address}/${permission}`);
      
      if (response.data.success) {
        return response.data.data.hasPermission;
      }
      return false;
    } catch (error) {
      console.error(`❌ Failed to check permission ${permission} for ${address}:`, error);
      return false;
    }
  }

  /**
   * Check if current user has specific permission
   * @param {string} permission - Permission to check
   * @returns {boolean} Whether current user has permission
   */
  hasPermission(permission) {
    const user = this.getCurrentUser();
    if (!user || !user.permissions) {
      return false;
    }
    return user.permissions.includes(permission);
  }

  /**
   * Get user role
   * @returns {string} User role (admin, reviewer, user, unregistered)
   */
  getUserRole() {
    const user = this.getCurrentUser();
    return user?.role || 'unregistered';
  }

  /**
   * Check if current user is admin
   * @returns {boolean} Whether user is admin
   */
  isAdmin() {
    return this.getUserRole() === 'admin';
  }

  /**
   * Check if current user is reviewer
   * @returns {boolean} Whether user is reviewer
   */
  isReviewer() {
    return this.getUserRole() === 'reviewer';
  }

  /**
   * Check if current user is registered
   * @returns {boolean} Whether user is registered
   */
  isRegistered() {
    const user = this.getCurrentUser();
    return user?.isRegistered || false;
  }

  /**
   * Get current user address
   * @returns {string|null} User address or null
   */
  getCurrentAddress() {
    const user = this.getCurrentUser();
    return user?.address || localStorage.getItem('userAddress') || null;
  }

  /**
   * Clear current user data
   */
  clearUser() {
    this.currentUser = null;
    localStorage.removeItem('currentUser');
    localStorage.removeItem('userAddress');
    this.notifyListeners(null);
  }

  /**
   * Add listener for user switch events
   * @param {Function} listener - Callback function to call when user switches
   */
  addSwitchListener(listener) {
    this.switchListeners.push(listener);
  }

  /**
   * Remove listener for user switch events
   * @param {Function} listener - Callback function to remove
   */
  removeSwitchListener(listener) {
    const index = this.switchListeners.indexOf(listener);
    if (index > -1) {
      this.switchListeners.splice(index, 1);
    }
  }

  /**
   * Notify all listeners about user switch
   * @param {Object|null} userData - New user data or null if logged out
   */
  notifyListeners(userData) {
    this.switchListeners.forEach(listener => {
      try {
        listener(userData);
      } catch (error) {
        console.error('Error in switch listener:', error);
      }
    });
  }

  /**
   * Handle MetaMask account change
   * @param {string[]} accounts - Array of accounts from MetaMask
   */
  async handleAccountsChanged(accounts) {
    if (accounts.length === 0) {
      // MetaMask is locked or no accounts
      console.log('🔒 MetaMask locked or no accounts available');
      this.clearUser();
      return null;
    }

    const newAddress = accounts[0];
    const currentAddress = this.getCurrentAddress();

    if (newAddress !== currentAddress) {
      // Account changed, switch user
      console.log(`🔄 MetaMask account changed from ${currentAddress} to ${newAddress}`);
      try {
        return await this.switchUser(newAddress);
      } catch (error) {
        console.error('Failed to switch user after account change:', error);
        this.clearUser();
        throw error;
      }
    }

    return this.getCurrentUser();
  }

  /**
   * Initialize MetaMask integration
   */
  initializeMetaMask() {
    if (typeof window !== 'undefined' && window.ethereum) {
      // Listen for account changes
      window.ethereum.on('accountsChanged', (accounts) => {
        this.handleAccountsChanged(accounts);
      });

      // Listen for network changes
      window.ethereum.on('chainChanged', (chainId) => {
        console.log('🌐 Network changed:', chainId);
        // Optionally refresh user data or show network warning
      });

      console.log('🦊 MetaMask integration initialized');
    } else {
      console.warn('⚠️ MetaMask not detected');
    }
  }

  /**
   * Connect to MetaMask and switch to the connected account
   * @returns {Promise<Object>} User data
   */
  async connectMetaMask() {
    if (typeof window === 'undefined' || !window.ethereum) {
      throw new Error('MetaMask not detected');
    }

    try {
      // Request account access
      const accounts = await window.ethereum.request({ 
        method: 'eth_requestAccounts' 
      });

      if (accounts.length === 0) {
        throw new Error('No accounts available');
      }

      // Switch to the first account
      return await this.switchUser(accounts[0]);
    } catch (error) {
      console.error('❌ Failed to connect MetaMask:', error);
      throw error;
    }
  }
}

// Create singleton instance
const userSwitchService = new UserSwitchService();

// Initialize MetaMask integration when module loads
if (typeof window !== 'undefined') {
  userSwitchService.initializeMetaMask();
}

export default userSwitchService; 