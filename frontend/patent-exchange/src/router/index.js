import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
    },

    // Patent Routes
    {
      path: '/patents',
      children: [
        {
          path: 'upload',
          name: 'patent-upload',
          component: () => import('../views/patents/PatentUploadView.vue'),
          meta: { requiresAuth: true, roles: ['user', 'reviewer', 'admin'] }
        },
        {
          path: 'search',
          name: 'patent-search',
          component: () => import('../views/patents/PatentSearchView.vue'),
          meta: { requiresAuth: true, roles: ['user', 'reviewer', 'admin'] }
        },
        {
          path: 'my-patents',
          name: 'my-patents',
          component: () => import('../views/patents/MyPatentsView.vue'),
          meta: { requiresAuth: true, roles: ['user', 'admin'] }
        },
        {
          path: 'trading',
          name: 'patent-trading',
          component: () => import('../views/patents/PatentTradingView.vue'),
          meta: { requiresAuth: true, roles: ['user', 'admin'] }
        },
        {
          path: 'protection',
          name: 'patent-protection',
          component: () => import('../views/patents/PatentProtectionView.vue'),
          meta: { requiresAuth: true, roles: ['user', 'reviewer', 'admin'] }
        }
      ]
    },

    // Review Routes (accessible by reviewers and admins)
    {
      path: '/review',
      children: [
        {
          path: 'pending',
          name: 'review-pending',
          component: () => import('../views/review/ReviewPendingView.vue'),
          meta: { requiresAuth: true, roles: ['reviewer', 'admin'] }
        },
        {
          path: 'upload',
          name: 'review-upload',
          component: () => import('../views/review/ReviewUploadView.vue'),
          meta: { requiresAuth: true, roles: ['reviewer', 'admin'] }
        },
        {
          path: 'trading',
          name: 'review-trading',
          component: () => import('../views/review/ReviewTradingView.vue'),
          meta: { requiresAuth: true, roles: ['reviewer', 'admin'] }
        },
        {
          path: 'protection',
          name: 'review-protection',
          component: () => import('../views/review/ReviewProtectionView.vue'),
          meta: { requiresAuth: true, roles: ['reviewer', 'admin'] }
        }
      ]
    },

    // Admin Routes (admins have access to all reviewer functions plus admin-specific ones)
    {
      path: '/admin',
      children: [
        {
          path: 'users',
          name: 'admin-users',
          component: () => import('../views/admin/AdminUsersView.vue'),
          meta: { requiresAuth: true, roles: ['admin'] }
        },
        {
          path: 'transactions',
          name: 'admin-transactions',
          component: () => import('../views/admin/AdminTransactionsView.vue'),
          meta: { requiresAuth: true, roles: ['admin'] }
        },
        {
          path: 'statistics',
          name: 'admin-statistics',
          component: () => import('../views/admin/AdminStatisticsView.vue'),
          meta: { requiresAuth: true, roles: ['admin'] }
        }
      ]
    },

    // Profile Routes
    {
      path: '/profile',
      children: [
        {
          path: 'personal-info',
          name: 'personal-info',
          component: () => import('../views/profile/PersonalInfoView.vue'),
          meta: { requiresAuth: true, roles: ['user', 'reviewer', 'admin'] }
        }
      ]
    },

    // Patent Detail Route
    {
      path: '/patent/:id',
      name: 'patent-detail',
      component: () => import('../views/patents/PatentDetailView.vue'),
      meta: { requiresAuth: true, roles: ['user', 'reviewer', 'admin'] }
    },

    // Notifications Route
    {
      path: '/notifications',
      name: 'notifications',
      component: () => import('../views/NotificationsView.vue'),
      meta: { requiresAuth: true, roles: ['user', 'reviewer', 'admin'] }
    }
  ],
})

// Helper function to get default route for role
const getDefaultRouteForRole = (role) => {
  switch (role) {
    case 'admin':
      return '/admin/users'
    case 'reviewer':
      return '/review/pending'
    case 'user':
      return '/patents/search'
    default:
      return '/'
  }
}

// Navigation guard
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Wait for role loading to complete if in progress
  if (authStore.isRoleLoading) {
    // Wait a bit for role detection to complete
    let attempts = 0
    while (authStore.isRoleLoading && attempts < 50) { // Max 5 seconds
      await new Promise(resolve => setTimeout(resolve, 100))
      attempts++
    }
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!authStore.isConnected) {
      console.log('Navigation blocked: User not connected')
      next('/')
      return
    }

    // Check role-based access
    if (to.meta.roles && !to.meta.roles.includes(authStore.userRole)) {
      console.log('Navigation blocked: Insufficient role permissions', {
        userRole: authStore.userRole,
        requiredRoles: to.meta.roles,
        targetRoute: to.path
      })

      // Redirect to appropriate default route for user's role
      const defaultRoute = getDefaultRouteForRole(authStore.userRole)
      if (to.path !== defaultRoute) {
        next(defaultRoute)
        return
      } else {
        // If already on default route, go to home
        next('/')
        return
      }
    }
  }

  // Handle role changes - redirect to appropriate default route
  if (authStore.hasRoleChanged && authStore.isConnected && to.path !== '/') {
    const defaultRoute = getDefaultRouteForRole(authStore.userRole)

    // Only redirect if not already on an appropriate route
    if (to.meta.requiresAuth && to.meta.roles && !to.meta.roles.includes(authStore.userRole)) {
      console.log('Role changed, redirecting to default route:', defaultRoute)
      next(defaultRoute)
      return
    }
  }

  next()
})

export default router
