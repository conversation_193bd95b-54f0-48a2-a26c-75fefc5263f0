<template>
  <div class="modal fade" id="userRegistrationModal" tabindex="-1" aria-labelledby="userRegistrationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="userRegistrationModalLabel">
            <i class="bi bi-person-plus me-2"></i>
            用户注册
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <!-- Registration Info -->
          <div class="alert alert-info mb-4">
            <i class="bi bi-info-circle me-2"></i>
            <strong>欢迎使用专利交易系统！</strong><br>
            为了确保交易安全，您需要先完成用户注册。注册信息将存储在区块链上，确保数据的安全性和不可篡改性。
          </div>

          <!-- Error <PERSON> -->
          <div v-if="error" class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {{ error }}
          </div>

          <!-- Success Alert -->
          <div v-if="successMessage" class="alert alert-success" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            {{ successMessage }}
          </div>

          <!-- Registration Form -->
          <form @submit.prevent="handleRegistration" v-if="!isRegistered">
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="userName" class="form-label">
                    <i class="bi bi-person me-1"></i>
                    姓名 <span class="text-danger">*</span>
                  </label>
                  <input
                    type="text"
                    class="form-control"
                    id="userName"
                    v-model="registrationForm.name"
                    :class="{ 'is-invalid': validationErrors.name }"
                    placeholder="请输入您的真实姓名"
                    required
                  >
                  <div v-if="validationErrors.name" class="invalid-feedback">
                    {{ validationErrors.name }}
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="userPhone" class="form-label">
                    <i class="bi bi-telephone me-1"></i>
                    手机号码 <span class="text-danger">*</span>
                  </label>
                  <input
                    type="tel"
                    class="form-control"
                    id="userPhone"
                    v-model="registrationForm.phone"
                    :class="{ 'is-invalid': validationErrors.phone }"
                    placeholder="请输入手机号码"
                    required
                  >
                  <div v-if="validationErrors.phone" class="invalid-feedback">
                    {{ validationErrors.phone }}
                  </div>
                </div>
              </div>
            </div>
            <div class="mb-3">
              <label for="userIdNumber" class="form-label">
                <i class="bi bi-card-text me-1"></i>
                身份证号码 <span class="text-danger">*</span>
              </label>
              <input
                type="text"
                class="form-control"
                id="userIdNumber"
                v-model="registrationForm.idNumber"
                :class="{ 'is-invalid': validationErrors.idNumber }"
                placeholder="请输入身份证号码"
                required
              >
              <div v-if="validationErrors.idNumber" class="invalid-feedback">
                {{ validationErrors.idNumber }}
              </div>
            </div>

            <!-- Blockchain Info -->
            <div class="alert alert-light border">
              <h6><i class="bi bi-shield-check me-2"></i>区块链信息</h6>
              <div class="row">
                <div class="col-md-6">
                  <small class="text-muted">钱包地址:</small><br>
                  <code class="small">{{ userAddress }}</code>
                </div>
                <div class="col-md-6">
                  <small class="text-muted">默认角色:</small><br>
                  <span class="badge bg-primary">普通用户</span>
                </div>
              </div>
            </div>

            <!-- Terms and Conditions -->
            <div class="form-check mb-3">
              <input
                class="form-check-input"
                type="checkbox"
                id="agreeTerms"
                v-model="agreeTerms"
                required
              >
              <label class="form-check-label" for="agreeTerms">
                我已阅读并同意 <a href="#" class="text-decoration-none">用户协议</a> 和 <a href="#" class="text-decoration-none">隐私政策</a>
              </label>
            </div>
          </form>

          <!-- Registration Success -->
          <div v-if="isRegistered" class="text-center py-4">
            <i class="bi bi-check-circle-fill text-success" style="font-size: 3rem;"></i>
            <h4 class="mt-3 text-success">注册成功！</h4>
            <p class="text-muted">您的账户已成功注册到区块链，现在可以开始使用专利交易系统了。</p>
          </div>
        </div>
        <div class="modal-footer">
          <button
            v-if="!isRegistered"
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            取消
          </button>
          <button
            v-if="!isRegistered"
            type="submit"
            class="btn btn-primary"
            @click="handleRegistration"
            :disabled="isRegistering || !agreeTerms"
          >
            <span v-if="isRegistering" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            <i v-else class="bi bi-person-plus me-2"></i>
            {{ isRegistering ? '注册中...' : '立即注册' }}
          </button>
          <button
            v-if="isRegistered"
            type="button"
            class="btn btn-success"
            data-bs-dismiss="modal"
            @click="$emit('registration-complete')"
          >
            <i class="bi bi-check me-2"></i>
            开始使用
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { userService } from '@/services/userService'

export default {
  name: 'UserRegistrationModal',
  emits: ['registration-complete'],
  setup(props, { emit }) {
    const authStore = useAuthStore()

    // State
    const isRegistering = ref(false)
    const isRegistered = ref(false)
    const error = ref(null)
    const successMessage = ref(null)
    const agreeTerms = ref(false)

    // Form data
    const registrationForm = reactive({
      name: '',
      phone: '',
      idNumber: ''
    })

    // Validation errors
    const validationErrors = ref({})

    // Computed
    const userAddress = computed(() => authStore.account || '')

    // Methods
    const validateForm = () => {
      const validation = userService.validateProfile(registrationForm)
      validationErrors.value = {}

      if (!validation.isValid) {
        validation.errors.forEach(error => {
          if (error.includes('姓名')) {
            validationErrors.value.name = error
          } else if (error.includes('手机')) {
            validationErrors.value.phone = error
          } else if (error.includes('身份证')) {
            validationErrors.value.idNumber = error
          }
        })
      }

      return validation.isValid
    }

    const handleRegistration = async () => {
      try {
        // Clear previous errors
        error.value = null
        successMessage.value = null

        // Validate form
        if (!validateForm()) {
          error.value = '请检查输入信息是否正确'
          return
        }

        // Check if terms are agreed
        if (!agreeTerms.value) {
          error.value = '请先同意用户协议和隐私政策'
          return
        }

        // Check if user is connected
        if (!authStore.isConnected || !authStore.account) {
          error.value = '请先连接钱包'
          return
        }

        isRegistering.value = true

        console.log('🚀 Registering user:', {
          address: authStore.account,
          name: registrationForm.name,
          phone: registrationForm.phone,
          idNumber: registrationForm.idNumber
        })

        // Register user
        const result = await userService.registerUser({
          name: registrationForm.name,
          phone: registrationForm.phone,
          idNumber: registrationForm.idNumber
        })

        console.log('✅ User registered successfully:', result)

        if (result.success) {
          isRegistered.value = true
          successMessage.value = result.message || '用户注册成功'
          
          // Update auth store to reflect the new registration
          await authStore.determineUserRole()
          
          // Emit success event
          setTimeout(() => {
            emit('registration-complete')
          }, 2000)
        } else {
          error.value = result.message || '注册失败，请重试'
        }

      } catch (err) {
        console.error('❌ Registration failed:', err)
        error.value = err.message || '注册失败，请检查网络连接并重试'
      } finally {
        isRegistering.value = false
      }
    }

    const resetForm = () => {
      registrationForm.name = ''
      registrationForm.phone = ''
      registrationForm.idNumber = ''
      agreeTerms.value = false
      validationErrors.value = {}
      error.value = null
      successMessage.value = null
      isRegistered.value = false
      isRegistering.value = false
    }

    // Watch for modal close to reset form
    watch(() => authStore.account, () => {
      resetForm()
    })

    return {
      authStore,
      isRegistering,
      isRegistered,
      error,
      successMessage,
      agreeTerms,
      registrationForm,
      validationErrors,
      userAddress,
      handleRegistration,
      resetForm
    }
  }
}
</script>

<style scoped>
.modal-content {
  border-radius: 10px;
}

.modal-header {
  border-bottom: 1px solid #dee2e6;
  background-color: #f8f9fa;
}

.form-control:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.alert {
  border-radius: 8px;
}

.badge {
  font-size: 0.75em;
}

code {
  font-size: 0.875em;
  word-break: break-all;
}

.btn {
  border-radius: 6px;
}

.form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}
</style> 