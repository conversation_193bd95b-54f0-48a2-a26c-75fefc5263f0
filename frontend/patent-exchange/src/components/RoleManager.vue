<template>
  <div class="role-manager">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="bi bi-person-gear me-2"></i>
          角色管理 (开发测试)
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <!-- Current Status -->
          <div class="col-md-6">
            <h6>当前状态</h6>
            <div class="mb-3">
              <div class="d-flex align-items-center mb-2">
                <strong>钱包地址:</strong>
                <span class="ms-2 font-monospace">{{ authStore.account || '未连接' }}</span>
              </div>
              <div class="d-flex align-items-center mb-2">
                <strong>当前角色:</strong>
                <span class="ms-2">
                  <span v-if="authStore.isRoleLoading" class="spinner-border spinner-border-sm me-2"></span>
                  <span class="badge" :class="getRoleBadgeClass(authStore.userRole)">
                    {{ getRoleText(authStore.userRole) }}
                  </span>
                </span>
              </div>
              <div v-if="authStore.previousRole" class="d-flex align-items-center mb-2">
                <strong>上一角色:</strong>
                <span class="ms-2">
                  <span class="badge bg-secondary">{{ getRoleText(authStore.previousRole) }}</span>
                </span>
              </div>
            </div>
          </div>

          <!-- Role Detection Strategy -->
          <div class="col-md-6">
            <h6>角色检测策略</h6>
            <div class="mb-3">
              <select 
                v-model="selectedStrategy" 
                class="form-select mb-2"
                @change="updateStrategy"
                :disabled="authStore.isRoleLoading"
              >
                <option value="address">地址模式 (演示)</option>
                <option value="contract">智能合约模式</option>
                <option value="api">API模式</option>
              </select>
              <small class="text-muted">
                当前使用: {{ getStrategyDescription(selectedStrategy) }}
              </small>
            </div>
          </div>
        </div>

        <!-- Demo Address Management -->
        <div v-if="selectedStrategy === 'address'" class="mt-4">
          <h6>演示地址管理</h6>
          <div class="row">
            <div class="col-md-4" v-for="role in ['admin', 'reviewer', 'user']" :key="role">
              <div class="card mb-3">
                <div class="card-header py-2">
                  <small class="fw-bold">{{ getRoleText(role) }} 地址</small>
                </div>
                <div class="card-body py-2">
                  <div class="mb-2">
                    <input 
                      v-model="newAddresses[role]" 
                      type="text" 
                      class="form-control form-control-sm" 
                      :placeholder="`添加${getRoleText(role)}地址`"
                      @keyup.enter="addDemoAddress(role)"
                    >
                  </div>
                  <button 
                    class="btn btn-sm btn-outline-primary mb-2" 
                    @click="addDemoAddress(role)"
                    :disabled="!newAddresses[role]"
                  >
                    添加地址
                  </button>
                  <div class="demo-addresses">
                    <div 
                      v-for="(address, index) in getDemoAddresses(role)" 
                      :key="index"
                      class="d-flex align-items-center justify-content-between mb-1"
                    >
                      <small class="font-monospace text-truncate me-2">
                        {{ address.slice(0, 10) }}...{{ address.slice(-6) }}
                      </small>
                      <button 
                        class="btn btn-sm btn-outline-danger py-0 px-1"
                        @click="removeDemoAddress(role, index)"
                      >
                        <i class="bi bi-x"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="mt-4">
          <h6>操作</h6>
          <div class="d-flex gap-2 flex-wrap">
            <button 
              class="btn btn-primary"
              @click="refreshRole"
              :disabled="authStore.isRoleLoading || !authStore.isConnected"
            >
              <span v-if="authStore.isRoleLoading" class="spinner-border spinner-border-sm me-2"></span>
              <i v-else class="bi bi-arrow-clockwise me-2"></i>
              刷新角色
            </button>
            
            <button 
              class="btn btn-info"
              @click="showConfig"
            >
              <i class="bi bi-gear me-2"></i>
              查看配置
            </button>

            <button 
              class="btn btn-warning"
              @click="clearNotifications"
            >
              <i class="bi bi-bell-slash me-2"></i>
              清除通知
            </button>
          </div>
        </div>

        <!-- Configuration Display -->
        <div v-if="showConfigData" class="mt-4">
          <h6>当前配置</h6>
          <pre class="bg-light p-3 rounded"><code>{{ JSON.stringify(configData, null, 2) }}</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { roleService } from '@/services/roleService'

export default {
  name: 'RoleManager',
  setup() {
    const authStore = useAuthStore()
    const selectedStrategy = ref('address')
    const newAddresses = ref({
      admin: '',
      reviewer: '',
      user: ''
    })
    const showConfigData = ref(false)
    const configData = ref({})

    const getRoleText = (role) => {
      switch (role) {
        case 'admin': return '管理员'
        case 'reviewer': return '审核方'
        case 'user': return '用户'
        default: return '未知'
      }
    }

    const getRoleBadgeClass = (role) => {
      switch (role) {
        case 'admin': return 'bg-danger'
        case 'reviewer': return 'bg-warning'
        case 'user': return 'bg-success'
        default: return 'bg-secondary'
      }
    }

    const getStrategyDescription = (strategy) => {
      switch (strategy) {
        case 'address': return '基于地址列表的角色检测'
        case 'contract': return '基于智能合约的角色检测'
        case 'api': return '基于后端API的角色检测'
        default: return '未知策略'
      }
    }

    const updateStrategy = () => {
      roleService.setStrategy(selectedStrategy.value)
      console.log('角色检测策略已更新:', selectedStrategy.value)
    }

    const getDemoAddresses = (role) => {
      const config = roleService.getConfig()
      return config.demoRoles[role] || []
    }

    const addDemoAddress = (role) => {
      const address = newAddresses.value[role].trim()
      if (address) {
        roleService.addDemoAddresses(role, [address])
        newAddresses.value[role] = ''
      }
    }

    const removeDemoAddress = (role, index) => {
      const config = roleService.getConfig()
      if (config.demoRoles[role]) {
        config.demoRoles[role].splice(index, 1)
      }
    }

    const refreshRole = async () => {
      if (authStore.isConnected) {
        await authStore.determineUserRole()
      }
    }

    const showConfig = () => {
      configData.value = roleService.getConfig()
      showConfigData.value = !showConfigData.value
    }

    const clearNotifications = () => {
      authStore.clearRoleNotification()
      authStore.error = null
    }

    onMounted(() => {
      // Initialize with current strategy
      const config = roleService.getConfig()
      selectedStrategy.value = config.strategy
    })

    return {
      authStore,
      selectedStrategy,
      newAddresses,
      showConfigData,
      configData,
      getRoleText,
      getRoleBadgeClass,
      getStrategyDescription,
      updateStrategy,
      getDemoAddresses,
      addDemoAddress,
      removeDemoAddress,
      refreshRole,
      showConfig,
      clearNotifications
    }
  }
}
</script>

<style scoped>
.role-manager {
  max-width: 1000px;
  margin: 0 auto;
}

.demo-addresses {
  max-height: 150px;
  overflow-y: auto;
}

.font-monospace {
  font-family: 'Courier New', monospace;
}

pre {
  font-size: 0.875rem;
  max-height: 300px;
  overflow-y: auto;
}

.card-header {
  background-color: #f8f9fa;
}

.text-truncate {
  max-width: 120px;
}
</style>
