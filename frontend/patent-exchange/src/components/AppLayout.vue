<template>
  <div class="app-layout">
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container-fluid">
        <router-link class="navbar-brand" to="/">
          <i class="bi bi-shield-check me-2"></i>
          专利交易系统
        </router-link>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            <!-- User Navigation -->
            <li v-if="authStore.isUser" class="nav-item dropdown" :class="{ 'nav-loading': authStore.isRoleLoading }">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                role="button"
                data-bs-toggle="dropdown"
              >
                <span v-if="authStore.isRoleLoading" class="spinner-border spinner-border-sm me-2"></span>
                专利管理
              </a>
              <ul class="dropdown-menu">
                <li><router-link class="dropdown-item" to="/patents/upload">上传专利</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/search">搜索专利</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/my-patents">我的专利</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/trading">专利交易</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/protection">专利维权</router-link></li>
              </ul>
            </li>

            <!-- Reviewer Navigation -->
            <li v-if="authStore.isReviewer" class="nav-item dropdown" :class="{ 'nav-loading': authStore.isRoleLoading }">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                role="button"
                data-bs-toggle="dropdown"
              >
                <span v-if="authStore.isRoleLoading" class="spinner-border spinner-border-sm me-2"></span>
                审核管理
              </a>
              <ul class="dropdown-menu">
                <li><router-link class="dropdown-item" to="/review/pending">待审核列表</router-link></li>
                <li><router-link class="dropdown-item" to="/review/upload">上传审核</router-link></li>
                <li><router-link class="dropdown-item" to="/review/trading">交易审核</router-link></li>
                <li><router-link class="dropdown-item" to="/review/protection">维权审核</router-link></li>
                <li><hr class="dropdown-divider"></li>
                <li><router-link class="dropdown-item" to="/patents/upload">上传专利</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/search">搜索专利</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/protection">专利维权</router-link></li>
              </ul>
            </li>

            <!-- Admin Navigation -->
            <li v-if="authStore.isAdmin" class="nav-item dropdown" :class="{ 'nav-loading': authStore.isRoleLoading }">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                role="button"
                data-bs-toggle="dropdown"
              >
                <span v-if="authStore.isRoleLoading" class="spinner-border spinner-border-sm me-2"></span>
                系统管理
              </a>
              <ul class="dropdown-menu">
                <li><h6 class="dropdown-header">审核管理</h6></li>
                <li><router-link class="dropdown-item" to="/review/pending">待审核列表</router-link></li>
                <li><router-link class="dropdown-item" to="/review/upload">上传审核</router-link></li>
                <li><router-link class="dropdown-item" to="/review/trading">交易审核</router-link></li>
                <li><router-link class="dropdown-item" to="/review/protection">维权审核</router-link></li>
                <li><hr class="dropdown-divider"></li>
                <li><h6 class="dropdown-header">系统管理</h6></li>
                <li><router-link class="dropdown-item" to="/admin/users">用户管理</router-link></li>
                <li><router-link class="dropdown-item" to="/admin/transactions">交易记录</router-link></li>
                <li><router-link class="dropdown-item" to="/admin/statistics">系统统计</router-link></li>
                <li><hr class="dropdown-divider"></li>
                <li><h6 class="dropdown-header">专利功能</h6></li>
                <li><router-link class="dropdown-item" to="/patents/upload">上传专利</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/search">搜索专利</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/protection">专利维权</router-link></li>
              </ul>
            </li>
          </ul>

          <!-- Notification Center (only when connected) -->
          <div v-if="authStore.isConnected" class="navbar-nav me-3">
            <NotificationCenter />
          </div>

          <!-- Wallet Connection -->
          <div class="navbar-nav">
            <div v-if="!authStore.isConnected" class="nav-item">
              <button
                class="btn btn-outline-light"
                @click="connectWallet"
                :disabled="authStore.isAnyLoading"
              >
                <span v-if="authStore.isAnyLoading" class="spinner-border spinner-border-sm me-2"></span>
                连接钱包
              </button>
            </div>

            <div v-else class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle text-light"
                href="#"
                role="button"
                data-bs-toggle="dropdown"
                :class="{ 'nav-loading': authStore.isRoleLoading }"
              >
                <i class="bi bi-wallet2 me-2"></i>
                {{ authStore.shortAddress }}
                <span v-if="authStore.isRoleLoading" class="spinner-border spinner-border-sm ms-2"></span>
                <span v-else class="badge bg-success ms-2">{{ roleText }}</span>
              </a>
              <ul class="dropdown-menu dropdown-menu-end">
                <li><h6 class="dropdown-header">账户信息</h6></li>
                <li><span class="dropdown-item-text">地址: {{ authStore.account }}</span></li>
                <li><span class="dropdown-item-text">角色: {{ roleText }}</span></li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <router-link class="dropdown-item" to="/profile/personal-info">
                    <i class="bi bi-person me-2"></i>
                    个人信息
                  </router-link>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <button class="dropdown-item" @click="disconnectWallet">
                    <i class="bi bi-box-arrow-right me-2"></i>
                    断开连接
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Error Alert -->
    <div v-if="authStore.error" class="alert alert-danger alert-dismissible fade show m-3" role="alert">
      <i class="bi bi-exclamation-triangle me-2"></i>
      {{ authStore.error }}
      <button type="button" class="btn-close" @click="authStore.error = null"></button>
    </div>

    <!-- Role Change Notification -->
    <div
      v-if="authStore.roleChangeNotification"
      class="alert alert-dismissible fade show m-3"
      :class="getNotificationClass(authStore.roleChangeNotification.type)"
      role="alert"
    >
      <i :class="getNotificationIcon(authStore.roleChangeNotification.type)" class="me-2"></i>
      {{ authStore.roleChangeNotification.message }}
      <button type="button" class="btn-close" @click="clearNotification"></button>
    </div>

    <!-- Main Content -->
    <main class="flex-grow-1">
      <router-view />
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
      <div class="container-fluid">
        <div class="row">
          <div class="col-md-6">
            <h6>基于区块链的专利交易系统</h6>
            <p class="text-muted small">
              使用 Vue.js + Bootstrap + MetaMask + IPFS + Ganache 构建
            </p>
          </div>
          <div class="col-md-6 text-md-end">
            <p class="text-muted small">
              © 2024 专利交易系统. 保留所有权利.
            </p>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { useAuthStore } from '@/stores/auth'
import { computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import NotificationCenter from '@/components/NotificationCenter.vue'

export default {
  name: 'AppLayout',
  components: {
    NotificationCenter
  },
  setup() {
    const authStore = useAuthStore()

    const roleText = computed(() => {
      switch (authStore.userRole) {
        case 'admin': return '管理员'
        case 'reviewer': return '审核方'
        case 'user': return '用户'
        default: return '未知'
      }
    })

    const connectWallet = async () => {
      await authStore.connectWallet()
    }

    const disconnectWallet = () => {
      authStore.disconnectWallet()
    }

    const clearNotification = () => {
      authStore.clearRoleNotification()
    }

    const getNotificationClass = (type) => {
      switch (type) {
        case 'success': return 'alert-success'
        case 'warning': return 'alert-warning'
        case 'info': return 'alert-info'
        case 'error': return 'alert-danger'
        default: return 'alert-info'
      }
    }

    const getNotificationIcon = (type) => {
      switch (type) {
        case 'success': return 'bi bi-check-circle'
        case 'warning': return 'bi bi-exclamation-triangle'
        case 'info': return 'bi bi-info-circle'
        case 'error': return 'bi bi-x-circle'
        default: return 'bi bi-info-circle'
      }
    }

    // Initialize Bootstrap dropdowns on mount and when role changes
    const initializeDropdowns = () => {
      nextTick(() => {
        const tryBootstrapInit = (bootstrap) => {
          if (bootstrap && bootstrap.Dropdown) {
            try {
              // Initialize all dropdowns
              const dropdownElements = document.querySelectorAll('.dropdown-toggle')
              console.log(`Found ${dropdownElements.length} dropdown elements`)
              
              dropdownElements.forEach(element => {
                // Dispose existing dropdown if it exists
                const existingDropdown = bootstrap.Dropdown.getInstance(element)
                if (existingDropdown) {
                  existingDropdown.dispose()
                }
                // Create new dropdown instance with proper options
                new bootstrap.Dropdown(element, {
                  autoClose: true,
                  boundary: 'viewport'
                })
              })

              // Initialize navbar collapse for mobile
              const navbarToggler = document.querySelector('.navbar-toggler')
              const navbarCollapse = document.querySelector('.navbar-collapse')
              if (navbarToggler && navbarCollapse) {
                const existingCollapse = bootstrap.Collapse.getInstance(navbarCollapse)
                if (existingCollapse) {
                  existingCollapse.dispose()
                }
                new bootstrap.Collapse(navbarCollapse, { toggle: false })
              }
              
              console.log('Bootstrap dropdowns initialized successfully')
              return true
            } catch (error) {
              console.warn('Bootstrap initialization failed:', error)
              return false
            }
          }
          return false
        }

        // Try global Bootstrap first
        const globalBootstrap = window.bootstrap
        if (tryBootstrapInit(globalBootstrap)) {
          return
        }

        // Try dynamic import as fallback
        import('bootstrap').then((bootstrap) => {
          if (!tryBootstrapInit(bootstrap)) {
            initializeFallbackDropdowns()
          }
        }).catch(() => {
          console.warn('Dynamic Bootstrap import failed, using manual handlers')
          initializeFallbackDropdowns()
        })
      })
    }

    // Fallback dropdown initialization
    const initializeFallbackDropdowns = () => {
      console.log('Initializing fallback dropdown handlers')
      const dropdownToggles = document.querySelectorAll('.dropdown-toggle')
      dropdownToggles.forEach(toggle => {
        // Remove existing event listeners to avoid duplicates
        toggle.removeEventListener('click', handleDropdownClick)
        // Add new event listener
        toggle.addEventListener('click', handleDropdownClick)
        console.log('Added fallback click handler to:', toggle)
      })
    }

    // Manual dropdown toggle handler
    const handleDropdownClick = (event) => {
      console.log('Dropdown clicked:', event.currentTarget)
      event.preventDefault()
      event.stopPropagation()
      
      const toggle = event.currentTarget
      const dropdown = toggle.closest('.dropdown')
      const menu = dropdown?.querySelector('.dropdown-menu')
      
      if (!menu) {
        console.warn('No dropdown menu found for toggle:', toggle)
        return
      }

      // Close all other dropdowns
      document.querySelectorAll('.dropdown-menu.show').forEach(otherMenu => {
        if (otherMenu !== menu) {
          otherMenu.classList.remove('show')
          otherMenu.closest('.dropdown')?.querySelector('.dropdown-toggle')?.setAttribute('aria-expanded', 'false')
        }
      })

      // Toggle current dropdown
      const isOpen = menu.classList.contains('show')
      if (isOpen) {
        menu.classList.remove('show')
        toggle.setAttribute('aria-expanded', 'false')
        console.log('Closed dropdown')
      } else {
        menu.classList.add('show')
        toggle.setAttribute('aria-expanded', 'true')
        console.log('Opened dropdown')
      }
    }

    // Close dropdowns when clicking outside
    const handleDocumentClick = (event) => {
      if (!event.target.closest('.dropdown')) {
        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
          menu.classList.remove('show')
          menu.closest('.dropdown')?.querySelector('.dropdown-toggle')?.setAttribute('aria-expanded', 'false')
        })
      }
    }

    onMounted(() => {
      // Add a small delay to ensure DOM is fully rendered
      setTimeout(() => {
        initializeDropdowns()
      }, 50)
      
      // Add document click listener to close dropdowns
      document.addEventListener('click', handleDocumentClick)
      
      // Re-initialize dropdowns when auth state changes
      watch(() => authStore.userRole, () => {
        setTimeout(initializeDropdowns, 200)
      })
      
      watch(() => authStore.isConnected, () => {
        setTimeout(initializeDropdowns, 200)
      })
    })

    onUnmounted(() => {
      // Clean up event listeners
      document.removeEventListener('click', handleDocumentClick)
    })

    return {
      authStore,
      roleText,
      connectWallet,
      disconnectWallet,
      clearNotification,
      getNotificationClass,
      getNotificationIcon
    }
  }
}
</script>

<style scoped>
.app-layout {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
}

.navbar {
  width: 100%;
}

.navbar-brand {
  font-weight: bold;
}

.dropdown-item-text {
  font-size: 0.875rem;
  color: #6c757d;
}

main {
  flex: 1;
  width: 100%;
  padding: 0;
  margin: 0;
}

footer {
  width: 100%;
}

/* Ensure no margins on alerts */
.alert {
  margin-left: 0;
  margin-right: 0;
  border-radius: 0;
}

/* Loading states */
.nav-loading {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.nav-loading .nav-link {
  pointer-events: none;
}

/* Role transition animations */
.navbar-nav .nav-item {
  transition: all 0.3s ease;
}

/* Notification animations */
.alert {
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
