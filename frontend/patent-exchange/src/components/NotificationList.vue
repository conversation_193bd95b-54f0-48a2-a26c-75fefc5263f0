<template>
  <div class="notification-list">
    <div v-if="notifications.length === 0" class="text-center py-5">
      <i class="bi bi-bell-slash text-muted" style="font-size: 3rem;"></i>
      <h5 class="text-muted mt-3">暂无通知</h5>
      <p class="text-muted">当有新的通知时，会在这里显示</p>
    </div>

    <div v-else>
      <div
        v-for="notification in notifications"
        :key="notification.id"
        class="notification-item"
        :class="{ 'unread': !notification.read }"
        @click="handleNotificationClick(notification)"
      >
        <div class="d-flex align-items-start p-4">
          <!-- Notification Icon -->
          <div class="notification-icon me-3">
            <div class="icon-wrapper" :style="{ backgroundColor: getNotificationColor(notification.severity) + '20' }">
              <i :class="getNotificationIcon(notification.type)" :style="{ color: getNotificationColor(notification.severity) }"></i>
            </div>
          </div>

          <!-- Notification Content -->
          <div class="flex-grow-1">
            <div class="d-flex justify-content-between align-items-start mb-2">
              <h6 class="notification-title mb-0">{{ notification.title }}</h6>
              <div class="d-flex align-items-center gap-2">
                <small class="text-muted">{{ formatTime(notification.timestamp) }}</small>
                <span v-if="!notification.read" class="unread-indicator"></span>
              </div>
            </div>
            
            <p class="notification-message mb-2 text-muted">{{ notification.message }}</p>
            
            <!-- Notification Type Badge -->
            <div class="d-flex justify-content-between align-items-center">
              <span :class="getTypeBadgeClass(notification.type)" class="badge">
                {{ getTypeText(notification.type) }}
              </span>
              
              <!-- Action Buttons -->
              <div class="notification-actions">
                <button
                  v-if="!notification.read"
                  class="btn btn-sm btn-outline-primary me-2"
                  @click.stop="$emit('mark-read', notification.id)"
                  title="标记为已读"
                >
                  <i class="bi bi-check"></i>
                </button>
                <button
                  class="btn btn-sm btn-outline-danger"
                  @click.stop="$emit('remove', notification.id)"
                  title="删除通知"
                >
                  <i class="bi bi-x"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NotificationList',
  props: {
    notifications: {
      type: Array,
      required: true
    }
  },
  emits: ['mark-read', 'remove'],
  setup(props, { emit }) {
    const handleNotificationClick = (notification) => {
      if (!notification.read) {
        emit('mark-read', notification.id)
      }
      if (notification.action) {
        notification.action()
      }
    }

    const getNotificationIcon = (type) => {
      const iconMap = {
        patent_approved: 'bi bi-check-circle-fill',
        patent_rejected: 'bi bi-x-circle-fill',
        patent_sold: 'bi bi-currency-exchange',
        patent_purchased: 'bi bi-bag-check-fill',
        rights_protection_initiated: 'bi bi-shield-exclamation',
        rights_protection_resolved: 'bi bi-shield-check',
        transaction_completed: 'bi bi-check2-circle',
        review_assigned: 'bi bi-clipboard-check'
      }
      return iconMap[type] || 'bi bi-info-circle-fill'
    }

    const getNotificationColor = (severity) => {
      const colorMap = {
        success: '#198754',
        error: '#dc3545',
        warning: '#fd7e14',
        info: '#0dcaf0'
      }
      return colorMap[severity] || '#6c757d'
    }

    const getTypeText = (type) => {
      const typeMap = {
        patent_approved: '专利审核',
        patent_rejected: '专利审核',
        patent_sold: '专利出售',
        patent_purchased: '专利购买',
        rights_protection_initiated: '维权申请',
        rights_protection_resolved: '维权结果',
        transaction_completed: '交易完成',
        review_assigned: '审核任务'
      }
      return typeMap[type] || '系统通知'
    }

    const getTypeBadgeClass = (type) => {
      const classMap = {
        patent_approved: 'bg-success',
        patent_rejected: 'bg-danger',
        patent_sold: 'bg-warning',
        patent_purchased: 'bg-info',
        rights_protection_initiated: 'bg-secondary',
        rights_protection_resolved: 'bg-primary',
        transaction_completed: 'bg-success',
        review_assigned: 'bg-info'
      }
      return classMap[type] || 'bg-secondary'
    }

    const formatTime = (timestamp) => {
      const now = new Date()
      const time = new Date(timestamp)
      const diffInMinutes = Math.floor((now - time) / (1000 * 60))

      if (diffInMinutes < 1) {
        return '刚刚'
      } else if (diffInMinutes < 60) {
        return `${diffInMinutes}分钟前`
      } else if (diffInMinutes < 1440) {
        const hours = Math.floor(diffInMinutes / 60)
        return `${hours}小时前`
      } else {
        const days = Math.floor(diffInMinutes / 1440)
        if (days === 1) {
          return '昨天'
        } else if (days < 7) {
          return `${days}天前`
        } else {
          return time.toLocaleDateString('zh-CN')
        }
      }
    }

    return {
      handleNotificationClick,
      getNotificationIcon,
      getNotificationColor,
      getTypeText,
      getTypeBadgeClass,
      formatTime
    }
  }
}
</script>

<style scoped>
.notification-list {
  max-height: 600px;
  overflow-y: auto;
}

.notification-item {
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f8f9fa;
  position: relative;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-icon .icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.notification-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #212529;
}

.notification-message {
  font-size: 0.875rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.unread-indicator {
  width: 8px;
  height: 8px;
  background-color: #2196f3;
  border-radius: 50%;
  display: inline-block;
}

.notification-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
  opacity: 1;
}

.badge {
  font-size: 0.7rem;
  padding: 0.25em 0.5em;
}

.btn-sm {
  padding: 0.2rem 0.4rem;
  font-size: 0.75rem;
}

/* Scrollbar styling */
.notification-list::-webkit-scrollbar {
  width: 6px;
}

.notification-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
