<template>
  <!-- User Detail Modal -->
  <div
    class="modal fade"
    :id="modalId"
    tabindex="-1"
    :aria-labelledby="modalId + 'Label'"
    aria-hidden="true"
  >
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title" :id="modalId + 'Label'">
            <i class="bi bi-person-circle me-2"></i>
            用户详情
          </h5>
          <button
            type="button"
            class="btn-close btn-close-white"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
        
        <div class="modal-body">
          <!-- Loading State -->
          <div v-if="isLoading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="text-muted mt-3 mb-0">正在加载用户信息...</p>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {{ error }}
          </div>

          <!-- User Information -->
          <div v-else-if="userDetails">
            <div class="row g-3">
              <div class="col-12">
                <div class="card border-0 bg-light">
                  <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                      <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                           style="width: 50px; height: 50px;">
                        <i class="bi bi-person-fill text-white fs-4"></i>
                      </div>
                      <div>
                        <h6 class="mb-1 fw-bold">{{ userDetails.name }}</h6>
                        <small class="text-muted">用户信息</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-12">
                <div class="row g-2">
                  <div class="col-sm-4">
                    <label class="form-label text-muted small mb-1">姓名</label>
                    <div class="fw-bold">{{ userDetails.name }}</div>
                  </div>
                  
                  <div class="col-sm-4">
                    <label class="form-label text-muted small mb-1">手机号码</label>
                    <div class="fw-bold">{{ userDetails.phone }}</div>
                  </div>
                  
                  <div class="col-sm-4">
                    <label class="form-label text-muted small mb-1">身份证号</label>
                    <div class="fw-bold">{{ maskIdNumber(userDetails.idNumber) }}</div>
                  </div>
                </div>
              </div>

              <div class="col-12">
                <label class="form-label text-muted small mb-1">区块链地址</label>
                <div class="input-group">
                  <input
                    type="text"
                    class="form-control font-monospace small"
                    :value="userDetails.address"
                    readonly
                    style="background-color: #f8f9fa;"
                  >
                  <button
                    class="btn btn-outline-secondary"
                    type="button"
                    @click="copyAddress"
                    :title="copyButtonText"
                  >
                    <i :class="copyIcon"></i>
                  </button>
                </div>
              </div>

              <!-- Additional Info -->
              <div class="col-12">
                <div class="alert alert-info d-flex align-items-center" role="alert">
                  <i class="bi bi-info-circle me-2"></i>
                  <small>此信息来自区块链记录，已通过身份验证</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-lg me-2"></i>
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { userService } from '@/services/userService'

export default {
  name: 'UserDetailModal',
  props: {
    modalId: {
      type: String,
      required: true
    },
    userAddress: {
      type: String,
      default: null
    }
  },
  emits: ['user-loaded'],
  setup(props, { emit }) {
    const isLoading = ref(false)
    const error = ref(null)
    const userDetails = ref(null)
    const copyButtonText = ref('复制地址')
    const copyIcon = ref('bi bi-clipboard')

    const loadUserDetails = async () => {
      if (!props.userAddress) return

      try {
        isLoading.value = true
        error.value = null
        userDetails.value = null

        const details = await userService.getUserDetails(props.userAddress)
        userDetails.value = details
        
        emit('user-loaded', details)
      } catch (err) {
        error.value = err.message
      } finally {
        isLoading.value = false
      }
    }

    const maskIdNumber = (idNumber) => {
      if (!idNumber || idNumber === '未知') return idNumber
      if (idNumber.length < 8) return idNumber
      
      const start = idNumber.substring(0, 4)
      const end = idNumber.substring(idNumber.length - 4)
      const middle = '*'.repeat(idNumber.length - 8)
      
      return start + middle + end
    }

    const copyAddress = async () => {
      try {
        await navigator.clipboard.writeText(userDetails.value.address)
        copyButtonText.value = '已复制!'
        copyIcon.value = 'bi bi-check-lg'
        
        setTimeout(() => {
          copyButtonText.value = '复制地址'
          copyIcon.value = 'bi bi-clipboard'
        }, 2000)
      } catch (err) {
        console.error('复制失败:', err)
        copyButtonText.value = '复制失败'
        copyIcon.value = 'bi bi-x-lg'
        
        setTimeout(() => {
          copyButtonText.value = '复制地址'
          copyIcon.value = 'bi bi-clipboard'
        }, 2000)
      }
    }

    // Watch for address changes
    watch(() => props.userAddress, (newAddress) => {
      if (newAddress) {
        loadUserDetails()
      } else {
        userDetails.value = null
        error.value = null
      }
    }, { immediate: true })

    return {
      isLoading,
      error,
      userDetails,
      copyButtonText,
      copyIcon,
      maskIdNumber,
      copyAddress
    }
  }
}
</script>

<style scoped>
.modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modal-header {
  border-radius: 12px 12px 0 0;
  border-bottom: none;
}

.modal-footer {
  border-top: 1px solid #dee2e6;
  border-radius: 0 0 12px 12px;
}

.card {
  border-radius: 8px;
}

.input-group .form-control {
  border-radius: 6px 0 0 6px;
}

.input-group .btn {
  border-radius: 0 6px 6px 0;
}

.alert {
  border-radius: 6px;
}

.font-monospace {
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.875rem;
}
</style>
