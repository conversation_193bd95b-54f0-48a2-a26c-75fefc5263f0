# Patent Exchange Platform API Documentation

This document defines all the backend APIs required by the frontend application. The frontend currently uses mock implementations that need to be replaced with actual backend API calls.

## Base Configuration

```javascript
const API_BASE_URL = process.env.VUE_APP_API_BASE_URL || 'http://localhost:3000/api'
const IPFS_BASE_URL = process.env.VUE_APP_IPFS_BASE_URL || 'http://localhost:5001/api/v0'
const BLOCKCHAIN_BASE_URL = process.env.VUE_APP_BLOCKCHAIN_BASE_URL || 'http://localhost:7545'
```

## Authentication & Authorization

### 1. User Role Management

#### GET /api/user/role/:address
**Description**: Get user role by blockchain address
**Parameters**:
- `address` (string): User's blockchain wallet address
**Response**:
```json
{
  "success": true,
  "data": {
    "address": "******************************************",
    "role": "user|reviewer|admin",
    "permissions": ["upload", "search", "trade"],
    "lastUpdated": "2024-01-20T10:30:00Z"
  }
}
```

#### POST /api/user/role
**Description**: Update user role (admin only)
**Body**:
```json
{
  "address": "******************************************",
  "role": "user|reviewer|admin",
  "updatedBy": "0xadmin_address"
}
```

## User Management

### 2. User Profile Operations

#### GET /api/user/profile/:address
**Description**: Get user profile information
**Response**:
```json
{
  "success": true,
  "data": {
    "address": "******************************************",
    "name": "张三",
    "phone": "13800138000",
    "idNumber": "110101199001011234",
    "registrationDate": "2024-01-15T00:00:00Z",
    "lastLoginDate": "2024-01-20T10:30:00Z",
    "status": "active|inactive|suspended"
  }
}
```

#### PUT /api/user/profile/:address
**Description**: Update user profile
**Body**:
```json
{
  "name": "张三",
  "phone": "13800138000",
  "idNumber": "110101199001011234"
}
```

#### GET /api/admin/users
**Description**: Get all users (admin only)
**Query Parameters**:
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20)
- `role` (string): Filter by role
- `status` (string): Filter by status
**Response**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "address": "******************************************",
        "name": "张三",
        "phone": "13800138000",
        "idNumber": "110101199001011234",
        "role": "user",
        "registrationDate": "2024-01-15T00:00:00Z",
        "lastLoginDate": "2024-01-20T10:30:00Z",
        "status": "active"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 156,
      "totalPages": 8
    }
  }
}
```

#### GET /api/admin/users/statistics
**Description**: Get user statistics (admin only)
**Response**:
```json
{
  "success": true,
  "data": {
    "totalUsers": 156,
    "activeUsers": 142,
    "newUsersThisMonth": 23,
    "usersByRole": {
      "user": 134,
      "reviewer": 18,
      "admin": 4
    },
    "userGrowth": [
      { "month": "2023-09", "count": 89 },
      { "month": "2023-10", "count": 102 }
    ]
  }
}
```

## Patent Management

### 3. Patent Upload & Registration

#### POST /api/patents/upload
**Description**: Upload a new patent for review
**Content-Type**: multipart/form-data
**Body**:
```javascript
{
  // Patent basic information
  patentName: "一种新型智能手机充电技术",
  patentNumber: "CN202410001234.5",
  patentCategory: "电子技术",
  transferPrice: "50000",
  patentAbstract: "本发明提供了一种新型的智能手机无线充电技术...",
  applicationDate: "2024-01-15",
  expirationDate: "2044-01-15",

  // Patent owner information
  ownerName: "张三",
  ownerIdNumber: "110101199001011234",

  // Upload settings
  isAgentSale: false,

  // Files (multipart)
  patentDocument: File, // Patent technical document
  ownershipDocument: File, // Patent ownership certificate or agency authorization

  // Uploader information (from auth)
  uploaderAddress: "******************************************"
}
```
**Response**:
```json
{
  "success": true,
  "data": {
    "patentId": "patent_12345",
    "status": "under_review",
    "documentHashes": {
      "patent": "QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
      "ownership": "QmYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY"
    },
    "uploadDate": "2024-01-20T10:30:00Z",
    "message": "专利上传成功，等待审核"
  }
}
```

### 4. Patent Search & Discovery

#### GET /api/patents/search
**Description**: Search patents with filters
**Query Parameters**:
- `name` (string): Patent name keyword
- `number` (string): Patent number
- `category` (string): Patent category
- `minPrice` (number): Minimum price
- `maxPrice` (number): Maximum price
- `status` (string): Patent status
- `page` (number): Page number
- `limit` (number): Items per page
- `sortBy` (string): Sort field (name, price, uploadDate)
- `sortOrder` (string): Sort order (asc, desc)
**Response**:
```json
{
  "success": true,
  "data": {
    "patents": [
      {
        "id": "1",
        "name": "一种新型智能手机充电技术",
        "number": "CN202410001234.5",
        "category": "电子技术",
        "price": "50000",
        "status": "normal",
        "uploaderAddress": "******************************************",
        "uploaderName": "张三",
        "uploadDate": "2024-01-15T00:00:00Z",
        "viewCount": 156,
        "downloadCount": 23
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "totalPages": 3
    }
  }
}
```

#### GET /api/patents/:id
**Description**: Get detailed patent information
**Response**:
```json
{
  "success": true,
  "data": {
    "id": "1",
    "name": "一种新型智能手机充电技术",
    "number": "CN202410001234.5",
    "category": "电子技术",
    "price": "50000",
    "transferPrice": "50000",
    "abstract": "本发明提供了一种新型的智能手机无线充电技术...",
    "applicationDate": "2024-01-15",
    "expiryDate": "2044-01-15",
    "ownerName": "张三",
    "ownerIdNumber": "110101199001011234",
    "uploaderAddress": "******************************************",
    "uploaderName": "张三",
    "uploaderPhone": "13800138000",
    "isProxySale": false,
    "status": "normal",
    "documentHash": "QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    "certificateHash": "QmYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY",
    "uploadDate": "2024-01-15T00:00:00Z",
    "viewCount": 156,
    "downloadCount": 23,
    "blockchain": {
      "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
      "blockNumber": "12345678",
      "contractAddress": "0x9876543210987654321098765432109876543210"
    },
    "documents": {
      "patent": "patent_document.pdf",
      "proxy": "proxy_authorization.pdf",
      "certificate": "patent_certificate.pdf"
    }
  }
}
```

#### GET /api/patents/user/:address
**Description**: Get patents uploaded by a specific user
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "1",
      "name": "智能手机快速充电技术",
      "number": "CN202123456789.1",
      "status": "approved",
      "uploadDate": "2024-01-15T00:00:00Z",
      "price": "50000",
      "viewCount": 156,
      "downloadCount": 23
    }
  ]
}
```

#### PUT /api/patents/:id/withdraw
**Description**: Withdraw patent from trading (make unavailable)
**Body**:
```json
{
  "reason": "Temporary withdrawal for updates"
}
```
**Response**:
```json
{
  "success": true,
  "message": "专利撤回成功"
}
```

#### PUT /api/patents/:id/restore
**Description**: Restore patent to trading (make available again)
**Response**:
```json
{
  "success": true,
  "message": "专利恢复成功"
}
```

### 5. Patent Document Management

#### GET /api/patents/:id/download/:documentType
**Description**: Download patent document
**Parameters**:
- `documentType` (string): Type of document (patent, certificate, proxy)
**Response**: File download or redirect to IPFS URL

#### POST /api/ipfs/upload
**Description**: Upload files to IPFS
**Content-Type**: multipart/form-data
**Body**: Files to upload
**Response**:
```json
{
  "success": true,
  "data": {
    "hashes": ["QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"],
    "urls": ["https://ipfs.io/ipfs/QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"]
  }
}
```

## Transaction Management

### 6. Patent Trading Operations

#### POST /api/transactions/initiate
**Description**: Initiate a patent purchase transaction
**Body**:
```json
{
  "patentId": "1",
  "buyerAddress": "0x2345678901234567890123456789012345678901",
  "sellerAddress": "******************************************",
  "price": "50000"
}
```
**Response**:
```json
{
  "success": true,
  "data": {
    "transactionId": "tx_001",
    "status": "pending",
    "message": "交易已发起，等待审核"
  }
}
```

#### GET /api/transactions/user/:address
**Description**: Get user's transaction history
**Query Parameters**:
- `type` (string): Transaction type (purchase, sale)
- `status` (string): Transaction status
- `page` (number): Page number
- `limit` (number): Items per page
**Response**:
```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": "tx_001",
        "patentId": "1",
        "patentName": "一种新型智能手机充电技术",
        "patentNumber": "CN202410001234.5",
        "buyerAddress": "0x2345678901234567890123456789012345678901",
        "buyerName": "李四",
        "sellerAddress": "******************************************",
        "sellerName": "张三",
        "price": "50000",
        "submitDate": "2024-01-20T10:30:00Z",
        "status": "pending",
        "type": "purchase"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

#### GET /api/transactions/pending
**Description**: Get pending transactions for review (reviewer/admin only)
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "tx_001",
      "patentId": "1",
      "patentName": "一种新型智能手机充电技术",
      "patentNumber": "CN202410001234.5",
      "buyerAddress": "0x2345678901234567890123456789012345678901",
      "buyerName": "李四",
      "sellerAddress": "******************************************",
      "sellerName": "张三",
      "price": "50000",
      "submitDate": "2024-01-20T10:30:00Z",
      "status": "pending",
      "type": "purchase"
    }
  ]
}
```

#### PUT /api/transactions/:id/approve
**Description**: Approve a transaction (reviewer/admin only)
**Body**:
```json
{
  "reviewerAddress": "0xreviewer_address",
  "comments": "Transaction approved after verification"
}
```
**Response**:
```json
{
  "success": true,
  "message": "交易已批准"
}
```

#### PUT /api/transactions/:id/reject
**Description**: Reject a transaction (reviewer/admin only)
**Body**:
```json
{
  "reviewerAddress": "0xreviewer_address",
  "reason": "Insufficient documentation",
  "comments": "Additional verification required"
}
```
**Response**:
```json
{
  "success": true,
  "message": "交易已拒绝"
}
```

### 7. Rights Protection Management

#### POST /api/protection/request
**Description**: Submit a rights protection request
**Body**:
```json
{
  "patentAddress": "******************************************",
  "patentName": "一种新型智能手机充电技术",
  "applicantAddress": "0x2345678901234567890123456789012345678901",
  "description": "发现某公司未经授权使用了我的专利技术...",
  "evidenceFiles": ["evidence1.pdf", "evidence2.jpg"]
}
```
**Response**:
```json
{
  "success": true,
  "data": {
    "protectionId": "protection_001",
    "status": "pending",
    "message": "维权申请已提交"
  }
}
```

#### GET /api/protection/pending
**Description**: Get pending protection requests (reviewer/admin only)
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "protection_001",
      "patentAddress": "******************************************",
      "patentName": "一种新型智能手机充电技术",
      "applicantAddress": "0x2345678901234567890123456789012345678901",
      "applicantName": "王五",
      "description": "发现某公司未经授权使用了我的专利技术...",
      "submitDate": "2024-01-18T14:20:00Z",
      "evidenceUrl": "/documents/evidence_001.pdf",
      "status": "pending"
    }
  ]
}
```

#### PUT /api/protection/:id/approve
**Description**: Approve a protection request (reviewer/admin only)
**Body**:
```json
{
  "reviewerAddress": "0xreviewer_address",
  "resolution": "Protection granted, infringement confirmed"
}
```
**Response**:
```json
{
  "success": true,
  "message": "维权申请已批准"
}
```

#### PUT /api/protection/:id/reject
**Description**: Reject a protection request (reviewer/admin only)
**Body**:
```json
{
  "reviewerAddress": "0xreviewer_address",
  "reason": "Insufficient evidence"
}
```
**Response**:
```json
{
  "success": true,
  "message": "维权申请已拒绝"
}
```

#### GET /api/protection/cases/pending
**Description**: Get pending protection cases for review
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "prot_001",
      "patentId": "4",
      "patentName": "区块链数据存储优化方案",
      "patentNumber": "CN202410001237.8",
      "claimantAddress": "0x5678901234567890123456789012345678901234",
      "claimantName": "孙七",
      "currentOwnerAddress": "0x6789012345678901234567890123456789012345",
      "currentOwnerName": "周八",
      "description": "我是该专利的真正发明人，有充分的证据证明专利权归属。",
      "evidenceHash": "QmZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ",
      "submitDate": "2024-01-22T11:15:00Z",
      "status": "pending"
    }
  ]
}
```

## Review Management

### 8. Patent Upload Review

#### GET /api/review/uploads/pending
**Description**: Get pending patent uploads for review (reviewer/admin only)
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "upload_001",
      "patentName": "智能家居控制系统",
      "patentNumber": "CN202410001235.6",
      "category": "智能控制",
      "price": "80000",
      "uploaderAddress": "0x2345678901234567890123456789012345678901",
      "uploaderName": "李四",
      "uploaderPhone": "13900139000",
      "ownerName": "李四",
      "ownerIdNumber": "110101199002021234",
      "isAgentSale": false,
      "submitDate": "2024-01-19T09:15:00Z",
      "documentHashes": {
        "patent": "QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
        "ownership": "QmYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY"
      },
      "status": "pending"
    }
  ]
}
```

#### PUT /api/review/uploads/:id/approve
**Description**: Approve a patent upload (reviewer/admin only)
**Body**:
```json
{
  "reviewerAddress": "0xreviewer_address",
  "comments": "Patent documentation verified and approved"
}
```
**Response**:
```json
{
  "success": true,
  "message": "专利上传已批准"
}
```

#### PUT /api/review/uploads/:id/reject
**Description**: Reject a patent upload (reviewer/admin only)
**Body**:
```json
{
  "reviewerAddress": "0xreviewer_address",
  "reason": "文档不完整",
  "comments": "请补充完整的专利权证明文档"
}
```
**Response**:
```json
{
  "success": true,
  "message": "专利上传已拒绝"
}
```

## Notification Management

### 9. Real-time Notifications

#### GET /api/notifications/:address
**Description**: Get user's notifications
**Query Parameters**:
- `page` (number): Page number
- `limit` (number): Items per page
- `unreadOnly` (boolean): Show only unread notifications
**Response**:
```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "notif_001",
        "type": "patent_approved",
        "title": "专利审核通过",
        "message": "您的专利"智能手机快速充电技术"已通过审核",
        "severity": "success",
        "read": false,
        "timestamp": "2024-01-20T10:30:00Z",
        "relatedId": "patent_1"
      }
    ],
    "unreadCount": 3,
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 15,
      "totalPages": 1
    }
  }
}
```

#### PUT /api/notifications/:id/read
**Description**: Mark notification as read
**Response**:
```json
{
  "success": true,
  "message": "通知已标记为已读"
}
```

#### PUT /api/notifications/:address/read-all
**Description**: Mark all notifications as read for a user
**Response**:
```json
{
  "success": true,
  "message": "所有通知已标记为已读"
}
```

#### POST /api/notifications/send
**Description**: Send notification to user (system use)
**Body**:
```json
{
  "recipientAddress": "******************************************",
  "type": "patent_approved",
  "title": "专利审核通过",
  "message": "您的专利已通过审核",
  "severity": "success",
  "relatedId": "patent_1"
}
```

## Admin Statistics & Analytics

### 10. System Statistics

#### GET /api/admin/statistics/overview
**Description**: Get system overview statistics (admin only)
**Response**:
```json
{
  "success": true,
  "data": {
    "patents": {
      "total": 1234,
      "approved": 1156,
      "pending": 45,
      "rejected": 33,
      "thisMonth": 89
    },
    "transactions": {
      "total": 567,
      "completed": 523,
      "pending": 32,
      "rejected": 12,
      "totalValue": "2345000"
    },
    "users": {
      "total": 156,
      "active": 142,
      "newThisMonth": 23
    },
    "protectionCases": {
      "total": 23,
      "resolved": 18,
      "pending": 5
    }
  }
}
```

#### GET /api/admin/statistics/patents
**Description**: Get detailed patent statistics (admin only)
**Query Parameters**:
- `period` (string): Time period (week, month, quarter, year)
- `category` (string): Filter by patent category
**Response**:
```json
{
  "success": true,
  "data": {
    "byCategory": [
      { "category": "电子技术", "count": 234 },
      { "category": "智能控制", "count": 189 }
    ],
    "byStatus": [
      { "status": "approved", "count": 1156 },
      { "status": "pending", "count": 45 }
    ],
    "timeline": [
      { "date": "2024-01-01", "uploads": 12, "approvals": 8 },
      { "date": "2024-01-02", "uploads": 15, "approvals": 11 }
    ],
    "topUploaders": [
      { "address": "0x1234...", "name": "张三", "count": 23 },
      { "address": "0x5678...", "name": "李四", "count": 18 }
    ]
  }
}
```

#### GET /api/admin/statistics/transactions
**Description**: Get detailed transaction statistics (admin only)
**Response**:
```json
{
  "success": true,
  "data": {
    "volumeByMonth": [
      { "month": "2023-12", "volume": "456000", "count": 45 },
      { "month": "2024-01", "volume": "567000", "count": 52 }
    ],
    "averagePrice": "45000",
    "topTraders": [
      { "address": "0x1234...", "name": "张三", "totalVolume": "123000" },
      { "address": "0x5678...", "name": "李四", "totalVolume": "98000" }
    ],
    "processingTimes": {
      "average": "2.5 days",
      "median": "2 days"
    }
  }
}
```

## Error Handling

### Standard Error Response Format

All API endpoints should return errors in the following format:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "field": "patentName",
      "reason": "Patent name is required"
    }
  }
}
```

### Common Error Codes

- `VALIDATION_ERROR`: Input validation failed
- `AUTHENTICATION_ERROR`: User not authenticated
- `AUTHORIZATION_ERROR`: User lacks required permissions
- `NOT_FOUND`: Requested resource not found
- `CONFLICT`: Resource already exists or conflict detected
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INTERNAL_ERROR`: Server internal error
- `BLOCKCHAIN_ERROR`: Blockchain transaction failed
- `IPFS_ERROR`: IPFS operation failed

## Implementation Notes

### Authentication
- All API calls should include the user's blockchain address for authentication
- Role-based access control should be enforced at the API level
- Consider implementing JWT tokens for session management

### File Uploads
- Use multipart/form-data for file uploads
- Implement file size and type validation
- Store files on IPFS and return hashes
- Consider implementing file encryption for sensitive documents

### Blockchain Integration
- APIs should interact with smart contracts for critical operations
- Implement proper error handling for blockchain failures
- Consider gas optimization for contract calls
- Maintain audit trails for all blockchain transactions

### Performance Considerations
- Implement pagination for list endpoints
- Use caching for frequently accessed data
- Consider implementing rate limiting
- Optimize database queries for large datasets

### Security
- Validate all input parameters
- Implement proper CORS policies
- Use HTTPS for all communications
- Sanitize file uploads to prevent malicious content
- Implement proper logging for security auditing