{"name": "patent-exchange", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix"}, "dependencies": {"@popperjs/core": "^2.11.8", "axios": "^1.9.0", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.13.1", "ethers": "^6.14.3", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "web3": "^4.16.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@vitejs/plugin-vue": "^5.2.3", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}