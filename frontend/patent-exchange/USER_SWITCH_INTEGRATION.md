# 用户切换集成指南

## 概述

本文档说明如何在前端集成用户切换功能，支持MetaMask账户切换和基于角色的权限控制。

## 功能特点

- ✅ **自动检测MetaMask账户切换**
- ✅ **获取用户角色和权限信息**  
- ✅ **本地缓存用户状态**
- ✅ **权限检查辅助方法**
- ✅ **监听器模式支持组件更新**
- ✅ **未注册用户处理**

## API 端点

### 1. 用户切换
```
POST /api/user/switch/:address
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "address": "0x1234...abcd",
    "isRegistered": true,
    "role": "admin",
    "permissions": [
      "patents.view",
      "patents.upload",
      "users.manage",
      "statistics.view"
    ],
    "profile": {
      "name": "系统管理员",
      "phone": "13800000000",
      "idNumber": "ADMIN001",
      "registrationDate": "2024-01-01T00:00:00.000Z",
      "lastLoginDate": "2024-01-15T10:30:00.000Z",
      "isActive": true
    },
    "needsRegistration": false
  },
  "message": "Switched to user: 系统管理员 (admin)"
}
```

### 2. 权限检查
```
GET /api/user/permission/:address/:permission
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "hasPermission": true,
    "role": "admin",
    "permission": "users.manage",
    "reason": "Permission granted"
  }
}
```

## 前端使用

### 1. 导入用户切换服务

```javascript
import userSwitchService from '@/services/userSwitchService';
```

### 2. 基本用法

#### 连接MetaMask并切换用户
```javascript
// 连接MetaMask
try {
  const userData = await userSwitchService.connectMetaMask();
  console.log('用户信息:', userData);
} catch (error) {
  console.error('连接失败:', error);
}

// 手动切换到指定地址
try {
  const userData = await userSwitchService.switchUser('0x1234...abcd');
  console.log('切换成功:', userData);
} catch (error) {
  console.error('切换失败:', error);
}
```

#### 获取当前用户信息
```javascript
const currentUser = userSwitchService.getCurrentUser();
const userAddress = userSwitchService.getCurrentAddress();
const userRole = userSwitchService.getUserRole();
```

#### 权限检查
```javascript
// 检查当前用户权限
const canManageUsers = userSwitchService.hasPermission('users.manage');
const isAdmin = userSwitchService.isAdmin();
const isReviewer = userSwitchService.isReviewer();
const isRegistered = userSwitchService.isRegistered();

// 检查指定用户权限
const hasPermission = await userSwitchService.checkPermission(
  '0x1234...abcd', 
  'patents.upload'
);
```

### 3. 在Vue组件中使用

```vue
<template>
  <div>
    <!-- 用户信息显示 -->
    <div v-if="currentUser">
      <h3>当前用户: {{ currentUser.profile?.name || currentUser.address }}</h3>
      <p>角色: {{ currentUser.role }}</p>
      <p>状态: {{ currentUser.profile?.isActive ? '活跃' : '未激活' }}</p>
    </div>

    <!-- 未注册用户提示 -->
    <div v-else-if="currentUser && !currentUser.isRegistered">
      <p>用户未注册，请先注册</p>
      <button @click="registerUser">注册用户</button>
    </div>

    <!-- 连接MetaMask按钮 -->
    <button v-else @click="connectWallet">连接钱包</button>

    <!-- 基于权限的功能按钮 -->
    <div v-if="currentUser?.isRegistered">
      <button v-if="hasUploadPermission" @click="uploadPatent">上传专利</button>
      <button v-if="hasReviewPermission" @click="reviewPatents">审查专利</button>
      <button v-if="hasAdminPermission" @click="manageUsers">用户管理</button>
    </div>
  </div>
</template>

<script>
import userSwitchService from '@/services/userSwitchService';

export default {
  name: 'UserSwitchExample',
  data() {
    return {
      currentUser: null
    };
  },
  computed: {
    hasUploadPermission() {
      return userSwitchService.hasPermission('patents.upload');
    },
    hasReviewPermission() {
      return userSwitchService.hasPermission('patents.review');
    },
    hasAdminPermission() {
      return userSwitchService.isAdmin();
    }
  },
  mounted() {
    // 获取当前用户
    this.currentUser = userSwitchService.getCurrentUser();
    
    // 监听用户切换事件
    userSwitchService.addSwitchListener(this.onUserSwitch);
  },
  beforeUnmount() {
    // 移除监听器
    userSwitchService.removeSwitchListener(this.onUserSwitch);
  },
  methods: {
    onUserSwitch(userData) {
      this.currentUser = userData;
      // 用户切换后的处理逻辑
      if (userData) {
        this.$message.success(`已切换到用户: ${userData.profile?.name || userData.address}`);
      } else {
        this.$message.info('用户已登出');
      }
    },
    
    async connectWallet() {
      try {
        const userData = await userSwitchService.connectMetaMask();
        this.currentUser = userData;
      } catch (error) {
        this.$message.error('连接钱包失败: ' + error.message);
      }
    },
    
    async registerUser() {
      // 实现用户注册逻辑
      console.log('注册用户');
    },
    
    uploadPatent() {
      // 实现专利上传逻辑
      console.log('上传专利');
    },
    
    reviewPatents() {
      // 实现专利审查逻辑
      console.log('审查专利');
    },
    
    manageUsers() {
      // 实现用户管理逻辑
      console.log('用户管理');
    }
  }
};
</script>
```

### 4. 路由守卫集成

```javascript
// router/index.js
import userSwitchService from '@/services/userSwitchService';

// 权限检查路由守卫
router.beforeEach((to, from, next) => {
  const requiredPermission = to.meta.permission;
  
  if (requiredPermission) {
    const hasPermission = userSwitchService.hasPermission(requiredPermission);
    
    if (!hasPermission) {
      // 重定向到无权限页面或登录页
      next('/unauthorized');
      return;
    }
  }
  
  // 检查是否需要注册
  const requiresRegistration = to.meta.requiresRegistration;
  if (requiresRegistration && !userSwitchService.isRegistered()) {
    next('/register');
    return;
  }
  
  next();
});

// 路由配置示例
const routes = [
  {
    path: '/admin',
    component: AdminPanel,
    meta: {
      permission: 'users.manage',
      requiresRegistration: true
    }
  },
  {
    path: '/review',
    component: ReviewPanel,
    meta: {
      permission: 'patents.review',
      requiresRegistration: true
    }
  }
];
```

### 5. 权限指令

```javascript
// directives/permission.js
import userSwitchService from '@/services/userSwitchService';

export default {
  mounted(el, binding) {
    const { value } = binding;
    
    if (value && !userSwitchService.hasPermission(value)) {
      el.style.display = 'none';
    }
  },
  updated(el, binding) {
    const { value } = binding;
    
    if (value && !userSwitchService.hasPermission(value)) {
      el.style.display = 'none';
    } else {
      el.style.display = '';
    }
  }
};

// 在main.js中注册
import permissionDirective from '@/directives/permission';
app.directive('permission', permissionDirective);

// 在模板中使用
<button v-permission="'users.manage'">管理用户</button>
<div v-permission="'statistics.view'">统计信息</div>
```

## 权限列表

### 普通用户 (user)
- `patents.view` - 查看专利
- `patents.search` - 搜索专利
- `patents.upload` - 上传专利
- `patents.own.manage` - 管理自己的专利
- `transactions.initiate` - 发起交易
- `transactions.own.view` - 查看自己的交易
- `profile.own.view` - 查看自己的资料
- `profile.own.edit` - 编辑自己的资料
- `notifications.own.view` - 查看自己的通知

### 审查员 (reviewer)
包含用户权限，另外增加：
- `patents.review` - 审查专利
- `patents.approve` - 批准专利
- `patents.reject` - 拒绝专利
- `transactions.review` - 审查交易
- `uploads.review` - 审查上传
- `uploads.approve` - 批准上传
- `uploads.reject` - 拒绝上传
- `protection.review` - 审查保护申请

### 管理员 (admin)
包含审查员权限，另外增加：
- `patents.all.manage` - 管理所有专利
- `transactions.all.view` - 查看所有交易
- `protection.approve` - 批准保护申请
- `protection.reject` - 拒绝保护申请
- `users.view` - 查看用户
- `users.manage` - 管理用户
- `users.roles.edit` - 编辑用户角色
- `statistics.view` - 查看统计信息
- `system.manage` - 系统管理
- `ganache.manage` - Ganache管理
- `profile.all.view` - 查看所有用户资料
- `profile.all.edit` - 编辑所有用户资料
- `notifications.all.view` - 查看所有通知
- `notifications.send` - 发送通知

## 最佳实践

1. **组件初始化时检查用户状态**
2. **监听用户切换事件更新UI**
3. **使用权限指令隐藏/显示功能**
4. **在路由守卫中检查访问权限**
5. **处理未注册用户的引导流程**
6. **提供清晰的权限不足提示**

## 故障排除

### 问题1：MetaMask未检测到
确保用户已安装MetaMask扩展并解锁账户。

### 问题2：用户未注册
引导用户完成注册流程，或联系管理员进行注册。

### 问题3：权限不足
检查用户角色是否正确，或联系管理员分配相应权限。

### 问题4：账户切换不生效
检查MetaMask是否正常工作，尝试刷新页面重新连接。 