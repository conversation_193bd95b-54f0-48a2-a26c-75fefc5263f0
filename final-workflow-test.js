#!/usr/bin/env node

/**
 * Final comprehensive test of the patent purchase workflow fix
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// Test configuration with actual Ganache addresses
const TEST_CONFIG = {
  adminAddress: '******************************************',
  reviewerAddress: '******************************************',
  userAddress: '******************************************'
};

// Helper function to make API calls
async function apiCall(method, endpoint, data = null, userAddress = null) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (userAddress) {
      config.headers['X-User-Address'] = userAddress;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message,
      status: error.response?.status,
      code: error.response?.data?.error?.code
    };
  }
}

async function testRoleAccess() {
  console.log('\n🔍 Step 1: Testing Role Access');
  console.log('==============================');
  
  // Test reviewer access
  const reviewerResult = await apiCall('GET', '/transactions/pending', null, TEST_CONFIG.reviewerAddress);
  if (reviewerResult.success) {
    const transactions = reviewerResult.data.data?.data || reviewerResult.data.data || [];
    console.log(`✅ Reviewer access: Found ${transactions.length} pending transactions`);
    return transactions.length > 0 ? transactions[0].id : null;
  } else {
    console.log(`❌ Reviewer access failed: ${reviewerResult.error}`);
    return null;
  }
}

async function testUserAccess() {
  console.log('\n🔍 Step 2: Testing User Access (Should be denied)');
  console.log('==================================================');
  
  const userResult = await apiCall('GET', '/transactions/pending', null, TEST_CONFIG.userAddress);
  if (userResult.success) {
    console.log(`❌ User access should be denied but was granted!`);
    return false;
  } else {
    console.log(`✅ User access correctly denied: ${userResult.error}`);
    return true;
  }
}

async function testApprovalWorkflow(transactionId) {
  if (!transactionId) {
    console.log('\n⚠️ Step 3: Skipping approval workflow (no transaction ID)');
    return false;
  }
  
  console.log('\n🔍 Step 3: Testing Approval Workflow');
  console.log('====================================');
  
  // Test approval
  const approvalResult = await apiCall('PUT', `/transactions/${transactionId}/approve`, {
    reviewerAddress: TEST_CONFIG.reviewerAddress,
    comments: 'Test approval'
  }, TEST_CONFIG.reviewerAddress);
  
  if (approvalResult.success) {
    console.log(`✅ Transaction ${transactionId} approved successfully`);
    console.log(`   Message: ${approvalResult.data.message}`);
    
    // Test completion
    const completionResult = await apiCall('PUT', `/transactions/${transactionId}/complete`, {}, TEST_CONFIG.reviewerAddress);
    
    if (completionResult.success) {
      console.log(`✅ Transaction ${transactionId} completed successfully`);
      console.log(`   Message: ${completionResult.data.message}`);
      return true;
    } else {
      console.log(`❌ Transaction completion failed: ${completionResult.error}`);
      return false;
    }
  } else {
    console.log(`❌ Transaction approval failed: ${approvalResult.error}`);
    return false;
  }
}

async function testFrontendCompatibility() {
  console.log('\n🔍 Step 4: Testing Frontend Compatibility');
  console.log('=========================================');
  
  // Test role endpoint
  const roleResult = await apiCall('GET', `/user/role/${TEST_CONFIG.reviewerAddress}`, null, TEST_CONFIG.reviewerAddress);
  if (roleResult.success) {
    const role = roleResult.data.data?.role;
    console.log(`✅ Role detection: ${role}`);
    
    if (role === 'reviewer' || role === 'admin') {
      console.log(`✅ Frontend should recognize this user as having review permissions`);
      return true;
    } else {
      console.log(`❌ Frontend may not recognize this user as a reviewer`);
      return false;
    }
  } else {
    console.log(`❌ Role detection failed: ${roleResult.error}`);
    return false;
  }
}

async function runFinalWorkflowTest() {
  console.log('🧪 Final Patent Purchase Workflow Test');
  console.log('=======================================');
  console.log('This test validates the complete fix for the transaction workflow issue.');
  console.log('');
  
  try {
    // Step 1: Test role access
    const transactionId = await testRoleAccess();
    
    // Step 2: Test user access denial
    const userAccessDenied = await testUserAccess();
    
    // Step 3: Test approval workflow
    const workflowSuccess = await testApprovalWorkflow(transactionId);
    
    // Step 4: Test frontend compatibility
    const frontendCompatible = await testFrontendCompatibility();
    
    // Summary
    console.log('\n🎯 Test Results Summary');
    console.log('=======================');
    console.log(`✅ Reviewer Access: ${transactionId ? 'PASS' : 'FAIL'}`);
    console.log(`✅ User Access Denied: ${userAccessDenied ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Approval Workflow: ${workflowSuccess ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Frontend Compatible: ${frontendCompatible ? 'PASS' : 'FAIL'}`);
    
    const allTestsPassed = transactionId && userAccessDenied && workflowSuccess && frontendCompatible;
    
    if (allTestsPassed) {
      console.log('\n🎉 ALL TESTS PASSED!');
      console.log('====================');
      console.log('✅ The transaction workflow fix is working correctly');
      console.log('✅ Reviewers can access pending transactions');
      console.log('✅ Users are properly denied access');
      console.log('✅ Approval and completion are separate steps');
      console.log('✅ Frontend role detection should work');
      console.log('');
      console.log('📋 Next Steps:');
      console.log('1. Connect to the frontend with a reviewer account');
      console.log('2. Navigate to Review → Trading Review');
      console.log('3. You should see pending transactions for review');
      console.log('4. Test the approve/reject functionality');
    } else {
      console.log('\n❌ SOME TESTS FAILED');
      console.log('====================');
      console.log('Please check the individual test results above for details.');
    }
    
  } catch (error) {
    console.error('\n❌ Test execution failed:', error.message);
  }
}

// Run the test
if (require.main === module) {
  runFinalWorkflowTest();
}

module.exports = {
  runFinalWorkflowTest,
  testRoleAccess,
  testUserAccess,
  testApprovalWorkflow,
  testFrontendCompatibility
};
