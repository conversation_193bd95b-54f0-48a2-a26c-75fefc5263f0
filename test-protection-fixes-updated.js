const axios = require('axios');

// Configuration
const BACKEND_URL = 'http://localhost:3000';
const TEST_USER_ADDRESS = '0x164D435789d02dbE8317f48017D61663C1CE369B';

async function testProtectionCasesFix() {
  console.log('🧪 Testing Protection Cases Fix...\n');

  try {
    console.log('📋 Testing /api/protection/my-cases endpoint...');
    const response = await axios.get(`${BACKEND_URL}/api/protection/my-cases`, {
      headers: {
        'X-User-Address': TEST_USER_ADDRESS
      }
    });

    console.log('✅ Response Status:', response.status);
    console.log('✅ Response Data:', JSON.stringify(response.data, null, 2));

    if (response.data.success) {
      const cases = response.data.data;
      console.log(`\n🎉 Success! Found ${cases.length} protection cases for user`);
      if (cases.length === 0) {
        console.log('✅ Empty state handled correctly - no more "Invalid case ID" error!');
      }
    } else {
      console.log('❌ Request failed:', response.data.message);
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

async function testPatentSearchFix() {
  console.log('\n🔍 Testing Patent Search Fix...\n');

  try {
    // Test 1: Search with single character (should work now)
    console.log('📋 Testing search with single character "2"...');
    const response1 = await axios.get(`${BACKEND_URL}/api/patents/search`, {
      params: {
        name: '2'
      }
    });

    console.log('✅ Single character search - Status:', response1.status);
    console.log('✅ Single character search successful!');

    // Test 2: Search with number "2"
    console.log('\n📋 Testing search with number "2"...');
    const response2 = await axios.get(`${BACKEND_URL}/api/patents/search`, {
      params: {
        number: '2'
      }
    });

    console.log('✅ Number search - Status:', response2.status);
    console.log('✅ Number search successful!');

    // Test 3: Search existing patent by number
    console.log('\n📋 Testing search for existing patent by number...');
    const response3 = await axios.get(`${BACKEND_URL}/api/patents/search`, {
      params: {
        number: 'CN1234567890'
      }
    });

    console.log('✅ Existing patent search - Status:', response3.status);
    if (response3.data.data.patents.length > 0) {
      console.log('✅ Found existing patent:', response3.data.data.patents[0].name);
      console.log('✅ Patent search working correctly!');
    }

    console.log('\n🎉 Success! No more "Validation failed" error!');

  } catch (error) {
    console.error('❌ Search Error:', error.response?.data || error.message);
  }
}

async function testPatentSearchForProtection() {
  console.log('\n🔧 Testing Patent Search for Protection Form...\n');

  try {
    // Test the specific search pattern used in protection form
    console.log('📋 Testing protection form search pattern...');
    const searchParams = {
      name: 'CN1234567890',
      number: 'CN1234567890'
    };

    const response = await axios.get(`${BACKEND_URL}/api/patents/search`, {
      params: searchParams
    });

    console.log('✅ Protection form search - Status:', response.status);
    console.log('✅ Search result:', JSON.stringify(response.data.data, null, 2));

    if (response.data.data.patents.length > 0) {
      const patent = response.data.data.patents[0];
      console.log('✅ Patent found for protection form:');
      console.log(`   - ID: ${patent.id}`);
      console.log(`   - Name: ${patent.name}`);
      console.log(`   - Number: ${patent.number}`);
      console.log(`   - Owner: ${patent.uploaderName}`);
      console.log('✅ Protection form should be able to select this patent!');
    } else {
      console.log('⚠️ No patents found - this is expected for some search terms');
    }

  } catch (error) {
    console.error('❌ Protection Search Error:', error.response?.data || error.message);
  }
}

async function runTests() {
  console.log('🚀 Running Updated Protection View Fix Tests...\n');
  console.log('=' * 60);
  
  await testProtectionCasesFix();
  await testPatentSearchFix();
  await testPatentSearchForProtection();
  
  console.log('\n' + '=' * 60);
  console.log('✅ All tests completed!');
  console.log('\n📋 Summary:');
  console.log('1. ✅ Protection cases endpoint now handles empty state correctly');
  console.log('2. ✅ Patent search accepts single characters and simple inputs');
  console.log('3. ✅ Search form should work without validation errors');
  console.log('\n🎯 Next steps:');
  console.log('1. Test the frontend protection form manually');
  console.log('2. Try searching for "CN1234567890" in the protection form');
  console.log('3. Verify the form can be submitted after selecting a patent');
}

runTests().catch(console.error); 