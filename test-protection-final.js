const axios = require('axios');

// Configuration
const BACKEND_URL = 'http://localhost:3000';
const TEST_USER_ADDRESS = '0x164D435789d02dbE8317f48017D61663C1CE369B';

async function testProtectionPageFixes() {
  console.log('🚀 Testing Patent Protection Page Fixes...\n');
  console.log('=' * 60);

  // Test 1: Protection cases endpoint
  console.log('📋 Test 1: Protection Cases Endpoint');
  try {
    const response = await axios.get(`${BACKEND_URL}/api/protection/my-cases`, {
      headers: {
        'X-User-Address': TEST_USER_ADDRESS
      }
    });

    if (response.status === 200 && response.data.success) {
      console.log('✅ Protection cases endpoint working correctly');
      console.log(`   - Status: ${response.status}`);
      console.log(`   - Cases found: ${response.data.data.data?.length || 0}`);
      console.log('   - No more "Invalid case ID" error!');
    }
  } catch (error) {
    console.error('❌ Protection cases test failed:', error.response?.data || error.message);
  }

  // Test 2: Patent search by ID
  console.log('\n🔍 Test 2: Patent Search by ID');
  try {
    const response = await axios.get(`${BACKEND_URL}/api/patents/search`, {
      params: { name: '2', number: '2' }
    });

    if (response.status === 200 && response.data.success) {
      const patents = response.data.data.patents;
      console.log('✅ Patent search by ID working correctly');
      console.log(`   - Status: ${response.status}`);
      console.log(`   - Patents found: ${patents.length}`);
      
      if (patents.length > 0) {
        const patent = patents[0];
        console.log(`   - Found patent: ID=${patent.id}, Name="${patent.name}"`);
        console.log('   - Frontend should be able to select this patent!');
      }
    }
  } catch (error) {
    console.error('❌ Patent search test failed:', error.response?.data || error.message);
  }

  // Test 3: Patent search validation
  console.log('\n📝 Test 3: Search Validation');
  try {
    // Test single character search
    const response1 = await axios.get(`${BACKEND_URL}/api/patents/search`, {
      params: { name: 'a' }
    });
    
    // Test number search
    const response2 = await axios.get(`${BACKEND_URL}/api/patents/search`, {
      params: { number: '1' }
    });

    console.log('✅ Search validation working correctly');
    console.log(`   - Single character search: ${response1.status}`);
    console.log(`   - Number search: ${response2.status}`);
    console.log('   - No more "Validation failed" errors!');
  } catch (error) {
    console.error('❌ Search validation test failed:', error.response?.data || error.message);
  }

  console.log('\n' + '=' * 60);
  console.log('🎯 Frontend Testing Instructions:');
  console.log('1. Open http://localhost:5174 in your browser');
  console.log('2. Connect your wallet');
  console.log('3. Navigate to "专利维权" page');
  console.log('4. Click on "发起维权" tab');
  console.log('5. In the "专利信息" input field, type "2"');
  console.log('6. Click the "搜索" button');
  console.log('7. You should see the patent "测试外观专利" selected');
  console.log('8. Fill in "维权描述" and upload a file');
  console.log('9. The "提交维权申请" button should be enabled');
  console.log('10. No red validation errors should appear!');

  console.log('\n✅ All backend tests passed! Frontend should now work correctly.');
}

testProtectionPageFixes().catch(console.error); 